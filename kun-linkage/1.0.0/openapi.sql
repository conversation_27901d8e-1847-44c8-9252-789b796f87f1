create table kl_openapi_role
(
    id          varchar(64)  not null comment '主键ID' primary key,
    role_code   varchar(64)  not null comment '角色编码',
    role_name   varchar(64)  not null comment '角色名称',
    description varchar(256) null comment '角色描述',
    create_date datetime              default current_timestamp comment '创建时间',
    update_date datetime              default current_timestamp on update current_timestamp comment '更新时间',
    is_deleted  tinyint(1)   not null default 0 comment '是否删除，0-否，1-是'
) comment 'KL开放平台角色表';
create table kl_openapi_permission
(
    id              varchar(64)  not null comment '主键ID' primary key,
    permission_code varchar(64)  not null comment '权限编码',
    permission_name varchar(64)  not null comment '权限名称',
    description     varchar(256) null comment '权限描述',
    create_date     datetime              default current_timestamp comment '创建时间',
    update_date     datetime              default current_timestamp on update current_timestamp comment '更新时间',
    is_deleted      tinyint(1)   not null default 0 comment '是否删除，0-否，1-是'
) comment 'KL开放平台权限表';
create table kl_openapi_role_permission
(
    id            varchar(64) not null comment '主键ID' primary key,
    role_id       varchar(64) not null comment '角色ID',
    permission_id varchar(64) not null comment '权限ID',
    create_date   datetime             default current_timestamp comment '创建时间',
    update_date   datetime             default current_timestamp on update current_timestamp comment '更新时间',
    is_deleted    tinyint(1)  not null default 0 comment '是否删除，0-否，1-是'
) comment 'KL开放平台角色权限关联表';
create table kl_openapi_orgnization_role
(
    id              varchar(64) not null comment '主键ID' primary key,
    role_id         varchar(64) not null comment '角色ID',
    organization_no varchar(64) not null comment '组织机构号',
    create_date     datetime             default current_timestamp comment '创建时间',
    update_date     datetime             default current_timestamp on update current_timestamp comment '更新时间',
    is_deleted      tinyint(1)  not null default 0 comment '是否删除，0-否，1-是'
) comment 'KL开放平台角色机构关联表';
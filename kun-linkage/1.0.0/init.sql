create table kl_auth_flow_202505(
                                    id varchar(64) not null primary key comment '交易ID',
                                    processor varchar(64) default null comment 'Processor',
                                    processor_request_id varchar(64) default null comment '请求ID',
                                    processor_trans_id varchar(64) default null comment 'processor交易ID',
                                    original_processor_trans_id varchar(64) default null comment 'processor原交易ID',
                                    merchant_no varchar(64) default null comment '商户号',
                                    merchant_name varchar(256) default null comment '商户名称',
                                    customer_id varchar(64) null comment '客户ID',
                                    status varchar(8) default null comment '状态,如：PENDING:处理中;SUCCESS:成功;FAIL:失败;',
                                    mti varchar(4) default null comment 'MTI',
                                    processing_code varchar(6) default null comment 'Processing code',
                                    systems_trace_audit_number varchar(32) default null comment '系统跟踪审计号',
                                    gateway_card_id varchar(64) default null comment '网关卡id',
                                    processor_card_id varchar(64) default null comment '通道卡id',
                                    issuer_card_id varchar(64) default null comment '发卡方卡id',
                                    masked_card_no varchar(128) default null comment '脱敏卡号',
                                    trans_type varchar(32) default null comment '交易类型',
                                    card_product_code varchar(32) default null comment '卡产品编号',
                                    trans_currency varchar(3) default null comment '交易币种',
                                    trans_currency_exponent int(4) null comment '交易币种的小数位数',
                                    trans_amount decimal(18,3) default null comment '交易金额',
                                    trans_fee decimal(18,3) default null comment '交易手续费',
                                    cardholder_billing_currency varchar(3) default null comment '持卡人账单币种',
                                    cardholder_currency_exponent int(4) null comment '持卡人账单币种的小数位数',
                                    cardholder_billing_amount decimal(18,3) default null comment '持卡人账单金额(不含markup)',
                                    cardholder_markup_billing_amount decimal(18, 3) null comment '持卡人账单金额(含markup)',
                                    markup_rate decimal(18,4) default 0 comment 'markup费率',
                                    markup_amount decimal(18,3) default null comment 'markup金额',
                                    pos_entry_mode varchar(12) default null comment 'POS输入方式',
                                    point_pin_code varchar(2) default null comment 'PIN码输入方式',
                                    pos_condition_code varchar(2) default null comment 'POS条件代码',
                                    transaction_local_datetime varchar(16) null comment '交易发生地时间',
                                    conversion_rate_cardholder_billing decimal(8,7) null comment '持卡人账单币种与交易币种的汇率',
                                    approve_code varchar(6) default null comment '授权码',
                                    acquire_reference_no varchar(32) default null comment '收单参考号',
                                    card_acceptor_name varchar(40) default null comment '收单商户名称',
                                    card_acceptor_id varchar(64) default null comment '收单商户号',
                                    card_acceptor_tid varchar(16) default null comment '收单商户终端号',
                                    card_acceptor_country_code varchar(3) default null comment '收单商户国家代码',
                                    card_acceptor_city varchar(16) default null comment '收单商户城市',
                                    mcc varchar(4) default null comment 'MCC',
                                    processor_ext1            varchar(32) default null comment 'processor扩展字段1',
                                    remaining_trans_amount decimal(18,3) default null comment '剩余交易金额',
                                    remaining_billing_amount decimal(18, 3) default 0 comment '剩余持卡人账单金额(不含markup)',
                                    remaining_markup_billing_amount decimal(18, 3) default 0 comment '剩余持卡人账单金额(含markup)',
                                    response_code varchar(32) default null comment '响应码',
                                    response_msg varchar(128) default null comment '响应信息',
                                    original_id varchar(64) default null comment '原KL交易ID',
                                    original_processor_request_id varchar(64) default null comment '原交易processor请求ID',
                                    original_trans_time varchar(14) default null comment '原交易时间',
                                    clear_flag varchar(2) default null comment '清分标记',
                                    release_flag varchar(4) null comment '释放标记, L:未施放;R:已释放;NONE:无需释放',
                                    release_time datetime null comment '释放时间',
                                    trans_accounting_date varchar(8) default null comment '交易会计日',
                                    clear_accounting_date varchar(8) default null comment '清分会计日',
                                    trans_done_time datetime default null comment '交易完成时间',
                                    clear_amount decimal(18, 3) default 0 comment '清算金额',
                                    clear_bill_amount decimal(18, 3) default 0 comment '清算账单金额(不含markup)',
                                    clear_bill_amount_with_markup decimal(20, 3) default 0 comment '清算账单金额(含markup)',
                                    release_trans_amount   decimal(18, 3) default 0 comment '释放交易金额',
                                    release_markup_billing_amount decimal(18, 3) default 0 comment '释放持卡人账单金额(含markup)',
                                    create_time datetime default now() comment '创建时间',
                                    update_time datetime default null comment '更新时间'
) comment 'Kunlinkage授权流水表';
CREATE TABLE IF NOT EXISTS kl_auth_flow_202506 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202507 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202508 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202509 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202510 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202511 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202512 LIKE kl_auth_flow_202505;
-- 2026年授权流水表
CREATE TABLE IF NOT EXISTS kl_auth_flow_202601 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202602 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202603 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202604 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202605 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202606 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202607 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202608 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202609 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202610 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202611 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202612 LIKE kl_auth_flow_202505;
-- 2027年授权流水表
CREATE TABLE IF NOT EXISTS kl_auth_flow_202701 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202702 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202703 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202704 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202705 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202706 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202707 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202708 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202709 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202710 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202711 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202712 LIKE kl_auth_flow_202505;
-- 2028年授权流水表
CREATE TABLE IF NOT EXISTS kl_auth_flow_202801 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202802 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202803 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202804 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202805 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202806 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202807 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202808 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202809 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202810 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202811 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202812 LIKE kl_auth_flow_202505;
-- 2029年授权流水表
CREATE TABLE IF NOT EXISTS kl_auth_flow_202901 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202902 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202903 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202904 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202905 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202906 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202907 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202908 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202909 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202910 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202911 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_202912 LIKE kl_auth_flow_202505;
-- 2030年授权流水表
CREATE TABLE IF NOT EXISTS kl_auth_flow_203001 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_203002 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_203003 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_203004 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_203005 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_203006 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_203007 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_203008 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_203009 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_203010 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_203011 LIKE kl_auth_flow_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_203012 LIKE kl_auth_flow_202505;

-- 授权记录请求日志表
CREATE TABLE kl_auth_request_log (
                                     auth_flow_id bigint NOT NULL COMMENT '授权流水ID',
                                     request_json text DEFAULT NULL COMMENT '请求JSON',
                                     create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     PRIMARY KEY (auth_flow_id, create_time)  -- 复合主键包含分区字段
) COMMENT '授权请求日志表'
PARTITION BY RANGE (TO_DAYS(create_time)) (
    -- 2025年分区 (从5月开始)
    PARTITION p202505 VALUES LESS THAN (TO_DAYS('2025-06-01')),
    PARTITION p202506 VALUES LESS THAN (TO_DAYS('2025-07-01')),
    PARTITION p202507 VALUES LESS THAN (TO_DAYS('2025-08-01')),
    PARTITION p202508 VALUES LESS THAN (TO_DAYS('2025-09-01')),
    PARTITION p202509 VALUES LESS THAN (TO_DAYS('2025-10-01')),
    PARTITION p202510 VALUES LESS THAN (TO_DAYS('2025-11-01')),
    PARTITION p202511 VALUES LESS THAN (TO_DAYS('2025-12-01')),
    PARTITION p202512 VALUES LESS THAN (TO_DAYS('2026-01-01')),

    -- 2026年分区
    PARTITION p202601 VALUES LESS THAN (TO_DAYS('2026-02-01')),
    PARTITION p202602 VALUES LESS THAN (TO_DAYS('2026-03-01')),
    PARTITION p202603 VALUES LESS THAN (TO_DAYS('2026-04-01')),
    PARTITION p202604 VALUES LESS THAN (TO_DAYS('2026-05-01')),
    PARTITION p202605 VALUES LESS THAN (TO_DAYS('2026-06-01')),
    PARTITION p202606 VALUES LESS THAN (TO_DAYS('2026-07-01')),
    PARTITION p202607 VALUES LESS THAN (TO_DAYS('2026-08-01')),
    PARTITION p202608 VALUES LESS THAN (TO_DAYS('2026-09-01')),
    PARTITION p202609 VALUES LESS THAN (TO_DAYS('2026-10-01')),
    PARTITION p202610 VALUES LESS THAN (TO_DAYS('2026-11-01')),
    PARTITION p202611 VALUES LESS THAN (TO_DAYS('2026-12-01')),
    PARTITION p202612 VALUES LESS THAN (TO_DAYS('2027-01-01')),

    -- 2027年分区
    PARTITION p202701 VALUES LESS THAN (TO_DAYS('2027-02-01')),
    PARTITION p202702 VALUES LESS THAN (TO_DAYS('2027-03-01')),
    PARTITION p202703 VALUES LESS THAN (TO_DAYS('2027-04-01')),
    PARTITION p202704 VALUES LESS THAN (TO_DAYS('2027-05-01')),
    PARTITION p202705 VALUES LESS THAN (TO_DAYS('2027-06-01')),
    PARTITION p202706 VALUES LESS THAN (TO_DAYS('2027-07-01')),
    PARTITION p202707 VALUES LESS THAN (TO_DAYS('2027-08-01')),
    PARTITION p202708 VALUES LESS THAN (TO_DAYS('2027-09-01')),
    PARTITION p202709 VALUES LESS THAN (TO_DAYS('2027-10-01')),
    PARTITION p202710 VALUES LESS THAN (TO_DAYS('2027-11-01')),
    PARTITION p202711 VALUES LESS THAN (TO_DAYS('2027-12-01')),
    PARTITION p202712 VALUES LESS THAN (TO_DAYS('2028-01-01')),

    -- 2028年分区
    PARTITION p202801 VALUES LESS THAN (TO_DAYS('2028-02-01')),
    PARTITION p202802 VALUES LESS THAN (TO_DAYS('2028-03-01')),
    PARTITION p202803 VALUES LESS THAN (TO_DAYS('2028-04-01')),
    PARTITION p202804 VALUES LESS THAN (TO_DAYS('2028-05-01')),
    PARTITION p202805 VALUES LESS THAN (TO_DAYS('2028-06-01')),
    PARTITION p202806 VALUES LESS THAN (TO_DAYS('2028-07-01')),
    PARTITION p202807 VALUES LESS THAN (TO_DAYS('2028-08-01')),
    PARTITION p202808 VALUES LESS THAN (TO_DAYS('2028-09-01')),
    PARTITION p202809 VALUES LESS THAN (TO_DAYS('2028-10-01')),
    PARTITION p202810 VALUES LESS THAN (TO_DAYS('2028-11-01')),
    PARTITION p202811 VALUES LESS THAN (TO_DAYS('2028-12-01')),
    PARTITION p202812 VALUES LESS THAN (TO_DAYS('2029-01-01')),

    -- 2029年分区
    PARTITION p202901 VALUES LESS THAN (TO_DAYS('2029-02-01')),
    PARTITION p202902 VALUES LESS THAN (TO_DAYS('2029-03-01')),
    PARTITION p202903 VALUES LESS THAN (TO_DAYS('2029-04-01')),
    PARTITION p202904 VALUES LESS THAN (TO_DAYS('2029-05-01')),
    PARTITION p202905 VALUES LESS THAN (TO_DAYS('2029-06-01')),
    PARTITION p202906 VALUES LESS THAN (TO_DAYS('2029-07-01')),
    PARTITION p202907 VALUES LESS THAN (TO_DAYS('2029-08-01')),
    PARTITION p202908 VALUES LESS THAN (TO_DAYS('2029-09-01')),
    PARTITION p202909 VALUES LESS THAN (TO_DAYS('2029-10-01')),
    PARTITION p202910 VALUES LESS THAN (TO_DAYS('2029-11-01')),
    PARTITION p202911 VALUES LESS THAN (TO_DAYS('2029-12-01')),
    PARTITION p202912 VALUES LESS THAN (TO_DAYS('2030-01-01')),

    -- 2030年分区
    PARTITION p203001 VALUES LESS THAN (TO_DAYS('2030-02-01')),
    PARTITION p203002 VALUES LESS THAN (TO_DAYS('2030-03-01')),
    PARTITION p203003 VALUES LESS THAN (TO_DAYS('2030-04-01')),
    PARTITION p203004 VALUES LESS THAN (TO_DAYS('2030-05-01')),
    PARTITION p203005 VALUES LESS THAN (TO_DAYS('2030-06-01')),
    PARTITION p203006 VALUES LESS THAN (TO_DAYS('2030-07-01')),
    PARTITION p203007 VALUES LESS THAN (TO_DAYS('2030-08-01')),
    PARTITION p203008 VALUES LESS THAN (TO_DAYS('2030-09-01')),
    PARTITION p203009 VALUES LESS THAN (TO_DAYS('2030-10-01')),
    PARTITION p203010 VALUES LESS THAN (TO_DAYS('2030-11-01')),
    PARTITION p203011 VALUES LESS THAN (TO_DAYS('2030-12-01')),
    PARTITION p203012 VALUES LESS THAN (TO_DAYS('2031-01-01')),

    -- 默认分区，用于存储超出2030年的数据
    PARTITION p_default VALUES LESS THAN MAXVALUE
);

-- 授权流水扩展表
create table kl_auth_flow_ext_202505(
                                        auth_flow_id varchar(64) not null primary key comment '交易ID',
                                        merchant_account_flag tinyint(1) not null default 0 comment '商户账户标志，0-不检查商户账户，1-检查商户账户',
                                        merchant_account_request_no varchar(64) default null comment '商户账户请求号',
                                        merchant_account_reversal_no   varchar(64) null comment '商户账户冲回请求号',
                                        cardholder_account_flag tinyint(1) not null default 0 comment '持卡人账户标志，0-不检查持卡人账户，1-检查持卡人账户',
                                        cardholder_account_request_no varchar(64) default null comment '持卡人账户请求号',
                                        cardholder_account_reversal_no varchar(64) null comment '持卡人账户冲回请求号',
                                        kun_account_flag varchar(2) default null comment 'Kun账户标志',
                                        kun_mid                        varchar(32) null comment 'kun对应的商户号',
                                        kun_account_request_no varchar(64) default null comment 'Kun账户请求号',
                                        kun_account_reversal_no        varchar(64) null comment 'Kun账户冲回请求号',
                                        payx_account_flag varchar(2) default null comment 'Kun账户标志',
                                        payx_mid                       varchar(32) null comment 'payx对应的商户号',
                                        payx_account_request_no varchar(64) default null comment 'payx账户请求号',
                                        payx_account_reversal_no       varchar(64) null comment 'payx账户冲回请求号',
                                        third_party_authorization_flag tinyint     null comment '第三方授权标记',
                                        mpc_tenant_id                  varchar(64) null comment 'MPC租户ID',
                                        mpc_group_code                 varchar(64) null comment 'MPC集团号',
                                        create_time datetime default now() comment '创建时间',
                                        update_time datetime default null comment '更新时间'
) comment 'Kunlinkage授权流水扩展表';

CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202506 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202507 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202508 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202509 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202510 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202511 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202512 LIKE kl_auth_flow_ext_202505;
-- 2026年授权流水扩展表
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202601 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202602 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202603 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202604 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202605 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202606 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202607 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202608 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202609 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202610 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202611 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202612 LIKE kl_auth_flow_ext_202505;
-- 2027年授权流水扩展表
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202701 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202702 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202703 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202704 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202705 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202706 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202707 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202708 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202709 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202710 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202711 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202712 LIKE kl_auth_flow_ext_202505;
-- 2028年授权流水扩展表
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202801 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202802 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202803 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202804 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202805 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202806 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202807 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202808 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202809 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202810 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202811 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202812 LIKE kl_auth_flow_ext_202505;
-- 2029年授权流水扩展表
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202901 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202902 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202903 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202904 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202905 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202906 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202907 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202908 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202909 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202910 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202911 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_202912 LIKE kl_auth_flow_ext_202505;
-- 2030年授权流水扩展表
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_203001 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_203002 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_203003 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_203004 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_203005 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_203006 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_203007 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_203008 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_203009 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_203010 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_203011 LIKE kl_auth_flow_ext_202505;
CREATE TABLE IF NOT EXISTS kl_auth_flow_ext_203012 LIKE kl_auth_flow_ext_202505;





-- 业务账户属性配置审核记录表 --
CREATE TABLE `kl_business_account_attribute` (
                                                 `id` bigint NOT NULL COMMENT '主键id',
                                                 `belong` tinyint DEFAULT NULL COMMENT '1:organization;2:Customer of Organization',
                                                 `type` varchar(16) DEFAULT NULL COMMENT '预留',
                                                 `direction` varchar(8) DEFAULT NULL COMMENT '方向;C: Credit;D: Debit',
                                                 `topup_flag` tinyint DEFAULT NULL COMMENT '0:not allow;1: allow',
                                                 `withdrawal_flag` tinyint DEFAULT NULL COMMENT '0:not allow;1: allow',
                                                 `transfer_out_flag` tinyint DEFAULT NULL COMMENT '0:not allow;1: allow',
                                                 `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                 `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                                 `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建人名称',
                                                 `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                                 `last_modify_user_id` varchar(32) DEFAULT NULL COMMENT '最后一次修改人id',
                                                 `last_modify_user_name` varchar(128) DEFAULT NULL COMMENT '最后一次修改人名称',
                                                 PRIMARY KEY (`id`)
) COMMENT='业务账户属性配置表';

-- 业务账户属性配置审核记录表 --
CREATE TABLE `kl_business_account_attribute_review_record` (
                                                               `review_id` bigint NOT NULL COMMENT '审核id',
                                                               `operator_type` varchar(8) NOT NULL COMMENT '操作类型:Add,Modify',
                                                               `business_account_attribute_id` bigint DEFAULT NULL COMMENT '业务账户属性配置表id,修改时有值',
                                                               `belong` tinyint DEFAULT NULL COMMENT '1:organization;2:Customer of Organization',
                                                               `type` varchar(16) DEFAULT NULL COMMENT '预留',
                                                               `direction` varchar(8) DEFAULT NULL COMMENT '方向;C: Credit;D: Debit',
                                                               `topup_flag` tinyint DEFAULT NULL COMMENT '0:not allow;1: allow',
                                                               `withdrawal_flag` tinyint DEFAULT NULL COMMENT '0:not allow;1: allow',
                                                               `transfer_out_flag` tinyint DEFAULT NULL COMMENT '0:not allow;1: allow',
                                                               `review_status` varchar(16) DEFAULT NULL COMMENT '审核状态',
                                                               `review_Reason` varchar(512) DEFAULT NULL COMMENT '审核备注',
                                                               `submit_time` datetime NULL DEFAULT NULL COMMENT '提交时间',
                                                               `submit_user_id` varchar(32) DEFAULT NULL COMMENT '提交人id',
                                                               `submit_user_name` varchar(128) DEFAULT NULL COMMENT '提交人名称',
                                                               `review_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
                                                               `review_user_id` varchar(32) DEFAULT NULL COMMENT '审核人id',
                                                               `review_user_name` varchar(128) DEFAULT NULL COMMENT '审核人名称',
                                                               PRIMARY KEY (`review_id`)
) COMMENT='业务账户属性配置审核记录表';

-- 机构卡产品信息表 --
CREATE TABLE `kl_card_product_info` (
                                        `id` bigint NOT NULL COMMENT '主键id',
                                        `card_product_code` varchar(16) DEFAULT NULL COMMENT '卡产品编码',
                                        `processor` varchar(16) DEFAULT NULL COMMENT '卡片处理方',
                                        `card_product_name` varchar(256) DEFAULT NULL COMMENT '卡产品名称',
                                        `card_scheme` varchar(16) DEFAULT NULL COMMENT '卡组',
                                        `card_currency_code` varchar(8) DEFAULT NULL COMMENT '卡片币种',
                                        `bin_range_low` varchar(32) DEFAULT NULL COMMENT 'BIN起始',
                                        `bin_range_high` varchar(32) DEFAULT NULL COMMENT 'BIN结束',
                                        `special_usage` varchar(512) DEFAULT NULL COMMENT '特殊用法',
                                        `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                        `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                        `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                        `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建人名称',
                                        `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                        `last_modify_user_id` varchar(32) DEFAULT NULL COMMENT '最后一次修改人id',
                                        `last_modify_user_name` varchar(128) DEFAULT NULL COMMENT '最后一次修改人名称',
                                        PRIMARY KEY (`id`),
                                        KEY idx_cpc(`card_product_code`)
) COMMENT='卡产品信息表';

-- 机构卡产品信息审核记录表 --
CREATE TABLE `kl_card_product_info_review_record` (
                                                      `review_id` bigint NOT NULL COMMENT '审核id',
                                                      `operator_type` varchar(8) NOT NULL COMMENT '操作类型:Add,Modify',
                                                      `card_product_info_id` bigint DEFAULT NULL COMMENT '卡产品信息表对应id,修改时有值',
                                                      `card_product_code` varchar(16) DEFAULT NULL COMMENT '卡产品编码',
                                                      `processor` varchar(16) DEFAULT NULL COMMENT '卡片处理方',
                                                      `card_product_name` varchar(256) DEFAULT NULL COMMENT '卡产品名称',
                                                      `card_scheme` varchar(16) DEFAULT NULL COMMENT '卡组',
                                                      `card_currency_code` varchar(8) DEFAULT NULL COMMENT '卡片币种',
                                                      `bin_range_low` varchar(32) DEFAULT NULL COMMENT 'BIN起始',
                                                      `bin_range_high` varchar(32) DEFAULT NULL COMMENT 'BIN结束',
                                                      `special_usage` varchar(512) DEFAULT NULL COMMENT '特殊用法',
                                                      `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                                      `review_status` varchar(16) DEFAULT NULL COMMENT '审核状态',
                                                      `review_Reason` varchar(512) DEFAULT NULL COMMENT '审核备注',
                                                      `submit_time` datetime NULL DEFAULT NULL COMMENT '提交时间',
                                                      `submit_user_id` varchar(32) DEFAULT NULL COMMENT '提交人id',
                                                      `submit_user_name` varchar(128) DEFAULT NULL COMMENT '提交人名称',
                                                      `review_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
                                                      `review_user_id` varchar(32) DEFAULT NULL COMMENT '审核人id',
                                                      `review_user_name` varchar(128) DEFAULT NULL COMMENT '审核人名称',
                                                      PRIMARY KEY (`review_id`),
                                                      KEY idx_cpc(`card_product_code`)
) COMMENT='卡产品信息审核记录表';


-- 机构账户信息表 --
CREATE TABLE `kl_organization_account_info` (
                                                `id` bigint NOT NULL COMMENT '主键id',
                                                `organization_no` varchar(16) DEFAULT NULL COMMENT '机构编号',
                                                `account_type` varchar(8) DEFAULT NULL COMMENT '账户类型001:法币基本账户/002:数币基本账户/003:信用法币账户',
                                                `currency_code` varchar(8) DEFAULT NULL COMMENT '币种字母码',
                                                `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                                `minimum_amount` decimal(30,16) DEFAULT '0' COMMENT '最小余额',
                                                `network` varchar(16) DEFAULT NULL COMMENT '所属方',
                                                `account_no` varchar(32) DEFAULT NULL COMMENT '账户唯一标识',
                                                `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                                `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建人名称',
                                                `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                                `last_modify_user_id` varchar(32) DEFAULT NULL COMMENT '最后一次修改人id',
                                                `last_modify_user_name` varchar(128) DEFAULT NULL COMMENT '最后一次修改人名称',
                                                PRIMARY KEY (`id`),
                                                KEY idx_on_at_cc(`organization_no`,`account_type`,`currency_code`)
) COMMENT='机构账户信息表';


-- 机构账户信息审核记录表 --
CREATE TABLE `kl_organization_account_info_review_record` (
                                                              `review_id` bigint NOT NULL COMMENT '审核id',
                                                              `operator_type` varchar(8) NOT NULL COMMENT '操作类型:Add,Modify',
                                                              `organization_account_info_id` bigint DEFAULT NULL COMMENT '机构账户信息表id,修改时有值',
                                                              `organization_no` varchar(16) DEFAULT NULL COMMENT '机构编号',
                                                              `account_type` varchar(8) DEFAULT NULL COMMENT '账户类型001:法币基本账户/002:数币基本账户/003:信用法币账户',
                                                              `currency_code` varchar(8) DEFAULT NULL COMMENT '币种字母码',
                                                              `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                                              `minimum_amount` decimal(30,16) DEFAULT '0' COMMENT '最小余额',
                                                              `network` varchar(16) DEFAULT NULL COMMENT '所属方',
                                                              `account_no` varchar(32) DEFAULT NULL COMMENT '账户唯一标识',
                                                              `review_status` varchar(16) DEFAULT NULL COMMENT '审核状态',
                                                              `review_Reason` varchar(512) DEFAULT NULL COMMENT '审核备注',
                                                              `submit_time` datetime NULL DEFAULT NULL COMMENT '提交时间',
                                                              `submit_user_id` varchar(32) DEFAULT NULL COMMENT '提交人id',
                                                              `submit_user_name` varchar(128) DEFAULT NULL COMMENT '提交人名称',
                                                              `review_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
                                                              `review_user_id` varchar(32) DEFAULT NULL COMMENT '审核人id',
                                                              `review_user_name` varchar(128) DEFAULT NULL COMMENT '审核人名称',
                                                              PRIMARY KEY (`review_id`),
                                                              KEY idx_on_at_cc(`organization_no`,`account_type`,`currency_code`)
) COMMENT='机构账户信息审核记录表';

-- 机构卡产品信息表 --
CREATE TABLE `kl_organization_application_card` (
                                                    `id` bigint NOT NULL COMMENT '主键id',
                                                    `organization_no` varchar(16) DEFAULT NULL COMMENT '机构编号',
                                                    `card_product_code` varchar(32) DEFAULT NULL COMMENT '卡产品',
                                                    `processor` varchar(16) DEFAULT NULL COMMENT '卡片处理方',
                                                    `auto_activation_flag` tinyint DEFAULT NULL COMMENT '自动激活标记',
                                                    `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                                    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                    `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                                    `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建人名称',
                                                    `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                                    `last_modify_user_id` varchar(32) DEFAULT NULL COMMENT '最后一次修改人id',
                                                    `last_modify_user_name` varchar(128) DEFAULT NULL COMMENT '最后一次修改人名称',
                                                    PRIMARY KEY (`id`),
                                                    KEY idx_on_cc(`organization_no`,`card_product_code`)
) COMMENT='机构卡产品信息表';

-- 机构卡产品信息审核记录表 --
CREATE TABLE `kl_organization_application_card_review_record` (
                                                                  `review_id` bigint NOT NULL COMMENT '审核id',
                                                                  `operator_type` varchar(8) NOT NULL COMMENT '操作类型:Add,Modify',
                                                                  `organization_application_card_id` bigint DEFAULT NULL COMMENT '机构卡产品信息表id,修改时有值',
                                                                  `organization_no` varchar(16) DEFAULT NULL COMMENT '机构编号',
                                                                  `card_product_code` varchar(32) DEFAULT NULL COMMENT '卡产品',
                                                                  `processor` varchar(16) DEFAULT NULL COMMENT '卡片处理方',
                                                                  `auto_activation_flag` tinyint DEFAULT NULL COMMENT '自动激活标记',
                                                                  `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                                                  `review_status` varchar(16) DEFAULT NULL COMMENT '审核状态',
                                                                  `review_Reason` varchar(512) DEFAULT NULL COMMENT '审核备注',
                                                                  `submit_time` datetime NULL DEFAULT NULL COMMENT '提交时间',
                                                                  `submit_user_id` varchar(32) DEFAULT NULL COMMENT '提交人id',
                                                                  `submit_user_name` varchar(128) DEFAULT NULL COMMENT '提交人名称',
                                                                  `review_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
                                                                  `review_user_id` varchar(32) DEFAULT NULL COMMENT '审核人id',
                                                                  `review_user_name` varchar(128) DEFAULT NULL COMMENT '审核人名称',
                                                                  PRIMARY KEY (`review_id`),
                                                                  KEY idx_on_cc(`organization_no`,`card_product_code`)
) COMMENT='机构卡产品信息审核记录表';

-- 机构基本信息表 --
CREATE TABLE `kl_organization_basic_info` (
                                              `id` bigint NOT NULL COMMENT '主键id',
                                              `organization_no` varchar(16) DEFAULT NULL COMMENT '机构编号',
                                              `organization_name` varchar(256) DEFAULT NULL COMMENT '机构名称',
                                              `business_type` varchar(2) DEFAULT NULL COMMENT '业务类型',
                                              `country_code` varchar(8) DEFAULT NULL COMMENT '国家字母码',
                                              `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                              `key` varchar(256) DEFAULT NULL COMMENT 'key',
                                              `sensitive_key` varchar(256) DEFAULT NULL COMMENT '敏感信息加解密key',
                                              `kun_mid` varchar(32) DEFAULT NULL COMMENT 'kun对应的商户号',
                                              `payx_mid` varchar(32) DEFAULT NULL COMMENT 'payx对应的商户号',
                                              `mode` varchar(8) DEFAULT NULL COMMENT '模式',
                                              `check_organization_account_flag` tinyint DEFAULT NULL COMMENT '是否校验机构账户标记;0否;1是',
                                              `check_customer_account_flag` tinyint DEFAULT NULL COMMENT '是否校验机构下用户账户标记;0否;1是',
                                              `third_party_authorization_flag` tinyint DEFAULT NULL COMMENT '第三方授权标记',
                                              `mpc_tenant_id` varchar(64) DEFAULT NULL COMMENT 'MPC租户ID',
                                              `mpc_group_code` varchar(64) DEFAULT NULL COMMENT 'MPC集团号',
                                              `mpc_token` varchar(64) DEFAULT NULL COMMENT 'MPC TOKEN',
                                              `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                              `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                              `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建人名称',
                                              `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                              `last_modify_user_id` varchar(32) DEFAULT NULL COMMENT '最后一次修改人id',
                                              `last_modify_user_name` varchar(128) DEFAULT NULL COMMENT '最后一次修改人名称',
                                              PRIMARY KEY (`id`),
                                              KEY idx_on(`organization_no`)
) COMMENT='机构基本信息表';

-- 机构基本信息审核记录表 --
CREATE TABLE `kl_organization_basic_info_review_record` (
                                                            `review_id` bigint NOT NULL COMMENT '审核id',
                                                            `operator_type` varchar(8) NOT NULL COMMENT '操作类型:Add,Modify',
                                                            `organization_id` bigint DEFAULT NULL COMMENT '机构表对应id,修改时有值',
                                                            `organization_no` varchar(16) DEFAULT NULL COMMENT '机构编号',
                                                            `organization_name` varchar(256) DEFAULT NULL COMMENT '机构名称',
                                                            `business_type` varchar(2) DEFAULT NULL COMMENT '业务类型',
                                                            `country_code` varchar(8) DEFAULT NULL COMMENT '国家字母码',
                                                            `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                                            `key` varchar(256) DEFAULT NULL COMMENT 'key',
                                                            `sensitive_key` varchar(256) DEFAULT NULL COMMENT '敏感信息加解密key',
                                                            `kun_mid` varchar(32) DEFAULT NULL COMMENT 'kun对应的商户号',
                                                            `payx_mid` varchar(32) DEFAULT NULL COMMENT 'payx对应的商户号',
                                                            `mode` varchar(8) DEFAULT NULL COMMENT '模式',
                                                            `check_organization_account_flag` tinyint DEFAULT NULL COMMENT '是否校验机构账户标记;0否;1是',
                                                            `check_customer_account_flag` tinyint DEFAULT NULL COMMENT '是否校验机构下用户账户标记;0否;1是',
                                                            `third_party_authorization_flag` tinyint DEFAULT NULL COMMENT '第三方授权标记',
                                                            `mpc_tenant_id` varchar(64) DEFAULT NULL COMMENT 'MPC租户ID',
                                                            `mpc_group_code` varchar(64) DEFAULT NULL COMMENT 'MPC集团号',
                                                            `mpc_token` varchar(64) DEFAULT NULL COMMENT 'MPC TOKEN',
                                                            `review_status` varchar(16) DEFAULT NULL COMMENT '审核状态',
                                                            `review_Reason` varchar(512) DEFAULT NULL COMMENT '审核备注',
                                                            `submit_time` datetime NULL DEFAULT NULL COMMENT '提交时间',
                                                            `submit_user_id` varchar(32) DEFAULT NULL COMMENT '提交人id',
                                                            `submit_user_name` varchar(128) DEFAULT NULL COMMENT '提交人名称',
                                                            `review_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
                                                            `review_user_id` varchar(32) DEFAULT NULL COMMENT '审核人id',
                                                            `review_user_name` varchar(128) DEFAULT NULL COMMENT '审核人名称',
                                                            PRIMARY KEY (`review_id`),
                                                            KEY idx_on(`organization_no`)
) COMMENT='机构基本信息表审核记录表';

-- 机构用户账户表 --
CREATE TABLE `kl_organization_customer_account_info_0` (
                                                           `id` bigint NOT NULL COMMENT '主键id',
                                                           `organization_no` varchar(16) DEFAULT NULL COMMENT '机构号',
                                                           `customer_id` varchar(64) DEFAULT NULL COMMENT '客户号',
                                                           `account_type` varchar(8) DEFAULT NULL COMMENT '账户类型001:法币基本账户/002:数币基本账户/003:信用法币账户',
                                                           `currency_code` varchar(8) DEFAULT NULL COMMENT '账户币种',
                                                           `account_no` varchar(64) DEFAULT NULL COMMENT '账户id',
                                                           `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                                           `network` varchar(16) DEFAULT NULL COMMENT '所属方',
                                                           `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                           `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                                           PRIMARY KEY (`id`),
                                                           KEY idx_on_ci_at_cc(`organization_no`,`customer_id`,`account_type`,`currency_code`)
) COMMENT='机构用户账户信息表';
CREATE TABLE `kl_organization_customer_account_info_1` LIKE `kl_organization_customer_account_info_0`;
CREATE TABLE `kl_organization_customer_account_info_2` LIKE `kl_organization_customer_account_info_0`;
CREATE TABLE `kl_organization_customer_account_info_3` LIKE `kl_organization_customer_account_info_0`;
CREATE TABLE `kl_organization_customer_account_info_4` LIKE `kl_organization_customer_account_info_0`;
CREATE TABLE `kl_organization_customer_account_info_5` LIKE `kl_organization_customer_account_info_0`;
CREATE TABLE `kl_organization_customer_account_info_6` LIKE `kl_organization_customer_account_info_0`;
CREATE TABLE `kl_organization_customer_account_info_7` LIKE `kl_organization_customer_account_info_0`;




-- 机构用户基本信息表 --
CREATE TABLE `kl_organization_customer_basic_info` (
                                                       `id` bigint NOT NULL COMMENT '主键id',
                                                       `organization_no` varchar(16) DEFAULT NULL COMMENT '机构号',
                                                       `customer_id` varchar(64) DEFAULT NULL COMMENT '客户号',
                                                       `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                                       `customer_level` int DEFAULT NULL COMMENT '客户等级',
                                                       `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                       `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                                       PRIMARY KEY (`id`),
                                                       KEY idx_on_ci(`organization_no`, `customer_id`)
) COMMENT='机构用户基础表';


-- 机构用户卡信息表 --
CREATE TABLE `kl_organization_customer_card_info_0` (
                                                        `id` bigint NOT NULL COMMENT '主键id',
                                                        `organization_no` varchar(16) DEFAULT NULL COMMENT '机构号',
                                                        `customer_id` varchar(64) DEFAULT NULL COMMENT '客户号',
                                                        `card_scheme` varchar(16) DEFAULT NULL COMMENT '卡组',
                                                        `card_product_code` varchar(16) DEFAULT NULL COMMENT '卡产品编码',
                                                        `card_id` varchar(64) DEFAULT NULL COMMENT '卡id',
                                                        `card_no` varchar(64) DEFAULT NULL COMMENT '加密卡号',
                                                        `masked_card_no` varchar(64) DEFAULT NULL COMMENT '掩码卡号',
                                                        `currency_code` varchar(8) DEFAULT NULL COMMENT '卡币种',
                                                        `processor` varchar(16) DEFAULT NULL COMMENT '卡片处理方',
                                                        `card_status` varchar(8) DEFAULT NULL COMMENT '卡片状态',
                                                        `card_active_status` varchar(16) DEFAULT NULL COMMENT '激活状态',
                                                        `mobile_phone_area` varchar(8) DEFAULT NULL COMMENT '手机区号',
                                                        `mobile_phone` varchar(16) DEFAULT NULL COMMENT '手机号',
                                                        `email` varchar(128) DEFAULT NULL COMMENT '邮箱',
                                                        `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                        `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                                        PRIMARY KEY (`id`),
                                                        KEY idx_on_ci_cpc(`organization_no`,`customer_id`,`card_product_code`),
                                                        KEY idx_on_ci_ci(`organization_no`,`customer_id`,`card_id`)
) COMMENT='机构用户卡信息表';
CREATE TABLE `kl_organization_customer_card_info_1` LIKE `kl_organization_customer_card_info_0`;
CREATE TABLE `kl_organization_customer_card_info_2` LIKE `kl_organization_customer_card_info_0`;
CREATE TABLE `kl_organization_customer_card_info_3` LIKE `kl_organization_customer_card_info_0`;
CREATE TABLE `kl_organization_customer_card_info_4` LIKE `kl_organization_customer_card_info_0`;
CREATE TABLE `kl_organization_customer_card_info_5` LIKE `kl_organization_customer_card_info_0`;
CREATE TABLE `kl_organization_customer_card_info_6` LIKE `kl_organization_customer_card_info_0`;
CREATE TABLE `kl_organization_customer_card_info_7` LIKE `kl_organization_customer_card_info_0`;




-- 机构用户卡操作记录表 --
CREATE TABLE `kl_organization_customer_card_operation_record_0` (
                                                                    `id` bigint NOT NULL COMMENT '主键id',
                                                                    `operation_type` varchar(16) DEFAULT NULL COMMENT '操作类型',
                                                                    `organization_no` varchar(16) DEFAULT NULL COMMENT '机构号',
                                                                    `request_no` varchar(64) DEFAULT NULL COMMENT '请求流水号',
                                                                    `customer_id` varchar(64) DEFAULT NULL COMMENT '客户号',
                                                                    `card_product_code` varchar(16) DEFAULT NULL COMMENT '卡产品编码',
                                                                    `card_id` varchar(64) DEFAULT NULL COMMENT '卡id',
                                                                    `card_no` varchar(64) DEFAULT NULL COMMENT '加密卡号',
                                                                    `masked_card_no` varchar(64) DEFAULT NULL COMMENT '掩码卡号',
                                                                    `operation_status` varchar(16) DEFAULT NULL COMMENT '操作状态;PPROCESSING:处理中;SUCCESS:成功;FAIL:失败',
                                                                    `fail_message` varchar(256) DEFAULT NULL COMMENT '失败信息',
                                                                    `request_params` varchar(1024) DEFAULT NULL COMMENT '原请求参数(json)',
                                                                    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                                    `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                                                    PRIMARY KEY (`id`),
                                                                    KEY `idx_ot_ci_os` (`operation_type`,`card_id`,`operation_status`)
) COMMENT='机构用户卡操作记录表';
CREATE TABLE `kl_organization_customer_card_operation_record_1` LIKE `kl_organization_customer_card_operation_record_0`;
CREATE TABLE `kl_organization_customer_card_operation_record_2` LIKE `kl_organization_customer_card_operation_record_0`;
CREATE TABLE `kl_organization_customer_card_operation_record_3` LIKE `kl_organization_customer_card_operation_record_0`;
CREATE TABLE `kl_organization_customer_card_operation_record_4` LIKE `kl_organization_customer_card_operation_record_0`;
CREATE TABLE `kl_organization_customer_card_operation_record_5` LIKE `kl_organization_customer_card_operation_record_0`;
CREATE TABLE `kl_organization_customer_card_operation_record_6` LIKE `kl_organization_customer_card_operation_record_0`;
CREATE TABLE `kl_organization_customer_card_operation_record_7` LIKE `kl_organization_customer_card_operation_record_0`;


-- 基础账户信息表 --
CREATE TABLE `kl_account_info_0` (
                                     `account_no` varchar(32) NOT NULL COMMENT '账户号',
                                     `business_system` varchar(16) DEFAULT NULL COMMENT '业务系统:KL/VCC',
                                     `account_type` varchar(16) DEFAULT NULL COMMENT '账户类型001:法币基本账户/002:数币基本账户/003:信用法币账户',
                                     `currency_code` varchar(16) DEFAULT NULL COMMENT '币种码',
                                     `currency_precision` int DEFAULT NULL COMMENT '币种精度',
                                     `direction` varchar(1) DEFAULT NULL COMMENT '账户方向C/D',
                                     `status` varchar(8) DEFAULT NULL COMMENT '账户状态',
                                     `minimum_amount` decimal(30,16) DEFAULT 0 COMMENT '最小余额',
                                     `total_balance_amount` decimal(30,16) DEFAULT 0 COMMENT '总余额',
                                     `frozen_amount` decimal(30,16) DEFAULT 0 COMMENT '冻结金额',
                                     `available_amount` decimal(30,16) DEFAULT 0 COMMENT '可用余额',
                                     `business_organization_no` varchar(16) DEFAULT NULL COMMENT '业务机构号(主要用于页面查询使用)',
                                     `business_customer_id` varchar(64) DEFAULT NULL COMMENT '业务客户号(主要用于页面查询使用)',
                                     `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                     `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                     PRIMARY KEY (`account_no`),
                                     KEY idx_bon_an(`business_organization_no`,`account_no`)
) COMMENT='基础账户信息表';
CREATE TABLE `kl_account_info_1` LIKE `kl_account_info_0`;
CREATE TABLE `kl_account_info_2` LIKE `kl_account_info_0`;
CREATE TABLE `kl_account_info_3` LIKE `kl_account_info_0`;
CREATE TABLE `kl_account_info_4` LIKE `kl_account_info_0`;
CREATE TABLE `kl_account_info_5` LIKE `kl_account_info_0`;
CREATE TABLE `kl_account_info_6` LIKE `kl_account_info_0`;
CREATE TABLE `kl_account_info_7` LIKE `kl_account_info_0`;



-- 基础账户信息操作记录表 --
CREATE TABLE `kl_account_info_operation_record_0` (
                                                      `id` bigint NOT NULL COMMENT '主键id',
                                                      `operation_type` varchar(16) DEFAULT NULL COMMENT '操作类型:ADD/MODIFY',
                                                      `business_system` varchar(16) DEFAULT NULL COMMENT '业务系统:KL/VCC',
                                                      `business_organization_no` varchar(16) DEFAULT NULL COMMENT '业务机构号(分表键)',
                                                      `request_no` varchar(64) DEFAULT NULL COMMENT '请求流水号',
                                                      `account_no` varchar(32) DEFAULT NULL COMMENT '账户号',
                                                      `request_params` varchar(1024) DEFAULT NULL COMMENT '请求参数(json)',
                                                      `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                      PRIMARY KEY (`id`),
                                                      KEY idx_bon_an(`business_organization_no`,`account_no`)
) COMMENT='基础账户信息操作记录表';
CREATE TABLE `kl_account_info_operation_record_1` LIKE `kl_account_info_operation_record_0`;
CREATE TABLE `kl_account_info_operation_record_2` LIKE `kl_account_info_operation_record_0`;
CREATE TABLE `kl_account_info_operation_record_3` LIKE `kl_account_info_operation_record_0`;
CREATE TABLE `kl_account_info_operation_record_4` LIKE `kl_account_info_operation_record_0`;
CREATE TABLE `kl_account_info_operation_record_5` LIKE `kl_account_info_operation_record_0`;
CREATE TABLE `kl_account_info_operation_record_6` LIKE `kl_account_info_operation_record_0`;
CREATE TABLE `kl_account_info_operation_record_7` LIKE `kl_account_info_operation_record_0`;


-- 账户交易明细表 --
CREATE TABLE `kl_account_transaction_detail_202506` (
                                                        `bookkeep_no` varchar(32) NOT NULL COMMENT '记账流水号',
                                                        `account_no` varchar(32) DEFAULT NULL COMMENT '账户号',
                                                        `request_no` varchar(64) DEFAULT NULL COMMENT '请求流水号(只用作日志排查,幂等使用业务系统流水号)',
                                                        `business_type` varchar(16) DEFAULT NULL COMMENT '业务类型:AUTH/CLEARING',
                                                        `accounting_action` varchar(32) DEFAULT NULL COMMENT '会计动作',
                                                        `business_system` varchar(16) DEFAULT NULL COMMENT '业务系统:KL/VCC',
                                                        `accounting_date` varchar(8) DEFAULT NULL COMMENT '会计日',
                                                        `bookkeep_datetime` datetime NULL DEFAULT NULL COMMENT '记账日期时间',
                                                        `account_type` varchar(16) DEFAULT NULL COMMENT '账户类型001:法币基本账户/002:数币基本账户/003:信用法币账户',
                                                        `currency_code` varchar(16) DEFAULT NULL COMMENT '币种码',
                                                        `currency_precision` int DEFAULT NULL COMMENT '币种精度',
                                                        `initial_amount` decimal(30,16) DEFAULT 0 COMMENT '初始金额',
                                                        `debit_amount` decimal(30,16) DEFAULT 0 COMMENT '借记金额',
                                                        `credit_amount` decimal(30,16) DEFAULT 0 COMMENT '贷记金额',
                                                        `final_total_balance_amount` decimal(30,16) DEFAULT 0 COMMENT '最终总余额',
                                                        `initial_frozen_amount` decimal(30,16) DEFAULT 0 COMMENT '初始冻结金额',
                                                        `frozen_amount` decimal(30,16) DEFAULT 0 COMMENT '冻结金额',
                                                        `release_amount` decimal(30,16) DEFAULT 0 COMMENT '释放金额',
                                                        `final_frozen_amount` decimal(30,16) DEFAULT 0 COMMENT '最终冻结金额',
                                                        `final_available_amount` decimal(30,16) DEFAULT 0 COMMENT '最终可用余额',
                                                        `business_action` varchar(32) DEFAULT NULL COMMENT '业务动作',
                                                        `business_transaction_no` varchar(64) DEFAULT NULL COMMENT '业务系统流水号',
                                                        `original_business_transaction_no` varchar(64) DEFAULT NULL COMMENT '原业务系统流水号(冲账的时候有值)',
                                                        `reversal_flag` tinyint DEFAULT 0 COMMENT '冲账标记:0:未冲账;1:已冲账',
                                                        `business_organization_no` varchar(16) DEFAULT NULL COMMENT '业务机构号(主要用于页面查询使用)',
                                                        `business_customer_id` varchar(64) DEFAULT NULL COMMENT '业务客户号(主要用于页面查询使用)',
                                                        `remark` varchar(512) DEFAULT NULL COMMENT '备注',
                                                        `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                        `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                                        PRIMARY KEY (`bookkeep_no`),
                                                        KEY idx_bd (bookkeep_datetime),
                                                        KEY idx_bs_btn_bd (business_system,business_transaction_no,bookkeep_datetime),
                                                        KEY idx_bs_obtn_bd (business_system,original_business_transaction_no,bookkeep_datetime)
) COMMENT='账户交易明细表';
CREATE TABLE `kl_account_transaction_detail_202505` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202507` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202508` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202509` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202510` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202511` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202512` LIKE `kl_account_transaction_detail_202506`;
-- 2026年账户交易明细表 --
CREATE TABLE `kl_account_transaction_detail_202601` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202602` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202603` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202604` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202605` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202606` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202607` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202608` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202609` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202610` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202611` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202612` LIKE `kl_account_transaction_detail_202506`;
-- 2027年账户交易明细表 --
CREATE TABLE `kl_account_transaction_detail_202701` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202702` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202703` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202704` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202705` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202706` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202707` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202708` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202709` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202710` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202711` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202712` LIKE `kl_account_transaction_detail_202506`;
-- 2028年账户交易明细表 --
CREATE TABLE `kl_account_transaction_detail_202801` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202802` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202803` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202804` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202805` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202806` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202807` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202808` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202809` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202810` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202811` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202812` LIKE `kl_account_transaction_detail_202506`;
-- 2029年账户交易明细表 --
CREATE TABLE `kl_account_transaction_detail_202901` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202902` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202903` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202904` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202905` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202906` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202907` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202908` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202909` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202910` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202911` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_202912` LIKE `kl_account_transaction_detail_202506`;
-- 2030年账户交易明细表 --
CREATE TABLE `kl_account_transaction_detail_203001` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_203002` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_203003` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_203004` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_203005` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_203006` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_203007` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_203008` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_203009` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_203010` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_203011` LIKE `kl_account_transaction_detail_202506`;
CREATE TABLE `kl_account_transaction_detail_203012` LIKE `kl_account_transaction_detail_202506`;

-- 账户日终快照表 --
CREATE TABLE `kl_account_daily_snapshot_202506` (
                                                    `id` bigint NOT NULL COMMENT '主键id',
                                                    `account_no` varchar(32) DEFAULT NULL COMMENT '账户号',
                                                    `business_system` varchar(16) DEFAULT NULL COMMENT '业务系统:KL/VCC',
                                                    `accounting_date` varchar(8) DEFAULT NULL COMMENT '会计日',
                                                    `snapshot_date` varchar(8) DEFAULT NULL COMMENT '快照日期',
                                                    `account_type` varchar(16) DEFAULT NULL COMMENT '账户类型001:法币基本账户/002:数币基本账户/003:信用法币账户',
                                                    `currency_code` varchar(16) DEFAULT NULL COMMENT '币种码',
                                                    `currency_precision` int DEFAULT NULL COMMENT '币种精度',
                                                    `initial_amount` decimal(30,16) DEFAULT 0 COMMENT '初始金额',
                                                    `initial_available_amount` decimal(30,16) DEFAULT 0 COMMENT '初始可用金额',
                                                    `debit_amount` decimal(30,16) DEFAULT 0 COMMENT '借记金额',
                                                    `credit_amount` decimal(30,16) DEFAULT 0 COMMENT '贷记金额',
                                                    `final_total_balance_amount` decimal(30,16) DEFAULT 0 COMMENT '最终总余额',
                                                    `initial_frozen_amount` decimal(30,16) DEFAULT 0 COMMENT '初始冻结金额',
                                                    `frozen_amount` decimal(30,16) DEFAULT 0 COMMENT '冻结金额',
                                                    `release_amount` decimal(30,16) DEFAULT 0 COMMENT '释放金额',
                                                    `final_frozen_amount` decimal(30,16) DEFAULT 0 COMMENT '最终冻结金额',
                                                    `final_available_amount` decimal(30,16) DEFAULT 0 COMMENT '最终可用余额',
                                                    `business_organization_no` varchar(16) DEFAULT NULL COMMENT '业务机构号(主要用于页面查询使用)',
                                                    `business_customer_id` varchar(64) DEFAULT NULL COMMENT '业务客户号(主要用于页面查询使用)',
                                                    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                    PRIMARY KEY (`id`),
                                                    KEY idx_sd (snapshot_date),
                                                    KEY idx_an_sd (`account_no`,`snapshot_date`),
                                                    KEY idx_an_bci (`account_no`,`business_customer_id`)
) COMMENT='账户日终快照表';
CREATE TABLE `kl_account_daily_snapshot_202505` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202507` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202508` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202509` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202510` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202511` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202512` LIKE `kl_account_daily_snapshot_202506`;
-- 2026年账户交易明细表 --
CREATE TABLE `kl_account_daily_snapshot_202601` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202602` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202603` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202604` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202605` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202606` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202607` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202608` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202609` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202610` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202611` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202612` LIKE `kl_account_daily_snapshot_202506`;
-- 2027年账户交易明细表 --
CREATE TABLE `kl_account_daily_snapshot_202701` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202702` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202703` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202704` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202705` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202706` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202707` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202708` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202709` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202710` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202711` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202712` LIKE `kl_account_daily_snapshot_202506`;
-- 2028年账户交易明细表 --
CREATE TABLE `kl_account_daily_snapshot_202801` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202802` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202803` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202804` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202805` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202806` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202807` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202808` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202809` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202810` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202811` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202812` LIKE `kl_account_daily_snapshot_202506`;
-- 2029年账户交易明细表 --
CREATE TABLE `kl_account_daily_snapshot_202901` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202902` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202903` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202904` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202905` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202906` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202907` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202908` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202909` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202910` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202911` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_202912` LIKE `kl_account_daily_snapshot_202506`;
-- 2030年账户交易明细表 --
CREATE TABLE `kl_account_daily_snapshot_203001` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_203002` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_203003` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_203004` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_203005` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_203006` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_203007` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_203008` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_203009` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_203010` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_203011` LIKE `kl_account_daily_snapshot_202506`;
CREATE TABLE `kl_account_daily_snapshot_203012` LIKE `kl_account_daily_snapshot_202506`;

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORGANIZATION_BUSINESS_TYPE', NULL, '01', 'U卡', 'U Card', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORGANIZATION_BUSINESS_TYPE', NULL, '02', 'VCC卡', 'VCC Card', 1, 2, now(), NULL, now(), NULL);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_BUSINESS_ACCOUNT_TYPE', NULL, '001', '法币基本账户', 'Basic', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_BUSINESS_ACCOUNT_TYPE', NULL, '002', '数币基本账户', 'Crypto', 1, 2, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_BUSINESS_ACCOUNT_TYPE', NULL, '003', '法币信用账户', 'Credit', 1, 3, now(), NULL, now(), NULL);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_PROCESSOR', NULL, 'BPC-GW', 'BPC-GW', 'BPC-GW', 1, 1, now(), NULL, now(), NULL);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CARD_SCHEME', NULL, '0', 'VISA', 'VISA', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CARD_SCHEME', NULL, '1', 'MASTERCARD', 'MASTERCARD', 1, 2, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CARD_SCHEME', NULL, '2', 'UPI', 'UPI', 1, 3, now(), NULL, now(), NULL);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_VALID_STATUS', NULL, 'VALID', '有效', 'Valid', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_VALID_STATUS', NULL, 'INVALID', '无效', 'Invalid', 1, 2, now(), NULL, now(), NULL);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_YES_FLAG', NULL, '1', '是', 'Yes', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_YES_FLAG', NULL, '0', '否', 'No', 1, 2, now(), NULL, now(), NULL);


INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_REVIEW_STATUS', NULL, 'PENDING', '待审核', 'PENDING', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_REVIEW_STATUS', NULL, 'PASS', '通过', 'PASS', 1, 2, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_REVIEW_STATUS', NULL, 'REJECT', '拒绝', 'REJECT', 1, 3, now(), NULL, now(), NULL);


INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_OPERATION_TYPE', NULL, 'ADD', '新增', 'Add', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_OPERATION_TYPE', NULL, 'MODIFY', '修改', 'Modify', 1, 2, now(), NULL, now(), NULL);


INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CARD_TYPE', NULL, '1', '共享卡', 'Share Card', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CARD_TYPE', NULL, '2', '充值卡', 'Recharge Card', 1, 2, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CARD_TYPE', NULL, '3', '账户卡', 'Account Card', 1, 3, now(), NULL, now(), NULL);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CARD_MODE', NULL, '1', '多次卡', 'Multiple Card', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CARD_MODE', NULL, '2', '单次卡', 'Single Card', 1, 2, now(), NULL, now(), NULL);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_BUSINESS_ACTION', NULL, 'RECHARGE', '充值', 'Recharge', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_BUSINESS_ACTION', NULL, 'CASHOUT', '提现', 'Cash Out', 1, 2, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_BUSINESS_ACTION', NULL, 'SALES', '消费', 'Sales', 1, 3, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_BUSINESS_ACTION', NULL, 'REFUND', '退款', 'Refund', 1, 4, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_BUSINESS_ACTION', NULL, 'VOID', '撤销', 'Void', 1, 5, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_BUSINESS_ACTION', NULL, 'PRE_AUTH_COMPLETION', '预授权完成', 'Pre Auth Completion', 1, 6, now(), NULL, now(), NULL);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CARDHOLDER_FEE_TYPE', NULL, '01', '充值FX Markup', 'Recharge FX Markup Fee', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CARDHOLDER_FEE_TYPE', NULL, '02', '充值承兑费', 'Recharge Acceptance Fee', 1, 2, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CARDHOLDER_FEE_TYPE', NULL, '03', '小额交易手续费', 'Small Transaction Fee', 1, 3, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CARDHOLDER_FEE_TYPE', NULL, '04', '交易手续费', 'Transaction Fee', 1, 4, now(), NULL, now(), NULL);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORGANIZATION_MODE', NULL, '1', '转三方授权', 'Transfer to Third Party Authorization', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORGANIZATION_MODE', NULL, '2', '充值上账', 'Recharge Posting', 1, 2, now(), NULL, now(), NULL);

INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'boss-kl-cardProduct' as code,
             '卡产品管理'           as name,
             (max(id) + 1)              as id,
             now()                      as create_date,
             null                       as update_date
      from vcc_permission ) temp where id >=1;

INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'boss-kl-organization-businessAccountAttribute' as code,
             '业务账户属性管理'           as name,
             (max(id) + 1)              as id,
             now()                      as create_date,
             null                       as update_date
      from vcc_permission ) temp where id >=1;

INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'boss-kl-organization-account' as code,
             '机构账户管理'           as name,
             (max(id) + 1)              as id,
             now()                      as create_date,
             null                       as update_date
      from vcc_permission ) temp where id >=1;

INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'boss-kl-organization-applicationCard' as code,
             '机构卡产品管理'           as name,
             (max(id) + 1)              as id,
             now()                      as create_date,
             null                       as update_date
      from vcc_permission ) temp where id >=1;

INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'boss-kl-organization-basic-info' as code,
             '机构基本信息管理'           as name,
             (max(id) + 1)              as id,
             now()                      as create_date,
             null                       as update_date
      from vcc_permission ) temp where id >=1;
INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'boss-kl-organization-customer-account' as code,
             '机构客户账户管理'           as name,
             (max(id) + 1)              as id,
             now()                      as create_date,
             null                       as update_date
      from vcc_permission) temp where id >=1 ;

INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'boss-kl-account' as code,
             '账户相关'           as name,
             (max(id) + 1)              as id,
             now()                      as create_date,
             null                       as update_date
      from vcc_permission ) temp where id >=1;
INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'boss-kl-cardholderFee' as code,
             '持卡人费用管理'           as name,
             (max(id) + 1)              as id,
             now()                      as create_date,
             null                       as update_date
      from vcc_permission ) temp where id >=1;
INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'boss-kl-organization-customer-card' as code,
             '机构客户卡片管理'           as name,
             (max(id) + 1)              as id,
             now()                      as create_date,
             null                       as update_date
      from vcc_permission ) temp where id >=1;

-- 机构交易清分表 --
create table kl_clearing_trans_202505
(
    clearing_id                        varchar(64) not null primary key comment '清算ID',
    auth_flow_id                       varchar(64) default null comment '授权流水ID',
    processor                          varchar(64)          default null comment 'Processor',
    gateway_request_no                 varchar(64)          default null comment '网关清算请求流水号',
    gateway_clearing_id                varchar(64)          default null comment '网关清算流水号',
    original_gateway_clearing_id       varchar(64)          default null comment '原网关清算流水号',
    processor_request_id               varchar(64)          default null comment '请求ID',
    processor_trans_id                 varchar(64)          default null comment 'processor交易ID',
    original_processor_trans_id        varchar(64)          default null comment 'processor原交易ID',
    merchant_no                        varchar(64)          default null comment '商户号',
    merchant_name                      varchar(256)         default null comment '商户名称',
    customer_id                        varchar(64)          default null comment '客户ID',
    status                             varchar(8)           default null comment '状态,如：PENDING:处理中;SUCCESS:成功;FAIL:失败;',
    mti                                varchar(4)           default null comment 'MTI',
    processing_code                    varchar(6)           default null comment 'Processing code',
    systems_trace_audit_number         varchar(32) null comment '系统跟踪审计号',
    gateway_card_id                    varchar(64)          default null comment '网关卡id',
    processor_card_id                  varchar(64)          default null comment '通道卡id',
    issuer_card_id                     varchar(64)          default null comment '发卡方卡id',
    masked_card_no                     varchar(128)         default null comment '脱敏卡号',
    trans_type                         varchar(32)          default null comment '交易类型',
    clearing_type                      varchar(32)          default null comment '清算类型',
    card_product_code                  varchar(32)          default null comment '卡产品编号',
    trans_currency                     varchar(3)           default null comment '交易币种',
    trans_currency_exponent            int(4)                  default null comment '交易币种的小数位数',
    trans_amount                       decimal(18, 3)       default null comment '交易金额',
    trans_fee                          decimal(18, 3)       default null comment '交易手续费',
    cardholder_billing_currency        varchar(3)           default null comment '持卡人账单币种',
    cardholder_currency_exponent       int(4)                  default null comment '持卡人账单币种的小数位数',
    cardholder_billing_amount          decimal(18, 3)       default null comment '持卡人账单金额(不含markup)',
    cardholder_markup_billing_amount   decimal(18, 3) null comment '持卡人账单金额(含markup)',
    markup_rate                        decimal(18, 4)       default 0 comment 'markup费率',
    markup_amount                      decimal(18, 3) null comment 'markup金额',
    pos_entry_mode                     varchar(12) null comment 'POS输入方式',
    transaction_local_datetime         varchar(16) null comment '交易发生地时间',
    conversion_rate_cardholder_billing decimal(8, 7) null comment '持卡人账单币种与交易币种的汇率',
    approve_code                       varchar(6) null comment '授权码',
    acquire_reference_no               varchar(32) null comment '参考号',
    card_acceptor_name                 varchar(40) null comment '收单商户名称',
    card_acceptor_id                   varchar(64) null comment '收单商户号',
    card_acceptor_tid                  varchar(16) null comment '收单商户终端号',
    card_acceptor_country_code         varchar(3) null comment '收单商户国家代码',
    card_acceptor_postal_code          varchar(12) null comment '收单商户邮政编码',
    card_acceptor_region               varchar(4) null comment '收单商户地区代码',
    card_acceptor_city                 varchar(16) null comment '收单商户城市',
    card_acceptor_street               varchar(256) null comment '收单商户街道地址',
    mcc                                varchar(4) null comment 'MCC',
    processor_ext_1                    varchar(32) null comment 'processor扩展字段1',
    original_auth_flow_id              varchar(64) null comment '原KL交易ID',
    original_processor_request_id      varchar(64) null comment '原交易processor请求ID',
    original_trans_time                varchar(14) null comment '原交易时间',
    clear_flag                         varchar(2) null comment '清分标记',
    trans_date                         varchar(8) null comment '交易日期',
    clearing_date                      varchar(8) null comment '清分日期',
    trans_time                         datetime null comment '交易时间',
    clear_amount                       decimal(18, 3)       default 0.000 null comment '清算金额',
    clear_time                         datetime null comment '清算时间',
    fee_interchange_sign               varchar(4) null comment 'IC费用方向',
    fee_interchange_amount             decimal(18, 3) null comment 'IC费用金额',
    issuer_charge                      varchar(4) null comment '发卡方费用标记',
    national_reimb_fee                 varchar(12) null comment 'National Reimbursement Fee',
    ing_fee_id                         int(8)         null comment '交换费ID',
    inter_fee_indicator                varchar(4) null comment '国际费用标记',
    fee_program_indicator              varchar(4) null comment '收费计划标记',
    overseas_flag                      varchar(4) null comment '是否跨境标记',
    conversion_date                    varchar(14) null comment '汇率日期',
    reversal_flag                      varchar(2) null comment '撤销标记',
    arn                                varchar(24) null comment '收单行参考号',
    acq_bin                            varchar(8) null comment '收单行BIN',
    usage_code                         varchar(1) null comment '用途代码',
    reason_code                        varchar(4) null comment '原因代码',
    card_schema_product_id             varchar(16) null comment '卡组产品ID(VISA产品ID)',
    create_time                        datetime    not null default current_timestamp comment '创建时间',
    update_time                        datetime    not null default current_timestamp on update current_timestamp comment '更新时间'
) comment '交易清分表';

create table kl_clearing_trans_202506 like kl_clearing_trans_202505;
create table kl_clearing_trans_202507 like kl_clearing_trans_202505;
create table kl_clearing_trans_202508 like kl_clearing_trans_202505;
create table kl_clearing_trans_202509 like kl_clearing_trans_202505;
create table kl_clearing_trans_202510 like kl_clearing_trans_202505;
create table kl_clearing_trans_202511 like kl_clearing_trans_202505;
create table kl_clearing_trans_202512 like kl_clearing_trans_202505;
create table kl_clearing_trans_202601 like kl_clearing_trans_202505;
create table kl_clearing_trans_202602 like kl_clearing_trans_202505;
create table kl_clearing_trans_202603 like kl_clearing_trans_202505;
create table kl_clearing_trans_202604 like kl_clearing_trans_202505;
create table kl_clearing_trans_202605 like kl_clearing_trans_202505;
create table kl_clearing_trans_202606 like kl_clearing_trans_202505;
create table kl_clearing_trans_202607 like kl_clearing_trans_202505;
create table kl_clearing_trans_202608 like kl_clearing_trans_202505;
create table kl_clearing_trans_202609 like kl_clearing_trans_202505;
create table kl_clearing_trans_202610 like kl_clearing_trans_202505;
create table kl_clearing_trans_202611 like kl_clearing_trans_202505;
create table kl_clearing_trans_202612 like kl_clearing_trans_202505;
create table kl_clearing_trans_202701 like kl_clearing_trans_202505;
create table kl_clearing_trans_202702 like kl_clearing_trans_202505;
create table kl_clearing_trans_202703 like kl_clearing_trans_202505;
create table kl_clearing_trans_202704 like kl_clearing_trans_202505;
create table kl_clearing_trans_202705 like kl_clearing_trans_202505;
create table kl_clearing_trans_202706 like kl_clearing_trans_202505;
create table kl_clearing_trans_202707 like kl_clearing_trans_202505;
create table kl_clearing_trans_202708 like kl_clearing_trans_202505;
create table kl_clearing_trans_202709 like kl_clearing_trans_202505;
create table kl_clearing_trans_202710 like kl_clearing_trans_202505;
create table kl_clearing_trans_202711 like kl_clearing_trans_202505;
create table kl_clearing_trans_202712 like kl_clearing_trans_202505;
create table kl_clearing_trans_202801 like kl_clearing_trans_202505;
create table kl_clearing_trans_202802 like kl_clearing_trans_202505;
create table kl_clearing_trans_202803 like kl_clearing_trans_202505;
create table kl_clearing_trans_202804 like kl_clearing_trans_202505;
create table kl_clearing_trans_202805 like kl_clearing_trans_202505;
create table kl_clearing_trans_202806 like kl_clearing_trans_202505;
create table kl_clearing_trans_202807 like kl_clearing_trans_202505;
create table kl_clearing_trans_202808 like kl_clearing_trans_202505;
create table kl_clearing_trans_202809 like kl_clearing_trans_202505;
create table kl_clearing_trans_202810 like kl_clearing_trans_202505;
create table kl_clearing_trans_202811 like kl_clearing_trans_202505;
create table kl_clearing_trans_202812 like kl_clearing_trans_202505;
create table kl_clearing_trans_202901 like kl_clearing_trans_202505;
create table kl_clearing_trans_202902 like kl_clearing_trans_202505;
create table kl_clearing_trans_202903 like kl_clearing_trans_202505;
create table kl_clearing_trans_202904 like kl_clearing_trans_202505;
create table kl_clearing_trans_202905 like kl_clearing_trans_202505;
create table kl_clearing_trans_202906 like kl_clearing_trans_202505;
create table kl_clearing_trans_202907 like kl_clearing_trans_202505;
create table kl_clearing_trans_202908 like kl_clearing_trans_202505;
create table kl_clearing_trans_202909 like kl_clearing_trans_202505;
create table kl_clearing_trans_202910 like kl_clearing_trans_202505;
create table kl_clearing_trans_202911 like kl_clearing_trans_202505;
create table kl_clearing_trans_202912 like kl_clearing_trans_202505;
create table kl_clearing_trans_203001 like kl_clearing_trans_202505;
create table kl_clearing_trans_203002 like kl_clearing_trans_202505;
create table kl_clearing_trans_203003 like kl_clearing_trans_202505;
create table kl_clearing_trans_203004 like kl_clearing_trans_202505;
create table kl_clearing_trans_203005 like kl_clearing_trans_202505;
create table kl_clearing_trans_203006 like kl_clearing_trans_202505;
create table kl_clearing_trans_203007 like kl_clearing_trans_202505;
create table kl_clearing_trans_203008 like kl_clearing_trans_202505;
create table kl_clearing_trans_203009 like kl_clearing_trans_202505;
create table kl_clearing_trans_203010 like kl_clearing_trans_202505;
create table kl_clearing_trans_203011 like kl_clearing_trans_202505;
create table kl_clearing_trans_203012 like kl_clearing_trans_202505;


create table kl_clearing_exception_trans
(
    id                                 varchar(64)    not null primary key comment 'ID',
    clearing_id                        varchar(64)    not null comment '清算ID',
    auth_flow_id                       varchar(64)             default null comment '授权流水ID',
    processor                          varchar(64)             default null comment 'Processor',
    processor_request_id               varchar(64)             default null comment '请求ID',
    processor_trans_id                 varchar(64)             default null comment 'processor交易ID',
    original_processor_trans_id        varchar(64)             default null comment 'processor原交易ID',
    merchant_no                        varchar(64)             default null comment '商户号',
    merchant_name                      varchar(256)            default null comment '商户名称',
    customer_id                        varchar(64)             default null comment '客户ID',
    status                             varchar(8)              default null comment '状态,如：PENDING:处理中;SUCCESS:成功;FAIL:失败;',
    mti                                varchar(4)              default null comment 'MTI',
    processing_code                    varchar(6)              default null comment 'Processing code',
    gateway_card_id                    varchar(64)             default null comment '网关卡id',
    processor_card_id                  varchar(64)             default null comment '通道卡id',
    issuer_card_id                     varchar(64)             default null comment '发卡方卡id',
    masked_card_no                     varchar(128)            default null comment '脱敏卡号',
    trans_type                         varchar(32)             default null comment '交易类型',
    card_product_code                  varchar(32)             default null comment '卡产品编号',
    trans_currency                     varchar(3)              default null comment '交易币种',
    trans_amount                       decimal(18, 3)          default null comment '交易金额',
    trans_currency_exponent            int(4)                  default null comment '交易币种的小数位数',
    trans_fee                          decimal(18, 3)          default null comment '交易手续费',
    cardholder_billing_currency        varchar(3)              default null comment '持卡人账单币种',
    cardholder_currency_exponent       int(4)                  default null comment '持卡人账单币种的小数位数',
    cardholder_billing_amount          decimal(18, 3)          default null comment '持卡人账单金额(不含markup)',
    cardholder_markup_billing_amount   decimal(18, 3) null comment '持卡人账单金额(含markup)',
    markup_rate                        decimal(18, 4)          default 0 comment 'markup费率',
    markup_amount                      decimal(18, 3) null comment 'markup金额',
    pos_entry_mode                     varchar(12)    null comment 'POS输入方式',
    transaction_local_datetime         varchar(16)    null comment '交易发生地时间',
    conversion_rate_cardholder_billing decimal(8, 7)  null comment '持卡人账单币种与交易币种的汇率',
    approve_code                       varchar(6)     null comment '授权码',
    acquire_reference_no               varchar(32)    null comment '参考号',
    card_acceptor_name                 varchar(40)    null comment '收单商户名称',
    card_acceptor_id                   varchar(64)    null comment '收单商户号',
    card_acceptor_tid                  varchar(16)    null comment '收单商户终端号',
    card_acceptor_country_code         varchar(3)     null comment '收单商户国家代码',
    card_acceptor_postal_code          varchar(12)    null comment '收单商户邮政编码',
    card_acceptor_region               varchar(4)     null comment '收单商户地区代码',
    card_acceptor_city                 varchar(16)    null comment '收单商户城市',
    card_acceptor_street               varchar(256)   null comment '收单商户街道地址',
    mcc                                varchar(4)     null comment 'MCC',
    arn                                varchar(24)    null comment '收单行参考号',
    processor_ext_1                    varchar(32)    null comment 'processor扩展字段1',
    original_auth_flow_id              varchar(64)    null comment '原KL交易ID',
    original_processor_request_id      varchar(64)    null comment '原交易processor请求ID',
    original_trans_time                varchar(14)    null comment '原交易时间',
    exception_reason                   varchar(256)   null comment '异常原因',
    trans_date                         varchar(8)     null comment '交易日期',
    trans_time                         datetime       null comment '交易时间',
    clearing_date                      varchar(8)     null comment '清分日期',
    clear_amount                       decimal(18, 3)          default 0.000 null comment '清算金额',
    process_flag                       varchar(2)              default 'N' comment '处理标记, Y:已处理;N:未处理',
    process_time                       datetime       null comment '处理时间',
    process_by                         varchar(64)    null comment '处理人',
    process_remark                     varchar(256)   null comment '处理备注',
    create_time                        datetime       not null default current_timestamp comment '创建时间',
    update_time                        datetime       not null default current_timestamp on update current_timestamp comment '更新时间'
) comment '交易清分异常表,用于记录清分异常的交易,只保留3个月，超过3个月的交易挪入历史表';

create table kl_clearing_exception_tran_history like kl_clearing_exception_trans;
alter table kl_clearing_exception_tran_history comment '交易清分异常历史数据表';


-- 机构用户钱包信息表 --
CREATE TABLE `kl_organization_customer_wallet_info_0` (
                                                          `id` bigint NOT NULL COMMENT '主键id',
                                                          `organization_no` varchar(16) DEFAULT NULL COMMENT '机构号',
                                                          `customer_id` varchar(64) DEFAULT NULL COMMENT '客户号',
                                                          `wallet_network` varchar(32) DEFAULT NULL COMMENT '钱包通道',
                                                          `chain_network` varchar(128) DEFAULT NULL COMMENT '链网络',
                                                          `wallet_address` varchar(256) DEFAULT NULL COMMENT '钱包地址',
                                                          `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                                          `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                          `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                                          PRIMARY KEY (`id`),
                                                          KEY idx_on_ci_wn_cn(`organization_no`,`customer_id`,`wallet_network`,chain_network)
) COMMENT='机构用户钱包信息表';
CREATE TABLE `kl_organization_customer_wallet_info_1` LIKE `kl_organization_customer_wallet_info_0`;
CREATE TABLE `kl_organization_customer_wallet_info_2` LIKE `kl_organization_customer_wallet_info_0`;
CREATE TABLE `kl_organization_customer_wallet_info_3` LIKE `kl_organization_customer_wallet_info_0`;
CREATE TABLE `kl_organization_customer_wallet_info_4` LIKE `kl_organization_customer_wallet_info_0`;
CREATE TABLE `kl_organization_customer_wallet_info_5` LIKE `kl_organization_customer_wallet_info_0`;
CREATE TABLE `kl_organization_customer_wallet_info_6` LIKE `kl_organization_customer_wallet_info_0`;
CREATE TABLE `kl_organization_customer_wallet_info_7` LIKE `kl_organization_customer_wallet_info_0`;


-- 机构用户钱包操作记录表 --
CREATE TABLE `kl_organization_customer_wallet_operation_record_0` (
                                                                      `id` bigint NOT NULL COMMENT '主键id',
                                                                      `operation_type` varchar(16) DEFAULT NULL COMMENT '操作类型',
                                                                      `organization_no` varchar(16) DEFAULT NULL COMMENT '机构号',
                                                                      `customer_id` varchar(64) DEFAULT NULL COMMENT '客户号',
                                                                      `request_no` varchar(64) DEFAULT NULL COMMENT '请求流水号',
                                                                      `wallet_network` varchar(32) DEFAULT NULL COMMENT '钱包通道',
                                                                      `chain_network` varchar(128) DEFAULT NULL COMMENT '链网络',
                                                                      `wallet_address` varchar(256) DEFAULT NULL COMMENT '钱包地址',
                                                                      `operation_status` varchar(16) DEFAULT NULL COMMENT '操作状态;SUCCESS:成功;FAIL:失败(该状态代表系统处理状态)',
                                                                      `fail_message` varchar(256) DEFAULT NULL COMMENT '失败信息',
                                                                      `request_params` varchar(2048) DEFAULT NULL COMMENT '原请求参数(json)',
                                                                      `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                                      `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                                                      PRIMARY KEY (`id`),
                                                                      KEY idx_ot_on_rn_ci(`operation_type`,`organization_no`,`request_no`,`customer_id`)
) COMMENT='机构用户钱包操作记录表';
CREATE TABLE `kl_organization_customer_wallet_operation_record_1` LIKE `kl_organization_customer_wallet_operation_record_0`;
CREATE TABLE `kl_organization_customer_wallet_operation_record_2` LIKE `kl_organization_customer_wallet_operation_record_0`;
CREATE TABLE `kl_organization_customer_wallet_operation_record_3` LIKE `kl_organization_customer_wallet_operation_record_0`;
CREATE TABLE `kl_organization_customer_wallet_operation_record_4` LIKE `kl_organization_customer_wallet_operation_record_0`;
CREATE TABLE `kl_organization_customer_wallet_operation_record_5` LIKE `kl_organization_customer_wallet_operation_record_0`;
CREATE TABLE `kl_organization_customer_wallet_operation_record_6` LIKE `kl_organization_customer_wallet_operation_record_0`;
CREATE TABLE `kl_organization_customer_wallet_operation_record_7` LIKE `kl_organization_customer_wallet_operation_record_0`;

-- MPC钱包通知记录表 --
CREATE TABLE `kl_mpc_wallet_webhook_record_0` (
                                                  `id` bigint NOT NULL COMMENT '主键id',
                                                  `request_no` varchar(64) DEFAULT NULL COMMENT '请求流水号',
                                                  `organization_no` varchar(16) DEFAULT NULL COMMENT '机构号',
                                                  `customer_id` varchar(64) DEFAULT NULL COMMENT '客户号',
                                                  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户ID',
                                                  `amount` decimal(30,16) DEFAULT NULL COMMENT '金额',
                                                  `currency_code` varchar(16) DEFAULT NULL COMMENT '币种',
                                                  `address_from` varchar(256) DEFAULT NULL COMMENT '充值地址',
                                                  `address_to` varchar(256) DEFAULT NULL COMMENT '到账地址',
                                                  `chain_network` varchar(16) DEFAULT NULL COMMENT '链网络',
                                                  `tx_hash` varchar(256) DEFAULT NULL COMMENT '交易hash',
                                                  `status` varchar(16) DEFAULT NULL COMMENT '钱包方状态:INIT(待上链),PROCESSING(进行中),SUCCESS(成功),FAILED(失败)',
                                                  `block_number` varchar(64) DEFAULT NULL COMMENT '区块高度',
                                                  `scene` varchar(16) DEFAULT NULL COMMENT '业务场景:DEPOSIT(充值),WITHDRAW(提现),ENERGY(补充gas),AGG(资金归集)',
                                                  `gas_fee` decimal(30,16) DEFAULT NULL COMMENT 'gas费',
                                                  `gas_currency` varchar(16) DEFAULT NULL COMMENT 'gas币种',
                                                  `risk_score` varchar(64) DEFAULT NULL COMMENT '风险系数',
                                                  `risk_suggest` varchar(16) DEFAULT NULL COMMENT '风险建议充值回调根据这个判断是否上账:PASS(通过),BLOCK(阻断),CONFIRM(待确认)',
                                                  `processing_status` varchar(16) DEFAULT NULL COMMENT '系统处理状态:SUCCESS,FAIL,BLOCK(风险建议不通过会将该状态更新为BLOCK,等待人工处理)',
                                                  `fail_message` varchar(128) DEFAULT NULL COMMENT '失败信息',
                                                  `retry_count` int DEFAULT 0 COMMENT '重试次数',
                                                  `organization_bookkeep_status` tinyint DEFAULT 0 COMMENT '机构账记账状态:1:成功;0:失败或未知',
                                                  `customer_bookkeep_status` tinyint DEFAULT 0 COMMENT '客户账记账状态:1:成功;0:失败或未知',
                                                  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                  `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                                  PRIMARY KEY (`id`),
                                                  KEY idx_on_ti_rn(`organization_no`,`tenant_id`,`request_no`)
) COMMENT='MPC钱包通知记录表';
CREATE TABLE `kl_mpc_wallet_webhook_record_1` LIKE `kl_mpc_wallet_webhook_record_0`;
CREATE TABLE `kl_mpc_wallet_webhook_record_2` LIKE `kl_mpc_wallet_webhook_record_0`;
CREATE TABLE `kl_mpc_wallet_webhook_record_3` LIKE `kl_mpc_wallet_webhook_record_0`;
CREATE TABLE `kl_mpc_wallet_webhook_record_4` LIKE `kl_mpc_wallet_webhook_record_0`;
CREATE TABLE `kl_mpc_wallet_webhook_record_5` LIKE `kl_mpc_wallet_webhook_record_0`;
CREATE TABLE `kl_mpc_wallet_webhook_record_6` LIKE `kl_mpc_wallet_webhook_record_0`;
CREATE TABLE `kl_mpc_wallet_webhook_record_7` LIKE `kl_mpc_wallet_webhook_record_0`;


-- 机构用户钱包交易流水表 --
CREATE TABLE `kl_wallet_transaction_detail_202505` (
                                                       `id` varchar(32) NOT NULL COMMENT '流水id',
                                                       `organization_no` varchar(16) DEFAULT NULL COMMENT '机构号',
                                                       `customer_id` varchar(64) DEFAULT NULL COMMENT '客户号',
                                                       `request_no` varchar(64) DEFAULT NULL COMMENT '请求流水号',
                                                       `wallet_network` varchar(32) DEFAULT NULL COMMENT '钱包通道',
                                                       `chain_network` varchar(128) DEFAULT NULL COMMENT '链网络',
                                                       `wallet_address` varchar(256) DEFAULT NULL COMMENT '钱包地址',
                                                       `transaction_type` varchar(16) DEFAULT NULL COMMENT '交易类型',
                                                       `transaction_datetime` datetime NULL DEFAULT NULL COMMENT '钱包交易日期时间',
                                                       `digital_amount` decimal(30,16) DEFAULT 0 COMMENT '数币金额',
                                                       `digital_currency_code` varchar(16) DEFAULT NULL COMMENT '数币币种',
                                                       `digital_currency_precision` int DEFAULT 6 COMMENT '数币币种精度',
                                                       `fiat_amount` decimal(30,16) DEFAULT 0 COMMENT '法币金额',
                                                       `fiat_currency_code` varchar(16) DEFAULT NULL COMMENT '法币币种',
                                                       `fiat_currency_precision` int DEFAULT 2 COMMENT '法币币种精度',
                                                       `fx_rate` decimal(10,5) DEFAULT 1 COMMENT '换汇汇率',
                                                       `markup_fee_proportion_rate` decimal(8,4) DEFAULT 0 COMMENT 'markup百分比费率',
                                                       `markup_fee_proportion_min_amount` decimal(12,2) DEFAULT NULL COMMENT 'markup百分比费率金额最小值',
                                                       `markup_fee_proportion_max_amount` decimal(12,2) DEFAULT NULL COMMENT 'markup百分比费率金额最大值',
                                                       `markup_fee_proportion_amount` decimal(30,16) DEFAULT NULL COMMENT 'markup百分比费率金额',
                                                       `markup_fee_fixed_amount` decimal(12,2) DEFAULT 0 COMMENT 'markup固定值费用',
                                                       `markup_fee_amount` decimal(30,16) DEFAULT 0 COMMENT 'markup费用金额',
                                                       `recharge_acceptance_fee_proportion_rate` decimal(8,4) DEFAULT 0 COMMENT '充值承兑百分比费率',
                                                       `recharge_acceptance_fee_proportion_min_amount` decimal(12,2) DEFAULT NULL COMMENT '充值承兑百分比费率金额最小值',
                                                       `recharge_acceptance_fee_proportion_max_amount` decimal(12,2) DEFAULT NULL COMMENT '充值承兑百分比费率金额最大值',
                                                       `recharge_acceptance_fee_proportion_amount` decimal(30,16) DEFAULT NULL COMMENT '充值承兑百分比费率金额',
                                                       `recharge_acceptance_fee_fixed_amount` decimal(12,2) DEFAULT 0 COMMENT '充值承兑固定值费用',
                                                       `recharge_acceptance_fee_amount` decimal(30,16) DEFAULT 0 COMMENT '充值承兑费用金额(充值时会使用)',
                                                       `bookkeep_no` varchar(32) DEFAULT NULL COMMENT '记账流水号',
                                                       `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                       `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                                       PRIMARY KEY (`id`),
                                                       KEY idx_tt(`transaction_type`),
                                                       KEY idx_td(`transaction_datetime`),
                                                       KEY idx_rn(`request_no`)
) COMMENT='机构用户钱包交易流水表';
CREATE TABLE `kl_wallet_transaction_detail_202506` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202507` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202508` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202509` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202510` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202511` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202512` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202601` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202602` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202603` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202604` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202605` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202606` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202607` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202608` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202609` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202610` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202611` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202612` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202701` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202702` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202703` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202704` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202705` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202706` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202707` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202708` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202709` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202710` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202711` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202712` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202801` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202802` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202803` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202804` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202805` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202806` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202807` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202808` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202809` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202810` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202811` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202812` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202901` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202902` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202903` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202904` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202905` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202906` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202907` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202908` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202909` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202910` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202911` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_202912` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_203001` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_203002` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_203003` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_203004` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_203005` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_203006` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_203007` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_203008` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_203009` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_203010` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_203011` LIKE `kl_wallet_transaction_detail_202505`;
CREATE TABLE `kl_wallet_transaction_detail_203012` LIKE `kl_wallet_transaction_detail_202505`;

-- 持卡人费用信息表 --
CREATE TABLE `kl_cardholder_fee` (
                                     `fee_id` bigint NOT NULL COMMENT '主键id',
                                     `organization_no` varchar(16) DEFAULT NULL COMMENT '机构号',
                                     `card_product_code` varchar(16) DEFAULT NULL COMMENT '卡产品编码',
                                     `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                     `effective_start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
                                     `effective_end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
                                     `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                     `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                     `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建人名称',
                                     `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                     `last_modify_user_id` varchar(32) DEFAULT NULL COMMENT '最后一次修改人id',
                                     `last_modify_user_name` varchar(128) DEFAULT NULL COMMENT '最后一次修改人名称',
                                     PRIMARY KEY (`fee_id`),
                                     KEY idx_on_cpc_est_eet(`organization_no`,`card_product_code`,`effective_start_time`,`effective_end_time`)
) COMMENT='持卡人费用信息表';

-- 持卡人费用信息明细表 --
CREATE TABLE `kl_cardholder_fee_detail` (
                                            `fee_detail_id` bigint NOT NULL COMMENT '主键id',
                                            `fee_id` bigint DEFAULT NULL COMMENT '所属费用信息id',
                                            `fee_type` varchar(4) DEFAULT NULL COMMENT '手续费类型',
                                            `min_amount` decimal(14,2) DEFAULT 0 COMMENT '金额区间:最小金额',
                                            `max_amount` decimal(14,2) DEFAULT 0 COMMENT '金额区间:最大金额',
                                            `scribing_proportion_rate` decimal(8,4) DEFAULT 0 COMMENT '划线费率-比例',
                                            `scribing_proportion_min_amount` decimal(12,2) DEFAULT NULL COMMENT '划线费率-比例的保底金额',
                                            `scribing_proportion_max_amount` decimal(12,2) DEFAULT NULL COMMENT '划线费率-比例的封顶金额',
                                            `scribing_fixed_amount` decimal(12,2) DEFAULT 0 COMMENT '划线费率-固定值',
                                            `actual_proportion_rate` decimal(8,4) DEFAULT 0 COMMENT '实际费率-比例',
                                            `actual_proportion_min_amount` decimal(12,2) DEFAULT NULL COMMENT '实际费率-比例的保底金额',
                                            `actual_proportion_max_amount` decimal(12,2) DEFAULT NULL COMMENT '实际费率-比例的封顶金额',
                                            `actual_fixed_amount` decimal(12,2) DEFAULT 0 COMMENT '实际费率-固定值',
                                            `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                            `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                            `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建人名称',
                                            `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后一次修改时间',
                                            `last_modify_user_id` varchar(32) DEFAULT NULL COMMENT '最后一次修改人id',
                                            `last_modify_user_name` varchar(128) DEFAULT NULL COMMENT '最后一次修改人名称',
                                            PRIMARY KEY (`fee_detail_id`),
                                            KEY idx_fi_ft(`fee_id`, `fee_type`)
) COMMENT='持卡人费用信息明细表';

-- 持卡人费用信息审核记录表 --
CREATE TABLE `kl_cardholder_fee_review_record` (
                                                   `review_id` bigint NOT NULL COMMENT '审核id',
                                                   `operator_type` varchar(8) NOT NULL COMMENT '操作类型:ADD,MODIFY',
                                                   `fee_id` bigint DEFAULT NULL COMMENT '费率信息表id,修改时有值',
                                                   `organization_no` varchar(16) DEFAULT NULL COMMENT '机构号',
                                                   `card_product_code` varchar(16) DEFAULT NULL COMMENT '卡产品编码',
                                                   `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                                   `effective_start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
                                                   `effective_end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
                                                   `review_status` varchar(16) DEFAULT NULL COMMENT '审核状态',
                                                   `review_Reason` varchar(512) DEFAULT NULL COMMENT '审核备注',
                                                   `submit_time` datetime NULL DEFAULT NULL COMMENT '提交时间',
                                                   `submit_user_id` varchar(32) DEFAULT NULL COMMENT '提交人id',
                                                   `submit_user_name` varchar(128) DEFAULT NULL COMMENT '提交人名称',
                                                   `review_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
                                                   `review_user_id` varchar(32) DEFAULT NULL COMMENT '审核人id',
                                                   `review_user_name` varchar(128) DEFAULT NULL COMMENT '审核人名称',
                                                   PRIMARY KEY (`review_id`),
                                                   KEY idx_on_cpc(`organization_no`, `card_product_code`)
) COMMENT='持卡人费用信息审核记录表';


-- 持卡人费用信息明细审核记录表 --
CREATE TABLE `kl_cardholder_fee_detail_review_record` (
                                                          `review_detail_id` bigint NOT NULL COMMENT '审核明细id',
                                                          `review_id` bigint NOT NULL COMMENT '所属审核id',
                                                          `operator_type` varchar(8) NOT NULL COMMENT '操作类型:ADD,MODIFY',
                                                          `fee_id` bigint DEFAULT NULL COMMENT '所属费用信息id,修改时有值',
                                                          `fee_detail_id` bigint DEFAULT NULL COMMENT '所属费用明细信息id,修改时有值',
                                                          `fee_type` varchar(4) DEFAULT NULL COMMENT '手续费类型',
                                                          `min_amount` decimal(14,2) DEFAULT 0 COMMENT '金额区间:最小金额',
                                                          `max_amount` decimal(14,2) DEFAULT 0 COMMENT '金额区间:最大金额',
                                                          `scribing_proportion_rate` decimal(8,4) DEFAULT 0 COMMENT '划线费率-比例',
                                                          `scribing_proportion_min_amount` decimal(12,2) DEFAULT NULL COMMENT '划线费率-比例的保底金额',
                                                          `scribing_proportion_max_amount` decimal(12,2) DEFAULT NULL COMMENT '划线费率-比例的封顶金额',
                                                          `scribing_fixed_amount` decimal(12,2) DEFAULT 0 COMMENT '划线费率-固定值',
                                                          `actual_proportion_rate` decimal(8,4) DEFAULT 0 COMMENT '实际费率-比例',
                                                          `actual_proportion_min_amount` decimal(12,2) DEFAULT NULL COMMENT '实际费率-比例的保底金额',
                                                          `actual_proportion_max_amount` decimal(12,2) DEFAULT NULL COMMENT '实际费率-比例的封顶金额',
                                                          `actual_fixed_amount` decimal(12,2) DEFAULT 0 COMMENT '实际费率-固定值',
                                                          `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                          PRIMARY KEY (`review_detail_id`)
) COMMENT='持卡人费用信息明细审核记录表';

-- 机构限额配置表 --
CREATE TABLE `kl_organization_limit_config` (
                                                `id` bigint NOT NULL COMMENT '主键id',
                                                `organization_no` varchar(16) DEFAULT NULL COMMENT '机构号',
                                                `customer_level` int DEFAULT NULL COMMENT '客户等级',
                                                `single_transaction_limit_amount` decimal(20,2) DEFAULT NULL COMMENT '单笔限额',
                                                `daily_limit_amount` decimal(20,2) DEFAULT NULL COMMENT '日限额',
                                                `currency_code` varchar(8) DEFAULT NULL COMMENT '币种码',
                                                `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                                `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建人名称',
                                                `last_modify_time` datetime DEFAULT NULL COMMENT '最后一次修改时间',
                                                `last_modify_user_id` varchar(32) DEFAULT NULL COMMENT '最后一次修改人id',
                                                `last_modify_user_name` varchar(128) DEFAULT NULL COMMENT '最后一次修改人名称',
                                                PRIMARY KEY (`id`),
                                                KEY idx_on_cl_s(`organization_no`,`customer_level`, `status`)
) COMMENT='机构限额配置表';


-- 机构用户限额累计表 --
CREATE TABLE `kl_organization_customer_limit_usage` (
                                                        `id` bigint NOT NULL COMMENT '主键id',
                                                        `organization_no` varchar(16) DEFAULT NULL COMMENT '机构号',
                                                        `customer_id` varchar(64) DEFAULT NULL COMMENT '客户号',
                                                        `daily_limit_used_amount` decimal(20,2) DEFAULT 0 COMMENT '日累计限额',
                                                        `currency_code` varchar(8) DEFAULT NULL COMMENT '币种码',
                                                        `last_used_date` varchar(8) DEFAULT NULL COMMENT '最后一次使用日期',
                                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                        `last_modify_time` datetime DEFAULT NULL COMMENT '最后一次修改时间',
                                                        PRIMARY KEY (`id`),
                                                        KEY idx_on_ci(`organization_no`,`customer_id`)
) COMMENT='机构用户限额累计表';


-- 交易请求账户记录表 --
create table kl_auth_account_log_202505
(
    id               varchar(64) not null primary key comment '主键',
    auth_flow_id     varchar(64) not null comment '授权流水ID',
    account_type     varchar(32) not null comment '账户类型',
    account_no       varchar(64) not null comment '账户号',
    request_json     text         default null comment '请求参数',
    request_no       varchar(64) not null comment '请求号',
    response_code    varchar(32)  default null comment '响应码',
    response_message varchar(256) default null comment '响应信息',
    response_json    text         default null comment '响应参数',
    create_time      datetime     default now() comment '创建时间',
    update_time      datetime     default now() on update now() comment '更新时间'
) comment '交易请求账户记录表';
create table kl_auth_account_log_202506 like kl_auth_account_log_202505;
create table kl_auth_account_log_202507 like kl_auth_account_log_202505;
create table kl_auth_account_log_202508 like kl_auth_account_log_202505;
create table kl_auth_account_log_202509 like kl_auth_account_log_202505;
create table kl_auth_account_log_202510 like kl_auth_account_log_202505;
create table kl_auth_account_log_202511 like kl_auth_account_log_202505;
create table kl_auth_account_log_202512 like kl_auth_account_log_202505;
create table kl_auth_account_log_202601 like kl_auth_account_log_202505;
create table kl_auth_account_log_202602 like kl_auth_account_log_202505;
create table kl_auth_account_log_202603 like kl_auth_account_log_202505;
create table kl_auth_account_log_202604 like kl_auth_account_log_202505;
create table kl_auth_account_log_202605 like kl_auth_account_log_202505;
create table kl_auth_account_log_202606 like kl_auth_account_log_202505;
create table kl_auth_account_log_202607 like kl_auth_account_log_202505;
create table kl_auth_account_log_202608 like kl_auth_account_log_202505;
create table kl_auth_account_log_202609 like kl_auth_account_log_202505;
create table kl_auth_account_log_202610 like kl_auth_account_log_202505;
create table kl_auth_account_log_202611 like kl_auth_account_log_202505;
create table kl_auth_account_log_202612 like kl_auth_account_log_202505;
create table kl_auth_account_log_202701 like kl_auth_account_log_202505;
create table kl_auth_account_log_202702 like kl_auth_account_log_202505;
create table kl_auth_account_log_202703 like kl_auth_account_log_202505;
create table kl_auth_account_log_202704 like kl_auth_account_log_202505;
create table kl_auth_account_log_202705 like kl_auth_account_log_202505;
create table kl_auth_account_log_202706 like kl_auth_account_log_202505;
create table kl_auth_account_log_202707 like kl_auth_account_log_202505;
create table kl_auth_account_log_202708 like kl_auth_account_log_202505;
create table kl_auth_account_log_202709 like kl_auth_account_log_202505;
create table kl_auth_account_log_202710 like kl_auth_account_log_202505;
create table kl_auth_account_log_202711 like kl_auth_account_log_202505;
create table kl_auth_account_log_202712 like kl_auth_account_log_202505;
create table kl_auth_account_log_202801 like kl_auth_account_log_202505;
create table kl_auth_account_log_202802 like kl_auth_account_log_202505;
create table kl_auth_account_log_202803 like kl_auth_account_log_202505;
create table kl_auth_account_log_202804 like kl_auth_account_log_202505;
create table kl_auth_account_log_202805 like kl_auth_account_log_202505;
create table kl_auth_account_log_202806 like kl_auth_account_log_202505;
create table kl_auth_account_log_202807 like kl_auth_account_log_202505;
create table kl_auth_account_log_202808 like kl_auth_account_log_202505;
create table kl_auth_account_log_202809 like kl_auth_account_log_202505;
create table kl_auth_account_log_202810 like kl_auth_account_log_202505;
create table kl_auth_account_log_202811 like kl_auth_account_log_202505;
create table kl_auth_account_log_202812 like kl_auth_account_log_202505;
create table kl_auth_account_log_202901 like kl_auth_account_log_202505;
create table kl_auth_account_log_202902 like kl_auth_account_log_202505;
create table kl_auth_account_log_202903 like kl_auth_account_log_202505;
create table kl_auth_account_log_202904 like kl_auth_account_log_202505;
create table kl_auth_account_log_202905 like kl_auth_account_log_202505;
create table kl_auth_account_log_202906 like kl_auth_account_log_202505;
create table kl_auth_account_log_202907 like kl_auth_account_log_202505;
create table kl_auth_account_log_202908 like kl_auth_account_log_202505;
create table kl_auth_account_log_202909 like kl_auth_account_log_202505;
create table kl_auth_account_log_202910 like kl_auth_account_log_202505;
create table kl_auth_account_log_202911 like kl_auth_account_log_202505;
create table kl_auth_account_log_202912 like kl_auth_account_log_202505;
create table kl_auth_account_log_203001 like kl_auth_account_log_202505;
create table kl_auth_account_log_203002 like kl_auth_account_log_202505;
create table kl_auth_account_log_203003 like kl_auth_account_log_202505;
create table kl_auth_account_log_203004 like kl_auth_account_log_202505;
create table kl_auth_account_log_203005 like kl_auth_account_log_202505;
create table kl_auth_account_log_203006 like kl_auth_account_log_202505;
create table kl_auth_account_log_203007 like kl_auth_account_log_202505;
create table kl_auth_account_log_203008 like kl_auth_account_log_202505;
create table kl_auth_account_log_203009 like kl_auth_account_log_202505;
create table kl_auth_account_log_203010 like kl_auth_account_log_202505;
create table kl_auth_account_log_203011 like kl_auth_account_log_202505;
create table kl_auth_account_log_203012 like kl_auth_account_log_202505;


INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '010000', '授权：包括消费', 'Authorization', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '010001', '预授权，预授权追加', 'Pre-auth', 1, 2, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '010002', '预授权完成', 'Pre-Auth Completion', 1, 3, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '010003', '取现', 'Cash Advance', 1, 4, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '010004', '退款', 'Refund', 1, 5, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '010005', '转出', 'Transfer Out', 1, 6, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '010006', '转入', 'Transfer In', 1, 7, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '020000', '授权撤销', 'Authorization VOID', 1, 8, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '020001', '预授权撤销', 'Pre-auth VOID', 1, 9, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '020002', '预授权完成撤销', 'Pre-Auth Completion VOID', 10, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '020003', '取现撤销', 'Cash Advance VOID', 1, 11, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '020004', '退款撤销', 'Refund VOID', 1, 12, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '020005', '转出撤销', 'Transfer Out VOID', 1, 13, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '020006', '转入撤销', 'Transfer In VOID', 1, 14, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '410000', '授权冲正', 'Authorization Reversal', 1, 15, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '410001', '预授权冲正', 'Pre-auth Reversal', 1, 16, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '410002', '预授权完成冲正', 'Pre-auth Completion Reversal', 17, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '410003', '取现冲正', 'Cash Advance Reversal', 1, 18, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '410004', '退款冲正', 'Refund Reversal', 1, 19, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '410005', '转出冲正', 'Transfer Out Reversal', 1, 20, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '410006', '转入冲正', 'Transfer In Reversal', 1, 21, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '420000', '授权撤销冲正', 'Authorization VOID Reversal', 1, 22, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '420001', '预授权撤销冲正', 'Pre-auth VOID Reversal', 1, 23, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '420002', '预授权完成撤销冲正', 'Pre-auth Completion VOID Reversal', 1, 24, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '420003', '取现撤销冲正', 'Cash Advance VOID Reversal', 1, 25, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '420004', '退款撤销冲正', 'Refund VOID Reversal', 1, 26, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '420005', '转出撤销冲正', 'Transfer Out VOID Reversal', 1, 27, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '420006', '转出撤销冲正', 'Transfer In VOID Reversal', 1, 28, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '090001', '卡验证', 'Card Verification', 1, 29, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_TYPE', NULL, '490001', '卡验证冲正', 'Card Verification Reversal', 1, 30, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEAR_FLAG', NULL, 'N', '未清分', 'To be Cleared', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEAR_FLAG', NULL, 'Y', '已清分', 'Cleared', 1, 2, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_STATUS', NULL, 'PENDING', '处理中', 'PENDING', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_STATUS', NULL, 'SUCCESS', '成功', 'SUCCESS', 1, 2, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_AUTH_TRANS_STATUS', NULL, 'FAILED', '失败', 'FAILED', 1, 3, now(), NULL, now(), NULL);

INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'kl-auth-query' as code,
             'KL授权交易查询'      as name,
             (max(id) + 1)             as id,
             now()                     as create_date,
             null                      as update_date
      from vcc_permission) temp where id >=1;

-- -------------------
-- KL错误码映射表 ------
-- -------------------
insert into kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time)
values ('BPC-GW', 'KLAU1001', 'Illegal Request', '1007', '909', now())
     , ('BPC-GW', 'KLAU1002', 'Duplicated Request', '1007', '913', now())
     , ('BPC-GW', 'KLAU1003', 'Can''t match the original transaction', '9999', '940', now())
     , ('BPC-GW', 'KLAU1004', 'Merchant is not existed', '1004', '112', now())
     , ('BPC-GW', 'KLAU1005', 'Merchant is not active', '2000', '112', now())
     , ('BPC-GW', 'KLAU1006', 'Card is not active', '2021', '112', now())
     , ('BPC-GW', 'KLAU1007', 'Insufficient available balance', '2002', '116', now())
     , ('BPC-GW', 'KLAU1008', 'Transaction not supported', '9999', '902', now())
     , ('BPC-GW', 'KLAU1009', 'Processor not supported', '9999', '916', now())
     , ('BPC-GW', 'KLAU1010', 'Single transaction amount exceeds limit', '9999', '917', now())
     , ('BPC-GW', 'KLAU1011', 'Cumulative transaction amount exceeds limit', '9999', '917', now())
     , ('BPC-GW', 'KLAU1012', 'Transaction count exceeds limit', '9999', '917', now())
     , ('BPC-GW', 'KLAU1013', 'Restricted transactions', '9999', '953', now())
     , ('BPC-GW', 'KLAU1014', 'Transaction type not supported', '9999', '902', now())
     , ('BPC-GW', 'KLAU1015', 'Transaction amount exceeds limit', '9999', '917', now())
     , ('BPC-GW', 'KLAU1016', 'Transaction amount invalid', '9999', '917', now())
     , ('BPC-GW', 'KLAU1017', 'Merchant status invalid', '9999', '957', now())
     , ('BPC-GW', 'KLAU1018', 'Accounting fail', '9999', '112', now())
     , ('BPC-GW', 'KLAU1019', 'Config error, can not be accounting', '9999', '111', now())
     , ('BPC-GW', 'KLAU1020', 'Timeout', '9999', '111', now());

-- KL清算查询权限
INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'kl-clearing-query' as code,
             'KL清算查询'      as name,
             (max(id) + 1)             as id,
             now()                     as create_date,
             null                      as update_date
      from vcc_permission) temp where id >=1;
-- KL清算异常查询权限
INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'kl-clearing-exception-query' as code,
             'KL清算异常查询'      as name,
             (max(id) + 1)             as id,
             now()                     as create_date,
             null                      as update_date
      from vcc_permission) temp where id >=1;
-- KL清算异常转嫁商户权限
INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'kl-clearing-exception-assign' as code,
             'KL清算异常转嫁商户'      as name,
             (max(id) + 1)             as id,
             now()                     as create_date,
             null                      as update_date
      from vcc_permission) temp where id >=1;
-- KL清算异常数据重试权限
INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'kl-clearing-exception-retry' as code,
             '清算异常数据重试'      as name,
             (max(id) + 1)             as id,
             now()                     as create_date,
             null                      as update_date
      from vcc_permission) temp where id >=1;

-- KL清算状态字典
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_STATUS', NULL, 'SUCCESS', '成功', 'SUCCESS', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_STATUS', NULL, 'FAILED', '失败', 'FAILED', 1, 2, now(), NULL, now(), NULL);
-- KL清算异常处理状态
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_EXCEPTION', 'PROCESS_STATUS', 'N', '待处理', 'To be process', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_EXCEPTION', 'PROCESS_STATUS', 'Y', '已处理', 'Processed', 1, 2, now(), NULL, now(), NULL);

-- KL清算异常原因
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_EXCEPTION', 'EXCEPTION_REASON', 'CARD_CANCELLATION', '卡注销', 'Card Cancellation', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_EXCEPTION', 'EXCEPTION_REASON', 'ACCOUNTING_FAIL', '记账失败', 'Journal Entry Failure', 1, 2, now(), NULL, now(), NULL);




CREATE TABLE `vcc_message_send_record` (
                                           `id` bigint NOT NULL AUTO_INCREMENT COMMENT '发送记录主键id',
                                           `source_from` varchar(255)  DEFAULT NULL COMMENT '请求来源，VCC,U+',
                                           `message_type` varchar(32)  DEFAULT NULL COMMENT '发送类型；SMS;Email',
                                           `recipient` varchar(255)  DEFAULT NULL COMMENT '接收方',
                                           `template_no` varchar(32)  DEFAULT NULL COMMENT '模板编号',
                                           `req_param` varchar(255)  DEFAULT NULL COMMENT '请求参数',
                                           `send_status` varchar(32)  DEFAULT NULL COMMENT '发送状态；SUCCESS,FAILED',
                                           `error_message` varchar(255)  DEFAULT NULL COMMENT '错误信息',
                                           `send_time` datetime NULL DEFAULT NULL COMMENT '发送状态',
                                           `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                           PRIMARY KEY (`id`) USING BTREE
) COMMENT='短信/邮件发送记录';
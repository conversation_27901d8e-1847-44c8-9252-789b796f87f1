server:
  port: 8080

spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: nacos-0.nacos-headless.payx.svc.cluster.local:8848,nacos-1.nacos-headless.payx.svc.cluster.local:8848,nacos-2.nacos-headless.payx.svc.cluster.local:8848
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 100MB
  shardingsphere:
    enabled: true
    props:
      #是否输出sql
      sql-show: true
      default-data-source-name: ds0
    datasource:
      names: ds0
      ds0:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: **********************************************************************************************************************
        username: kcardapp
        # 原文密码加密的内容
        password: PAHFQ4JHrq7gEbb+xW5rHVQi1gS1kMtl53yaDy6/WpHC04s4H9KvMnmBFCv8UiyPHckptkNSJkkWo2vaYJIjSg==
        #特别提示:配置数据库加密 config这个不能忘掉
        filters: stat,wall,config
        use-global-data-source-stat: true
        connectionProperties: config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKHGwq7q2RmwuRgKxBypQHw0mYu4BQZ3eMsTrdK8E6igRcxsobUC7uT0SoxIjl1WveWniCASejoQtn/BY6hVKWsCAwEAAQ==
        # 连接池的配置信息
        # 初始化大小，最小空闲连接数，最大活跃数
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
    # 配置分片分表策略
    rules:
      sharding:
  redis:
    host: master.pro-aws-hk-payx-redis-withpass.xlpfwo.ape1.cache.amazonaws.com
    port: 6379
    password: xT!x6vwmJ6SBE2u^
    ssl: true
    database: 8
    timeout: 10000
    # 连接池
    lettuce:
      pool:
        # 最大连接数
        max-active: 8
        # 最大阻塞等待时间(负数表示没限制)
        max-wait: -1
        # 最小空闲
        min-idle: 0
        # 最大空闲
        max-idle: 8

mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: com.kun.linkage.common.db.entity

#启动雪花算法工具
uid:
  enable: true

springdoc:
  api-docs:
    # 开启api-docs
    enabled: false

vcc:
  boss:
    interceptor:
      enabled: true
    account:
      jwtExpiresSecond: 1800000 # 1hour
      jwtSecretString: vcc-boss
      #是否启动Filter过滤
      StartFilter: true
      #是否启动记录Filter所有日志
      StartFilterAllLog: true
-- 备份表数据
create table kc_clearing_info_2025_q3_bak_250727 like kc_clearing_info_2025_q3;
insert into kc_clearing_info_2025_q3_bak_250727 (clearing_id, clearing_no, clearing_file_name, clearing_file_id, channel_source, `system`, clearing_date, auth_id, trans_id, auth_remain_auth_amt, trans_code, auth_date, original_clearing_no, customer_mer_id, card_acceptor_id, card_acceptor_name, card_acceptor_country_code, card_acceptor_tid, transaction_type, transaction_date, transaction_datetime, transaction_currency_no, transaction_currency_code, transaction_currency_precision, clear_amount, reference_no, trace_audit_no, processor_card_id, kcard_id, card_no, card_last_four, cardholder_currency_no, cardholder_currency_code, cardholder_currency_precision, cardholder_amount, acq_arn, auth_amount, difference_flag, transaction_amount_offset, markup_rate, cardholder_markup_amount, cardholder_billing_amount_with_markup, clearing_status, error_flag, error_reason, auth_code, pos_entry_mode_tcr0, acquiring_Identifier_tcr0, card_acceptor_mcc, cpd, intechange_fee_amt, intechange_fee_sign, pos_environment_tcr1, fx_rate_source_tcr5, fx_rate_destination_tcr5, authorization_response_code_tcr5, multiple_clearing_sequence_number_tcr5, multiple_clearing_sequence_count_tcr5, mvv, reversal_flag, processor_request_id, original_processor_request_id, fe_transaction_number, cardholder_billing_amount_offset, cardholder_markup_billing_amount_offset, card_schema_product_id, response_message, create_time, update_time, acquiring_id, clearing_type, remaining_clear_amount, original_clearing_id, auth_remain_bill_amt, auth_remain_bill_amt_with_markup, auth_remain_frozen_amt, auth_markup_rate, notify_flag, notify_results, card_product_code, usage_cod, reason_code, merchant_postal_code, merchant_city) select clearing_id, clearing_no, clearing_file_name, clearing_file_id, channel_source, `system`, clearing_date, auth_id, trans_id, auth_remain_auth_amt, trans_code, auth_date, original_clearing_no, customer_mer_id, card_acceptor_id, card_acceptor_name, card_acceptor_country_code, card_acceptor_tid, transaction_type, transaction_date, transaction_datetime, transaction_currency_no, transaction_currency_code, transaction_currency_precision, clear_amount, reference_no, trace_audit_no, processor_card_id, kcard_id, card_no, card_last_four, cardholder_currency_no, cardholder_currency_code, cardholder_currency_precision, cardholder_amount, acq_arn, auth_amount, difference_flag, transaction_amount_offset, markup_rate, cardholder_markup_amount, cardholder_billing_amount_with_markup, clearing_status, error_flag, error_reason, auth_code, pos_entry_mode_tcr0, acquiring_Identifier_tcr0, card_acceptor_mcc, cpd, intechange_fee_amt, intechange_fee_sign, pos_environment_tcr1, fx_rate_source_tcr5, fx_rate_destination_tcr5, authorization_response_code_tcr5, multiple_clearing_sequence_number_tcr5, multiple_clearing_sequence_count_tcr5, mvv, reversal_flag, processor_request_id, original_processor_request_id, fe_transaction_number, cardholder_billing_amount_offset, cardholder_markup_billing_amount_offset, card_schema_product_id, response_message, create_time, update_time, acquiring_id, clearing_type, remaining_clear_amount, original_clearing_id, auth_remain_bill_amt, auth_remain_bill_amt_with_markup, auth_remain_frozen_amt, auth_markup_rate, notify_flag, notify_results, card_product_code, usage_cod, reason_code, merchant_postal_code, merchant_city from kc_clearing_info_2025_q3 where clearing_no = '2006952386794553344' and acq_arn = '74007035207920004095695';
-- 脏数据处理
update kc_clearing_info_2025_q3 set acq_arn = '-99999901' where clearing_no = '2006952386794553344' and acq_arn = '74007035207920004095695';
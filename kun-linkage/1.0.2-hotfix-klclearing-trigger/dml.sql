-- 纠正字段名错误
alter table kl_clearing_trans_202505 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202506 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202507 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202508 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202509 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202510 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202511 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202512 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202601 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202602 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202603 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202604 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202605 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202606 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202607 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202608 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202609 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202610 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202611 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202612 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202701 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202702 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202703 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202704 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202705 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202706 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202707 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202708 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202709 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202710 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202711 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202712 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202801 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202802 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202803 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202804 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202805 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202806 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202807 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202808 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202809 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202810 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202811 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202812 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202901 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202902 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202903 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202904 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202905 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202906 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202907 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202908 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202909 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202910 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202911 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_202912 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_203001 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_203002 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_203003 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_203004 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_203005 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_203006 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_203007 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_203008 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_203009 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_203010 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_203011 rename column gateway_request_no to gateway_request_id;
alter table kl_clearing_trans_203012 rename column gateway_request_no to gateway_request_id;
-- 创建手动触发参数表
create table common_manual_trigger_param(
                                            id bigint auto_increment primary key comment '主键',
                                            param longtext default null comment '参数内容',
                                            create_time datetime not null default current_timestamp comment '创建时间',
                                            update_time datetime not null default current_timestamp on update current_timestamp comment '更新时间'
) comment '手动触发参数表';
-- 插入数据
insert into common_manual_trigger_param (param) values ('{"acquireReferenceNo":"520607437312","approveCode":"XTO1PE","arn":"74143255206100006222858","cardAcceptorCity":"SINGAPORE    ","cardAcceptorCountryCode":"SG","cardAcceptorId":"NJMQHOWSS4QZVGD","cardAcceptorName":"LUCKINCOFFEE","cardAcceptorTid":"4YI1CG8K","cardProductCode":"VISA Purchase","cardSchemaProductId":"S","cardholderBillingAmount":3.45,"cardholderBillingAmountOffset":0,"cardholderBillingCurrency":"USD","cardholderCurrencyExponent":2,"cardholderMarkupBillingAmount":3.47,"cardholderMarkupBillingAmountOffset":0,"clearAmount":4.40,"clearingDate":"20250726","clearingType":"1050","conversionRateCardholderBilling":0.7840909,"feeInterchangeAmount":0.069000,"feeInterchangeSign":"C","gatewayCardId":"K-cfee9e60c8014488abaacf3476fd5c00","gatewayClearingId":"1948790360980918274","markupAmount":0.02,"markupRate":0.0050,"maskedCardNo":"441359******8133","mcc":"5814","merchantNo":"12066104","posEntryMode":"01","processor":"BPC-GW","processorCardId":"448528891VC00000001","processorExt1":"250725000490575470","processorRequestId":"6ad1b6d2-9f26-4e52-9b9a-04dbebe2751a","processorTransId":"2507251502121881035","reasonCode":"00","requestId":"a6ddc50b-b56c-4657-ad01-fa8b7b3032ae","responseCode":"  ","responseMessage":"Success","reversalFlag":"0","systemsTraceAuditNumber":"437312","transAmount":4.400,"transAmountOffset":0,"transCurrency":"SGD","transCurrencyExponent":2,"transDate":"20250725","transType":"010000","transactionLocalDatetime":"20250725150212","usageCode":"1"}');
ALTER TABLE kl_organization_basic_info_review_record
ADD COLUMN is_kyc_reported TINYINT NULL DEFAULT 0 COMMENT '是否个人客户kyc报送;0否;1:是' AFTER check_customer_account_flag,
ADD COLUMN is_kyc_verified TINYINT NULL DEFAULT 0 COMMENT '是否个人客户kyc校验;0否;1:是' AFTER is_kyc_reported;

ALTER TABLE kl_organization_basic_info
ADD COLUMN is_kyc_reported TINYINT NULL DEFAULT 0 COMMENT '是否个人客户kyc报送;0否;1:是' AFTER check_customer_account_flag,
ADD COLUMN is_kyc_verified TINYINT NULL DEFAULT 0 COMMENT '是否个人客户kyc校验;0否;1:是' AFTER is_kyc_reported;


CREATE TABLE `kl_customer_kyc_audit_record` (
                                                `audit_id` bigint NOT NULL COMMENT '主键 id',
                                                `request_no` varchar(64) NOT NULL COMMENT '请求流水号',
                                                `customer_id` varchar(64) NOT NULL COMMENT '用户id',
                                                `organization_no` varchar(64) NOT NULL COMMENT '机构号',
                                                `case_no` varchar(64) NOT NULL COMMENT '案件号',
                                                `case_type` varchar(12)  DEFAULT NULL COMMENT '案件类型 申卡前置报送、KYC报送、kyc升级、资料修改',
                                                `kyc_level` varchar(64)  DEFAULT NULL COMMENT 'KYC等级 无、零级、 一级、二级、三级',
                                                `cardholder_name` varchar(128) DEFAULT NULL COMMENT '持卡人姓名',
                                                `hit_result` varchar(12)  DEFAULT NULL COMMENT '命中结果 无、未命中、疑似命中',
                                                `case_status` varchar(12) DEFAULT NULL COMMENT '案件状态 待审核、通过、拒绝、待补充',
                                                `id_type` varchar(16)  DEFAULT NULL COMMENT '证件类型',
                                                `id_no` varchar(255)  DEFAULT NULL COMMENT '加密,证件号',
                                                `email` varchar(255)  DEFAULT NULL COMMENT '邮箱',
                                                `phone_area` varchar(5)  DEFAULT NULL COMMENT '手机区号',
                                                `mobile_no` varchar(32)  DEFAULT NULL COMMENT '手机号码',
                                                `audit_description` varchar(128) DEFAULT NULL COMMENT '审核描述',
                                                `remark` varchar(128) DEFAULT NULL COMMENT '备注',
                                                `card_product_no` varchar(64) DEFAULT NULL COMMENT '卡产品号',
                                                `card_product_name` varchar(128) DEFAULT NULL COMMENT '卡产品名称',
                                                `create_user` varchar(32)  DEFAULT NULL COMMENT '创建人',
                                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                `update_user` varchar(32) DEFAULT NULL COMMENT '更新人',
                                                `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                PRIMARY KEY (`audit_id`),
                                                UNIQUE KEY `idx_uk_case_no` (`case_no`),
                                                UNIQUE KEY `idx_req_customer_org` (`request_no`,`customer_id`,`organization_no`),
                                                KEY `idx_customer_id` (`customer_id`),
                                                KEY `idx_organization_no` (`organization_no`)
)  COMMENT='kyc信息审核表记录';


CREATE TABLE `kl_customer_kyc_level1_info` (
                                               `kyc_level1_id` bigint NOT NULL COMMENT 'kyc一级认证信息主键ID',
                                               `customer_id` varchar(64)  NOT NULL COMMENT '用户id',
                                               `organization_no` varchar(64)  NOT NULL COMMENT '机构号',
                                               `case_no` varchar(64) NOT NULL COMMENT '案件号',
                                               `operation_type` varchar(32) DEFAULT NULL COMMENT '操作类型;开卡申请;主动报送',
                                               `is_complete` int DEFAULT NULL COMMENT '数据是否完整;主动报送进来的数据1;开卡新增的数据0',
                                               `last_name` varchar(64) DEFAULT NULL COMMENT '姓',
                                               `middle_name` varchar(64) DEFAULT NULL COMMENT '中间名',
                                               `first_name` varchar(64) DEFAULT NULL COMMENT '名',
                                               `id_type` varchar(16) DEFAULT NULL COMMENT '证件类型',
                                               `id_no` varchar(255) DEFAULT NULL COMMENT '加密,证件号',
                                               `masked_id_no` varchar(64) DEFAULT NULL COMMENT '脱敏的证件号',
                                               `birth_date` varchar(10) DEFAULT NULL COMMENT '出生日期;yyyy/mm/dd',
                                               `gender` int DEFAULT NULL COMMENT '性别;1:男;2:女',
                                               `id_issue_date` varchar(10) DEFAULT NULL COMMENT '证件签发日',
                                               `id_expiry_date` varchar(10) DEFAULT NULL COMMENT '证件有效期 ',
                                               `nationality` varchar(32) DEFAULT NULL COMMENT '国籍',
                                               `country_code` varchar(3) DEFAULT NULL COMMENT '国家代码,3位字母',
                                               `country_no` varchar(3) DEFAULT NULL COMMENT '国家地区代码,3位数字',
                                               `id_card_front_image_upload_id` varchar(255) DEFAULT NULL COMMENT '证件正面地址上传id',
                                               `id_card_front_image` varchar(256) DEFAULT NULL COMMENT '证件正面地址',
                                               `face_photo_image_upload_id` varchar(255) DEFAULT NULL COMMENT '人脸图片地址上传id',
                                               `face_photo_image` varchar(256) DEFAULT NULL COMMENT '人脸图片地址',
                                               `email` varchar(255) DEFAULT NULL COMMENT '邮箱',
                                               `phone_area` varchar(5) DEFAULT NULL COMMENT '手机区号',
                                               `mobile_no` varchar(32) DEFAULT NULL COMMENT '手机号码',
                                               `residence_country_code` varchar(3) DEFAULT NULL COMMENT '居住地国家/地区;3位字母',
                                               `residence_state_province` varchar(255) DEFAULT NULL COMMENT '居住地州/省',
                                               `residence_city` varchar(255) DEFAULT NULL COMMENT '居住地城市',
                                               `residence_address_detail` varchar(512) DEFAULT NULL COMMENT '居住地详细地址',
                                               `postal_code` varchar(16) DEFAULT NULL COMMENT '邮编',
                                               `submission_time` datetime DEFAULT NULL COMMENT '报送时间',
                                               `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                               `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
                                               PRIMARY KEY (`kyc_level1_id`),
                                               KEY `idx_organization_no` (`organization_no`),
                                               KEY `idx_customer_id` (`customer_id`),
                                               KEY `idx_case_no` (`case_no`)
)  COMMENT='kyc一级认证信息记录';


CREATE TABLE `kl_customer_kyc_level1_record` (
                                                 `kyc_level1_id` bigint NOT NULL COMMENT 'kyc一级认证信息主键ID',
                                                 `customer_id` varchar(64)  NOT NULL COMMENT '用户id',
                                                 `organization_no` varchar(64)  NOT NULL COMMENT '机构号',
                                                 `case_no` varchar(64) NOT NULL COMMENT '案件号',
                                                 `last_name` varchar(64) DEFAULT NULL COMMENT '姓',
                                                 `middle_name` varchar(64) DEFAULT NULL COMMENT '中间名',
                                                 `first_name` varchar(64) DEFAULT NULL COMMENT '名',
                                                 `id_type` varchar(16) DEFAULT NULL COMMENT '证件类型',
                                                 `id_no` varchar(255) DEFAULT NULL COMMENT '加密,证件号',
                                                 `masked_id_no` varchar(64) DEFAULT NULL COMMENT '脱敏的证件号',
                                                 `birth_date` varchar(10) DEFAULT NULL COMMENT '出生日期;yyyy/mm/dd',
                                                 `gender` int DEFAULT NULL COMMENT '性别;1:男;2:女',
                                                 `id_issue_date` varchar(10) DEFAULT NULL COMMENT '证件签发日',
                                                 `id_expiry_date` varchar(10) DEFAULT NULL COMMENT '证件有效期 ',
                                                 `nationality` varchar(32) DEFAULT NULL COMMENT '国籍',
                                                 `country_code` varchar(3) DEFAULT NULL COMMENT '国家代码,3位字母',
                                                 `country_no` varchar(3) DEFAULT NULL COMMENT '国家地区代码,3位数字',
                                                 `id_card_front_image_upload_id` varchar(255) DEFAULT NULL COMMENT '证件正面地址上传id',
                                                 `id_card_front_image` varchar(256) DEFAULT NULL COMMENT '证件正面地址',
                                                 `face_photo_image_upload_id` varchar(255) DEFAULT NULL COMMENT '人脸图片地址上传id',
                                                 `face_photo_image` varchar(256) DEFAULT NULL COMMENT '人脸图片地址',
                                                 `email` varchar(255) DEFAULT NULL COMMENT '邮箱',
                                                 `phone_area` varchar(5) DEFAULT NULL COMMENT '手机区号',
                                                 `mobile_no` varchar(32) DEFAULT NULL COMMENT '手机号码',
                                                 `residence_country_code` varchar(3) DEFAULT NULL COMMENT '居住地国家/地区;3位字母',
                                                 `residence_state_province` varchar(255) DEFAULT NULL COMMENT '居住地州/省',
                                                 `residence_city` varchar(255) DEFAULT NULL COMMENT '居住地城市',
                                                 `residence_address_detail` varchar(512) DEFAULT NULL COMMENT '居住地详细地址',
                                                 `postal_code` varchar(16) DEFAULT NULL COMMENT '邮编',
                                                 `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                                 `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
                                                 PRIMARY KEY (`kyc_level1_id`),
                                                 UNIQUE KEY `idx_case_no` (`case_no`),
                                                 KEY `idx_customer_id` (`customer_id`),
                                                 KEY `idx_organization_no` (`organization_no`)
)  COMMENT='kyc一级认证信息记录';


CREATE TABLE `kl_customer_kyc_level2_info` (
                                               `kyc_level2_id` bigint NOT NULL COMMENT '自增主键id',
                                               `customer_id` varchar(64)  NOT NULL COMMENT '用户id',
                                               `organization_no` varchar(64)  NOT NULL COMMENT '机构号',
                                               `case_no` varchar(64) NOT NULL COMMENT '案件号',
                                               `address_proof_upload_ids` varchar(255) DEFAULT NULL COMMENT '地址证明图片上传ids',
                                               `address_proof_photo_1` varchar(255) DEFAULT NULL COMMENT '地址证明图片1',
                                               `address_proof_photo_2` varchar(255) DEFAULT NULL COMMENT '地址证明图片2',
                                               `address_proof_photo_3` varchar(255) DEFAULT NULL COMMENT '地址证明图片3',
                                               `submission_time` datetime DEFAULT NULL COMMENT '报送时间',
                                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                               `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                               PRIMARY KEY (`kyc_level2_id`),
                                               KEY `idx_case_no` (`case_no`),
                                               KEY `idx_customer_id` (`customer_id`),
                                               KEY `idx_organization_no` (`organization_no`)
)  COMMENT='kyc二级认证信息记录';


CREATE TABLE `kl_customer_kyc_level2_record` (
                                                 `kyc_level2_id` bigint NOT NULL COMMENT '自增主键id',
                                                 `customer_id` varchar(64)  NOT NULL COMMENT '用户id',
                                                 `organization_no` varchar(64)  NOT NULL COMMENT '机构号',
                                                 `case_no` varchar(64) NOT NULL COMMENT '案件号',
                                                 `address_proof_upload_ids` varchar(255) DEFAULT NULL COMMENT '地址证明图片上传ids',
                                                 `address_proof_photo_1` varchar(255) DEFAULT NULL COMMENT '地址证明图片1',
                                                 `address_proof_photo_2` varchar(255) DEFAULT NULL COMMENT '地址证明图片2',
                                                 `address_proof_photo_3` varchar(255) DEFAULT NULL COMMENT '地址证明图片3',
                                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                 PRIMARY KEY (`kyc_level2_id`),
                                                 KEY `idx_case_no` (`case_no`),
                                                 KEY `idx_customer_id` (`customer_id`),
                                                 KEY `idx_organization_no` (`organization_no`)
)  COMMENT='kyc二级认证信息记录';


CREATE TABLE `kl_customer_kyc_level3_info` (
                                               `kyc_level3_id` bigint NOT NULL COMMENT '自增主键id',
                                               `customer_id` varchar(64)  NOT NULL COMMENT '用户id',
                                               `organization_no` varchar(64)  NOT NULL COMMENT '机构号',
                                               `case_no` varchar(64) NOT NULL COMMENT '案件号',
                                               `employment_status` varchar(32) DEFAULT NULL COMMENT '雇佣状态；枚举',
                                               `company_name` varchar(32) DEFAULT NULL COMMENT '公司名称',
                                               `industry` varchar(32) DEFAULT NULL COMMENT '行业;枚举',
                                               `position` varchar(32) DEFAULT NULL COMMENT '职位;枚举',
                                               `years_of_experience` varchar(32) DEFAULT NULL COMMENT '工作年限；枚举',
                                               `annual_income` varchar(32) DEFAULT NULL COMMENT '年收入;枚举',
                                               `asset_proof_upload_ids` varchar(255) DEFAULT NULL COMMENT '资产证明图片上传ids;'',''号分割',
                                               `asset_proof` varchar(512) DEFAULT NULL COMMENT '资产证明；枚举集合;'',''号分割',
                                               `asset_proof_other_notes` varchar(512) DEFAULT NULL COMMENT '资产证明其他说明',
                                               `asset_proof_document_upload_ids` varchar(255) DEFAULT NULL COMMENT '资产证明文件上传ids;'',''号分割',
                                               `asset_proof_document` text COMMENT '资产证明文件url集合;'',''分割',
                                               `cryptocurrency_funding_source` varchar(255) DEFAULT NULL COMMENT '数币资金来源;枚举集合;‘，’分割',
                                               `cryptocurrency_funding_source_other_notes` varchar(255) DEFAULT NULL COMMENT '数币资金来源其他说明',
                                               `cryptocurrency_funding_source_document_upload_ids` varchar(255) DEFAULT NULL COMMENT '数币资金来源证明文件上传ids;'',''号分割',
                                               `cryptocurrency_funding_source_document` text COMMENT '数币资金来源证明文件url集合;'',''分割',
                                               `submission_time` datetime DEFAULT NULL COMMENT '报送时间',
                                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                               `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                               PRIMARY KEY (`kyc_level3_id`),
                                               KEY `idx_case_no` (`case_no`),
                                               KEY `idx_customer_id` (`customer_id`),
                                               KEY `idx_organization_no` (`organization_no`)
)  COMMENT='kyc三级认证信息记录';


CREATE TABLE `kl_customer_kyc_level3_record` (
                                                 `kyc_level3_id` bigint NOT NULL COMMENT '自增主键id',
                                                 `customer_id` varchar(64)  NOT NULL COMMENT '用户id',
                                                 `organization_no` varchar(64)  NOT NULL COMMENT '机构号',
                                                 `case_no` varchar(64) NOT NULL COMMENT '案件号',
                                                 `employment_status` varchar(32) DEFAULT NULL COMMENT '雇佣状态；枚举',
                                                 `company_name` varchar(32) DEFAULT NULL COMMENT '公司名称',
                                                 `industry` varchar(32) DEFAULT NULL COMMENT '行业;枚举',
                                                 `position` varchar(32) DEFAULT NULL COMMENT '职位;枚举',
                                                 `years_of_experience` varchar(32) DEFAULT NULL COMMENT '工作年限；枚举',
                                                 `annual_income` varchar(32) DEFAULT NULL COMMENT '年收入;枚举',
                                                 `asset_proof_upload_ids` varchar(255) DEFAULT NULL COMMENT '资产证明图片上传ids;'',''号分割',
                                                 `asset_proof` varchar(512) DEFAULT NULL COMMENT '资产证明；枚举集合;'',''号分割',
                                                 `asset_proof_other_notes` varchar(512) DEFAULT NULL COMMENT '资产证明其他说明',
                                                 `asset_proof_document_upload_ids` varchar(255) DEFAULT NULL COMMENT '资产证明文件上传ids;'',''号分割',
                                                 `asset_proof_document` text COMMENT '资产证明文件url集合;'',''分割',
                                                 `cryptocurrency_funding_source` varchar(255) DEFAULT NULL COMMENT '数币资金来源;枚举集合;‘，’分割',
                                                 `cryptocurrency_funding_source_other_notes` varchar(255) DEFAULT NULL COMMENT '数币资金来源其他说明',
                                                 `cryptocurrency_funding_source_document_upload_ids` varchar(255) DEFAULT NULL COMMENT '数币资金来源证明文件上传ids;'',''号分割',
                                                 `cryptocurrency_funding_source_document` text COMMENT '数币资金来源证明文件url集合;'',''分割',
                                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                 PRIMARY KEY (`kyc_level3_id`),
                                                 UNIQUE KEY `idx_case_no` (`case_no`) USING BTREE,
                                                 KEY `idx_organization_no` (`organization_no`),
                                                 KEY `idx_customer_id` (`customer_id`)
)  COMMENT='kyc三级认证信息记录';


INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select *
from (select 'boss-kl-customer-kyc' as code,
             '机构客户kyc管理'           as name,
             (max(id) + 1)              as id,
             now()                      as create_date,
             null                       as update_date
      from vcc_permission) temp;


---------- 新增字典数据 ----------

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`)
VALUES
    ('KYC_CASE_TYPE', 'PRE_CARD_APPLICATION', 'Pre-card application submission', '申卡前置报送', 1, 1, NOW(), 0),
    ('KYC_CASE_TYPE', 'KYC_REPORT', 'KYC submission', 'KYC报送', 1, 2, NOW(), 0),
    ('KYC_CASE_TYPE', 'UPGRADE_KYC', 'KYC upgrade', 'KYC升级', 1, 3, NOW(), 0),
    ('KYC_CASE_TYPE', 'MODIFY_INFO', 'Information modification', '资料修改', 1, 4, NOW(), 0);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`)
VALUES
    ('KYC_LEVEL', 'NONE', 'None', '无', 1, 0, NOW(), 0),
    ('KYC_LEVEL', 'LEVEL_0', 'Level 0', '零级', 1, 1, NOW(), 0),
    ('KYC_LEVEL', 'LEVEL_1', 'Level 1', '一级', 1, 2, NOW(), 0),
    ('KYC_LEVEL', 'LEVEL_2', 'Level 2', '二级', 1, 3, NOW(), 0),
    ('KYC_LEVEL', 'LEVEL_3', 'Level 3', '三级', 1, 4, NOW(), 0);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`)
VALUES
    ('ID_NUMBER_TYPE', 'id_card', 'ID card', '身份证', 1, 1, NOW(), 0),
    ('ID_NUMBER_TYPE', 'passport', 'Passport', '护照', 1, 2, NOW(), 0);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`)
VALUES
    ('GENDER', '1', 'Male', '男', 1, 1, NOW(), 0),
    ('GENDER', '2', 'Female', '女', 1, 2, NOW(), 0);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`)
VALUES
    ('INDUSTRY', 'internet_tech_ai', 'Internet / Technology / AI', '互联网/技术服务/AI', 1, 1, NOW(), 0),
    ('INDUSTRY', 'digital_currency', 'Digital Currency / Payment Industry', '支付/数字货币行业', 1, 2, NOW(), 0),
    ('INDUSTRY', 'finance', 'Finance', '金融业', 1, 3, NOW(), 0),
    ('INDUSTRY', 'consulting_services', 'Professional Consulting Services', '专业咨询服务', 1, 4, NOW(), 0),
    ('INDUSTRY', 'agriculture_forestry', 'Agriculture / Forestry / Animal Husbandry / Fishery', '农/林/畜牧/渔业', 1, 5, NOW(), 0),
    ('INDUSTRY', 'energy_chemical_mining', 'Energy / Chemical / Mining / Environmental Protection', '能源/化工/采矿/环保', 1, 6, NOW(), 0),
    ('INDUSTRY', 'manufacturing', 'Manufacturing', '制造业', 1, 7, NOW(), 0),
    ('INDUSTRY', 'real_estate_constr', 'Real Estate / Construction', '房地产/建筑', 1, 8, NOW(), 0),
    ('INDUSTRY', 'education_services', 'Education Services', '教育服务', 1, 9, NOW(), 0),
    ('INDUSTRY', 'healthcare', 'Healthcare', '医疗健康', 1, 10, NOW(), 0),
    ('INDUSTRY', 'consumer_goods', 'Consumer Goods / Wholesale Retail', '消费品/批发零售业', 1, 11, NOW(), 0),
    ('INDUSTRY', 'tourism_accomm_food', 'Tourism / Accommodation / Food Services', '旅游/住宿/餐饮业', 1, 12, NOW(), 0),
    ('INDUSTRY', 'trade_logistics', 'Trade / Logistics / Transportation', '贸易/物流运输业', 1, 13, NOW(), 0),
    ('INDUSTRY', 'advertising_media', 'Advertising / Media / Culture / Sports', '广告/传媒/文化/体育', 1, 14, NOW(), 0),
    ('INDUSTRY', 'gov_nonprofit_other', 'Government / Non-profit Organizations / Other', '政府/非盈利机构/其他', 1, 15, NOW(), 0);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`)
VALUES
    ('POSITION', 'shareholder_director', 'Shareholder / Director / Partner', '股东/董事/合伙人', 1, 1, NOW(), 0),
    ('POSITION', 'executive', 'Company Executive', '公司高管', 1, 2, NOW(), 0),
    ('POSITION', 'manager', 'Manager (Department/Project/Regional)', '部门/项目/区域经理', 1, 3, NOW(), 0),
    ('POSITION', 'sales_specialist', 'Sales / Specialist', '销售/专员', 1, 4, NOW(), 0),
    ('POSITION', 'engineer_designer', 'Engineer / Designer / Doctor / Teacher / Lawyer and other Professionals', '工程师/设计师/医生/教师/律师等专业技术人员', 1, 5, NOW(), 0),
    ('POSITION', 'actor', 'Actor', '演员', 1, 6, NOW(), 0),
    ('POSITION', 'intern', 'Intern', '实习生', 1, 7, NOW(), 0),
    ('POSITION', 'freelancer', 'Freelancer', '自由职业', 1, 8, NOW(), 0);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`)
VALUES
    ('Years of Work Experience', '0_5_years', '0-5 Years', '0-5年', 1, 1, NOW(), 0),
    ('Years of Work Experience', '6_10_years', '6-10 Years', '6-10年', 1, 2, NOW(), 0),
    ('Years of Work Experience', '11_20_years', '11-20 Years', '11-20年', 1, 3, NOW(), 0),
    ('Years of Work Experience', 'over_20_years', 'Over 20 Years', '20年以上', 1, 4, NOW(), 0);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`)
VALUES
    ('Annual Income', 'under_50k_usd', 'Under 50,000 USD', '50000美金以下', 1, 1, NOW(), 0),
    ('Annual Income', '50k_99k_usd', '50,000 - 99,999 USD', '50000-99999美金', 1, 2, NOW(), 0),
    ('Annual Income', '100k_199k_usd', '100,000 - 199,999 USD', '100000-199999美金', 1, 3, NOW(), 0),
    ('Annual Income', '200k_299k_usd', '200,000 - 299,999 USD', '200000-299999美金', 1, 4, NOW(), 0),
    ('Annual Income', 'over_300k_usd', 'Over 300,000 USD', '300000美金以上', 1, 5, NOW(), 0);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`)
VALUES
    ('KYC_CASE_STATUS', 'NONE', 'No review required', '无需审核', 1, 1, NOW(), 0),
    ('KYC_CASE_STATUS', 'PENDING', 'Pending review', '待审核', 1, 2, NOW(), 0),
    ('KYC_CASE_STATUS', 'APPROVED', 'Approved', '通过', 1, 3, NOW(), 0),
    ('KYC_CASE_STATUS', 'REJECTED', 'Rejected', '拒绝', 1, 4, NOW(), 0),
    ('KYC_CASE_STATUS', 'SUPPLEMENT', 'Pending supplement', '待补充', 1, 5, NOW(), 0);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`)
VALUES
    ('KYC_HIT_RESULT', 'NONE', 'None', '无', 1, 1, NOW(), 0),
    ('KYC_HIT_RESULT', 'NOT_HIT', 'Not hit', '未命中', 1, 2, NOW(), 0),
    ('KYC_HIT_RESULT', 'SUSPICIOUS', 'Suspicious', '疑似命中', 1, 3, NOW(), 0);

-- 机构客户表添加创建用户id,最后一次更新人 --
ALTER TABLE `kl_organization_customer_account_info_0`
ADD COLUMN `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建用户id' AFTER `last_modify_time`,
ADD COLUMN `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建用户名' AFTER `create_user_id`,
ADD COLUMN `last_modify_user_id` varchar(32) NULL COMMENT '最后一次更新人id' AFTER `create_user_name`,
ADD COLUMN `last_modify_user_name` varchar(128) NULL COMMENT '最后一次更新用户名' AFTER `last_modify_user_id`;
ALTER TABLE `kl_organization_customer_account_info_1`
ADD COLUMN `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建用户id' AFTER `last_modify_time`,
ADD COLUMN `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建用户名' AFTER `create_user_id`,
ADD COLUMN `last_modify_user_id` varchar(32) NULL COMMENT '最后一次更新人id' AFTER `create_user_name`,
ADD COLUMN `last_modify_user_name` varchar(128) NULL COMMENT '最后一次更新用户名' AFTER `last_modify_user_id`;
ALTER TABLE `kl_organization_customer_account_info_2`
ADD COLUMN `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建用户id' AFTER `last_modify_time`,
ADD COLUMN `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建用户名' AFTER `create_user_id`,
ADD COLUMN `last_modify_user_id` varchar(32) NULL COMMENT '最后一次更新人id' AFTER `create_user_name`,
ADD COLUMN `last_modify_user_name` varchar(128) NULL COMMENT '最后一次更新用户名' AFTER `last_modify_user_id`;
ALTER TABLE `kl_organization_customer_account_info_3`
ADD COLUMN `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建用户id' AFTER `last_modify_time`,
ADD COLUMN `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建用户名' AFTER `create_user_id`,
ADD COLUMN `last_modify_user_id` varchar(32) NULL COMMENT '最后一次更新人id' AFTER `create_user_name`,
ADD COLUMN `last_modify_user_name` varchar(128) NULL COMMENT '最后一次更新用户名' AFTER `last_modify_user_id`;
ALTER TABLE `kl_organization_customer_account_info_4`
ADD COLUMN `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建用户id' AFTER `last_modify_time`,
ADD COLUMN `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建用户名' AFTER `create_user_id`,
ADD COLUMN `last_modify_user_id` varchar(32) NULL COMMENT '最后一次更新人id' AFTER `create_user_name`,
ADD COLUMN `last_modify_user_name` varchar(128) NULL COMMENT '最后一次更新用户名' AFTER `last_modify_user_id`;

ALTER TABLE `kl_organization_customer_account_info_5`
ADD COLUMN `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建用户id' AFTER `last_modify_time`,
ADD COLUMN `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建用户名' AFTER `create_user_id`,
ADD COLUMN `last_modify_user_id` varchar(32) NULL COMMENT '最后一次更新人id' AFTER `create_user_name`,
ADD COLUMN `last_modify_user_name` varchar(128) NULL COMMENT '最后一次更新用户名' AFTER `last_modify_user_id`;

ALTER TABLE `kl_organization_customer_account_info_6`
ADD COLUMN `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建用户id' AFTER `last_modify_time`,
ADD COLUMN `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建用户名' AFTER `create_user_id`,
ADD COLUMN `last_modify_user_id` varchar(32) NULL COMMENT '最后一次更新人id' AFTER `create_user_name`,
ADD COLUMN `last_modify_user_name` varchar(128) NULL COMMENT '最后一次更新用户名' AFTER `last_modify_user_id`;


ALTER TABLE `kl_organization_customer_account_info_7`
ADD COLUMN `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建用户id' AFTER `last_modify_time`,
ADD COLUMN `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建用户名' AFTER `create_user_id`,
ADD COLUMN `last_modify_user_id` varchar(32) NULL COMMENT '最后一次更新人id' AFTER `create_user_name`,
ADD COLUMN `last_modify_user_name` varchar(128) NULL COMMENT '最后一次更新用户名' AFTER `last_modify_user_id`;


-- 卡充值明细表 --
CREATE TABLE `kl_card_recharge_detail_2025Q1` (
                                                  `id` varchar(32) NOT NULL COMMENT '主键ID',
                                                  `organization_no` varchar(16) DEFAULT NULL COMMENT '机构号',
                                                  `business_type` varchar(32) DEFAULT NULL COMMENT '业务类型',
                                                  `customer_id` varchar(64) DEFAULT NULL COMMENT '客户号',
                                                  `request_no` varchar(64) DEFAULT NULL COMMENT '请求流水号',
                                                  `card_id` varchar(64) DEFAULT NULL COMMENT '卡id',
                                                  `recharge_datetime` datetime DEFAULT NULL COMMENT '充值日期时间',
                                                  `recharge_amount` decimal(20,3) DEFAULT 0 COMMENT '充值金额(卡片币种)',
                                                  `recharge_currency_code` varchar(3) DEFAULT NULL COMMENT '充值币种(卡片币种)',
                                                  `recharge_currency_precision` int DEFAULT 2 COMMENT '充值币种精度(卡片币种)',
                                                  `recharge_bookkeep_request_no` varchar(64) DEFAULT NULL COMMENT '用户上账记账请求单号(账户中的业务系统流水号)',
                                                  `recharge_bookkeep_status` tinyint DEFAULT 0 COMMENT '用户上账记账状态:0:未记账或已冲账或记账明确失败;1:已记账;2:无需记账;3:未知',
                                                  `fx_rate` decimal(10,5) DEFAULT 1 COMMENT '换汇汇率',
                                                  `deduct_currency_code` varchar(16) DEFAULT NULL COMMENT '扣除币种',
                                                  `deduct_processor` varchar(16) DEFAULT NULL COMMENT '扣除处理方(KUN和PAYX)',
                                                  `deduct_currency_precision` int DEFAULT NULL COMMENT '扣除币种精度',
                                                  `deduct_principal_amount` decimal(30,16) DEFAULT 0 COMMENT '扣除本金金额',
                                                  `deduct_principal_bookkeep_request_no` varchar(64) DEFAULT NULL COMMENT '扣除本金记账请求单号(kun或payx中的requestNo)',
                                                  `deduct_principal_bookkeep_status` tinyint DEFAULT 0 COMMENT '扣除本金记账状态:0:未记账或已冲账或记账明确失败;1:已记账;2:无需记账;3:未知',
                                                  `deduct_recharge_fee_amount` decimal(30,16) DEFAULT 0 COMMENT '扣除充值手续费金额',
                                                  `deduct_recharge_fee_bookkeep_request_no` varchar(64) DEFAULT NULL COMMENT '扣除充值手续费记账请求单号(kun或payx中的requestNo)',
                                                  `deduct_recharge_fee_bookkeep_status` tinyint DEFAULT 0 COMMENT '扣除充值手续费记账状态:0:未记账或已冲账或记账明确失败;1:已记账;2:无需记账;3:未知',
                                                  `deduct_recharge_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '扣除充值手续费费用明细记录id',
                                                  `deduct_acceptance_fee_amount` decimal(30,16) DEFAULT 0 COMMENT '扣除充值承兑费金额',
                                                  `deduct_acceptance_fee_bookkeep_request_no` varchar(64) DEFAULT NULL COMMENT '扣除充值承兑费记账请求单号(kun或payx中的requestNo)',
                                                  `deduct_acceptance_fee_bookkeep_status` tinyint DEFAULT 0 COMMENT '扣除充值承兑费记账状态:0:未记账或已冲账或记账明确失败;1:已记账;2:无需记账;3:未知',
                                                  `deduct_acceptance_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '扣除充值承兑费费用明细记录id',
                                                  `deduct_total_amount` decimal(30,16) DEFAULT 0 COMMENT '扣除总金额',
                                                  `recharge_status` varchar(16) DEFAULT NULL COMMENT '充值状态:SUCCESS,FAIL,PENDING',
                                                  `fail_message` varchar(128) DEFAULT NULL COMMENT '失败信息',
                                                  `bookkeep_reversal_count` int DEFAULT 0 COMMENT '记账冲正次数',
                                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                  `last_modify_time` datetime DEFAULT NULL COMMENT '最后一次修改时间',
                                                  PRIMARY KEY (`id`),
                                                  KEY idx_on_rn_ci(`organization_no`,`request_no`,`customer_id`),
                                                  KEY idx_on_ci_ci(`organization_no`,`customer_id`,`card_id`)
) COMMENT='卡充值明细表(按季度分表)';
CREATE TABLE `kl_card_recharge_detail_2025Q2` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2025Q3` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2025Q4` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2026Q1` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2026Q2` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2026Q3` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2026Q4` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2027Q1` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2027Q2` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2027Q3` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2027Q4` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2028Q1` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2028Q2` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2028Q3` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2028Q4` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2029Q1` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2029Q2` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2029Q3` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2029Q4` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2030Q1` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2030Q2` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2030Q3` LIKE `kl_card_recharge_detail_2025Q1`;
CREATE TABLE `kl_card_recharge_detail_2030Q4` LIKE `kl_card_recharge_detail_2025Q1`;



-- 机构费率模版表 --
CREATE TABLE `kl_organization_fee_template` (
                                                `template_no` varchar(32) NOT NULL COMMENT '模版号',
                                                `template_name` varchar(256) DEFAULT NULL COMMENT '模版名称',
                                                `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                                `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建人名称',
                                                `last_modify_time` datetime DEFAULT NULL COMMENT '最后一次修改时间',
                                                `last_modify_user_id` varchar(32) DEFAULT NULL COMMENT '最后一次修改人id',
                                                `last_modify_user_name` varchar(128) DEFAULT NULL COMMENT '最后一次修改人名称',
                                                PRIMARY KEY (`template_no`)
) COMMENT='机构费率模版表';

-- 机构费率模版明细表 --
CREATE TABLE `kl_organization_fee_template_detail` (
                                                       `template_detail_id` varchar(32) NOT NULL COMMENT '模版明细id',
                                                       `template_no` varchar(32) DEFAULT NULL COMMENT '所属模版号',
                                                       `fee_type` varchar(4) DEFAULT NULL COMMENT '手续费类型',
                                                       `billing_dimension` varchar(32) DEFAULT NULL COMMENT '计费维度',
                                                       `collection_method` varchar(32) DEFAULT NULL COMMENT '收取方式',
                                                       `currency_code` varchar(8) DEFAULT NULL COMMENT '币种',
                                                       `min_amount` decimal(14,2) DEFAULT 0 COMMENT '金额区间:最小金额',
                                                       `max_amount` decimal(14,2) DEFAULT 0 COMMENT '金额区间:最大金额',
                                                       `proportion_rate` decimal(8,4) DEFAULT 0 COMMENT '比例',
                                                       `proportion_min_amount` decimal(12,2) DEFAULT NULL COMMENT '比例的保底金额',
                                                       `proportion_max_amount` decimal(12,2) DEFAULT NULL COMMENT '比例的封顶金额',
                                                       `fixed_amount` decimal(12,4) DEFAULT 0 COMMENT '固定值',
                                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                       `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                                       `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建人名称',
                                                       `last_modify_time` datetime DEFAULT NULL COMMENT '最后一次修改时间',
                                                       `last_modify_user_id` varchar(32) DEFAULT NULL COMMENT '最后一次修改人id',
                                                       `last_modify_user_name` varchar(128) DEFAULT NULL COMMENT '最后一次修改人名称',
                                                       PRIMARY KEY (`template_detail_id`),
                                                       KEY idx_tn_ft_bd_cm(`template_no`,`fee_type`,`billing_dimension`,`collection_method`)
) COMMENT='机构费率模版明细表';


-- 机构费率模版审核记录表 --
CREATE TABLE `kl_organization_fee_template_review_record` (
                                                              `review_id` varchar(32) NOT NULL COMMENT '审核id',
                                                              `operator_type` varchar(8) NOT NULL COMMENT '操作类型:ADD,MODIFY',
                                                              `template_no` varchar(32) DEFAULT NULL COMMENT '模版号,修改时有值',
                                                              `template_name` varchar(256) DEFAULT NULL COMMENT '模版名称',
                                                              `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                                              `review_status` varchar(16) DEFAULT NULL COMMENT '审核状态',
                                                              `review_reason` varchar(512) DEFAULT NULL COMMENT '审核备注',
                                                              `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
                                                              `submit_user_id` varchar(32) DEFAULT NULL COMMENT '提交人id',
                                                              `submit_user_name` varchar(128) DEFAULT NULL COMMENT '提交人名称',
                                                              `review_time` datetime DEFAULT NULL COMMENT '审核时间',
                                                              `review_user_id` varchar(32) DEFAULT NULL COMMENT '审核人id',
                                                              `review_user_name` varchar(128) DEFAULT NULL COMMENT '审核人名称',
                                                              PRIMARY KEY (`review_id`)
) COMMENT='机构费率模版审核记录表';


-- 机构费率模版明细审核记录表 --
CREATE TABLE `kl_organization_fee_template_detail_review_record` (
                                                                     `review_detail_id` varchar(32) NOT NULL COMMENT '审核明细id',
                                                                     `review_id` varchar(32) NOT NULL COMMENT '所属审核id',
                                                                     `operator_type` varchar(8) NOT NULL COMMENT '操作类型:ADD,MODIFY',
                                                                     `template_no` varchar(32) DEFAULT NULL COMMENT '所属模版号,修改时有值',
                                                                     `fee_type` varchar(4) DEFAULT NULL COMMENT '手续费类型',
                                                                     `billing_dimension` varchar(32) DEFAULT NULL COMMENT '计费维度',
                                                                     `collection_method` varchar(32) DEFAULT NULL COMMENT '收取方式',
                                                                     `currency_code` varchar(8) DEFAULT NULL COMMENT '币种',
                                                                     `min_amount` decimal(14,2) DEFAULT 0 COMMENT '金额区间:最小金额',
                                                                     `max_amount` decimal(14,2) DEFAULT 0 COMMENT '金额区间:最大金额',
                                                                     `proportion_rate` decimal(8,4) DEFAULT 0 COMMENT '比例',
                                                                     `proportion_min_amount` decimal(12,2) DEFAULT NULL COMMENT '比例的保底金额',
                                                                     `proportion_max_amount` decimal(12,2) DEFAULT NULL COMMENT '比例的封顶金额',
                                                                     `fixed_amount` decimal(12,4) DEFAULT 0 COMMENT '固定值',
                                                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                                     PRIMARY KEY (`review_detail_id`)
) COMMENT='机构费率模版明细审核记录表';



-- 机构费率配置表 --
CREATE TABLE `kl_organization_fee_config` (
                                              `id` varchar(32) NOT NULL COMMENT '主键id',
                                              `organization_no` varchar(16) DEFAULT NULL COMMENT '机构号',
                                              `card_product_code` varchar(32) DEFAULT NULL COMMENT '卡产品',
                                              `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                              `effective_start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
                                              `effective_end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
                                              `template_no` varchar(32) DEFAULT NULL COMMENT '模版号',
                                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                              `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建人id',
                                              `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建人名称',
                                              `last_modify_time` datetime DEFAULT NULL COMMENT '最后一次修改时间',
                                              `last_modify_user_id` varchar(32) DEFAULT NULL COMMENT '最后一次修改人id',
                                              `last_modify_user_name` varchar(128) DEFAULT NULL COMMENT '最后一次修改人名称',
                                              PRIMARY KEY (`id`),
                                              KEY idx_on_cpc_status_est_eet(`organization_no`,`card_product_code`,`status`,`effective_start_time`,`effective_end_time`)
) COMMENT='机构费率配置表';


-- 机构费率配置审核记录表 --
CREATE TABLE `kl_organization_fee_config_review_record` (
                                                            `review_id` varchar(32) NOT NULL COMMENT '审核id',
                                                            `operator_type` varchar(8) NOT NULL COMMENT '操作类型:ADD,MODIFY',
                                                            `fee_config_id` varchar(32) DEFAULT NULL COMMENT '所属费率配置id,修改时有值',
                                                            `organization_no` varchar(16) DEFAULT NULL COMMENT '机构号',
                                                            `card_product_code` varchar(32) DEFAULT NULL COMMENT '卡产品',
                                                            `status` varchar(8) DEFAULT NULL COMMENT '状态',
                                                            `effective_start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
                                                            `effective_end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
                                                            `template_no` varchar(32) DEFAULT NULL COMMENT '模版号',
                                                            `review_status` varchar(16) DEFAULT NULL COMMENT '审核状态',
                                                            `review_reason` varchar(512) DEFAULT NULL COMMENT '审核备注',
                                                            `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
                                                            `submit_user_id` varchar(32) DEFAULT NULL COMMENT '提交人id',
                                                            `submit_user_name` varchar(128) DEFAULT NULL COMMENT '提交人名称',
                                                            `review_time` datetime DEFAULT NULL COMMENT '审核时间',
                                                            `review_user_id` varchar(32) DEFAULT NULL COMMENT '审核人id',
                                                            `review_user_name` varchar(128) DEFAULT NULL COMMENT '审核人名称',
                                                            PRIMARY KEY (`review_id`)
) COMMENT='机构费率配置审核记录表';


-- 机构费用明细表 --
CREATE TABLE `kl_organization_fee_detail_202505` (
                                                     `id` varchar(64) NOT NULL COMMENT '主键id',
                                                     `organization_no` varchar(16) DEFAULT NULL COMMENT '机构号',
                                                     `card_product_code` varchar(16) DEFAULT NULL COMMENT '卡产品编码',
                                                     `calculate_datetime` datetime DEFAULT NULL COMMENT '手续费计算日期时间',
                                                     `transaction_datetime` datetime DEFAULT NULL COMMENT '交易日期时间',
                                                     `related_transaction_id` varchar(64) DEFAULT NULL COMMENT '关联交易id',
                                                     `fee_type` varchar(4) DEFAULT NULL COMMENT '手续费类型',
                                                     `fee_collection_method` varchar(32) DEFAULT NULL COMMENT '手续费收取方式',
                                                     `transaction_amount` decimal(20,3) DEFAULT 0 COMMENT '交易金额',
                                                     `transaction_currency_code` varchar(3) DEFAULT NULL COMMENT '交易币种',
                                                     `transaction_currency_precision` int DEFAULT 2 COMMENT '交易币种精度',
                                                     `fee_amount` decimal(20,6) DEFAULT 0 COMMENT '手续费金额(币种同交易币种)',
                                                     `fx_rate` decimal(10,5) DEFAULT 1 COMMENT '换汇汇率',
                                                     `deduct_processor` varchar(16) DEFAULT NULL COMMENT '扣除处理方(KUN和PAYX)',
                                                     `deduct_currency_code` varchar(16) DEFAULT NULL COMMENT '扣除币种',
                                                     `deduct_currency_precision` int DEFAULT NULL COMMENT '扣除币种精度',
                                                     `deduct_fee_amount` decimal(20,6) DEFAULT 0 COMMENT '扣除的费用金额',
                                                     `deduct_request_no` varchar(64) DEFAULT NULL COMMENT '扣除时的请求流水号(kun或payx中的requestNo)',
                                                     `remark` varchar(128) DEFAULT NULL COMMENT '送到kun/payx的remark数据，后续用来对账使用',
                                                     `fee_collection_status` tinyint DEFAULT NULL COMMENT '手续费收取状态;0:未收;1:已收;',
                                                     `snapshot_billing_dimension` varchar(32) DEFAULT NULL COMMENT '快照-计费维度',
                                                     `snapshot_min_amount` decimal(14,2) DEFAULT 0 COMMENT '快照-金额区间:最小金额',
                                                     `snapshot_max_amount` decimal(14,2) DEFAULT 0 COMMENT '快照-金额区间:最大金额',
                                                     `snapshot_proportion_rate` decimal(8,4) DEFAULT 0 COMMENT '快照-比例',
                                                     `snapshot_proportion_min_amount` decimal(12,2) DEFAULT NULL COMMENT '快照-比例的保底金额',
                                                     `snapshot_proportion_max_amount` decimal(12,2) DEFAULT NULL COMMENT '快照-比例的封顶金额',
                                                     `snapshot_fixed_amount` decimal(12,4) DEFAULT 0 COMMENT '快照-固定值',
                                                     `call_count` int DEFAULT 0 COMMENT '调用次数',
                                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                     `last_modify_time` datetime DEFAULT NULL COMMENT '最后一次修改时间',
                                                     PRIMARY KEY (`id`),
                                                     KEY idx_on_cpc_ft_fcm(`calculate_datetime`,`organization_no`,`card_product_code`,`fee_type`,`fee_collection_method`)
) COMMENT='机构费用明细表';
CREATE TABLE `kl_organization_fee_detail_202506` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202507` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202508` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202509` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202510` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202511` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202512` LIKE `kl_organization_fee_detail_202505`;

CREATE TABLE `kl_organization_fee_detail_202601` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202602` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202603` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202604` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202605` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202606` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202607` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202608` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202609` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202610` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202611` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202612` LIKE `kl_organization_fee_detail_202505`;

CREATE TABLE `kl_organization_fee_detail_202701` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202702` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202703` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202704` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202705` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202706` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202707` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202708` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202709` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202710` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202711` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202712` LIKE `kl_organization_fee_detail_202505`;

CREATE TABLE `kl_organization_fee_detail_202801` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202802` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202803` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202804` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202805` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202806` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202807` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202808` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202809` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202810` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202811` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202812` LIKE `kl_organization_fee_detail_202505`;

CREATE TABLE `kl_organization_fee_detail_202901` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202902` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202903` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202904` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202905` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202906` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202907` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202908` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202909` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202910` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202911` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_202912` LIKE `kl_organization_fee_detail_202505`;

CREATE TABLE `kl_organization_fee_detail_203001` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_203002` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_203003` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_203004` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_203005` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_203006` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_203007` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_203008` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_203009` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_203010` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_203011` LIKE `kl_organization_fee_detail_202505`;
CREATE TABLE `kl_organization_fee_detail_203012` LIKE `kl_organization_fee_detail_202505`;


alter table kl_organization_basic_info add column `pool_currency_code` varchar(8) DEFAULT NULL COMMENT '资金池币种' after mpc_token;
alter table kl_organization_basic_info_review_record add column `pool_currency_code` varchar(8) DEFAULT NULL COMMENT '资金池币种' after mpc_token;

alter table kl_organization_customer_card_info_0 add column `open_card_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '开卡手续费费用明细记录id' after email;
alter table kl_organization_customer_card_info_1 add column `open_card_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '开卡手续费费用明细记录id' after email;
alter table kl_organization_customer_card_info_2 add column `open_card_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '开卡手续费费用明细记录id' after email;
alter table kl_organization_customer_card_info_3 add column `open_card_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '开卡手续费费用明细记录id' after email;
alter table kl_organization_customer_card_info_4 add column `open_card_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '开卡手续费费用明细记录id' after email;
alter table kl_organization_customer_card_info_5 add column `open_card_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '开卡手续费费用明细记录id' after email;
alter table kl_organization_customer_card_info_6 add column `open_card_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '开卡手续费费用明细记录id' after email;
alter table kl_organization_customer_card_info_7 add column `open_card_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '开卡手续费费用明细记录id' after email;

alter table kl_organization_customer_card_info_0 add column `cancel_card_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '销卡手续费费用明细记录id' after open_card_fee_detail_id;
alter table kl_organization_customer_card_info_1 add column `cancel_card_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '销卡手续费费用明细记录id' after open_card_fee_detail_id;
alter table kl_organization_customer_card_info_2 add column `cancel_card_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '销卡手续费费用明细记录id' after open_card_fee_detail_id;
alter table kl_organization_customer_card_info_3 add column `cancel_card_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '销卡手续费费用明细记录id' after open_card_fee_detail_id;
alter table kl_organization_customer_card_info_4 add column `cancel_card_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '销卡手续费费用明细记录id' after open_card_fee_detail_id;
alter table kl_organization_customer_card_info_5 add column `cancel_card_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '销卡手续费费用明细记录id' after open_card_fee_detail_id;
alter table kl_organization_customer_card_info_6 add column `cancel_card_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '销卡手续费费用明细记录id' after open_card_fee_detail_id;
alter table kl_organization_customer_card_info_7 add column `cancel_card_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '销卡手续费费用明细记录id' after open_card_fee_detail_id;

alter table kl_organization_customer_card_info_0 add column `cancel_card_acceptance_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '销卡承兑费费用明细记录id' after cancel_card_fee_detail_id;
alter table kl_organization_customer_card_info_1 add column `cancel_card_acceptance_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '销卡承兑费费用明细记录id' after cancel_card_fee_detail_id;
alter table kl_organization_customer_card_info_2 add column `cancel_card_acceptance_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '销卡承兑费费用明细记录id' after cancel_card_fee_detail_id;
alter table kl_organization_customer_card_info_3 add column `cancel_card_acceptance_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '销卡承兑费费用明细记录id' after cancel_card_fee_detail_id;
alter table kl_organization_customer_card_info_4 add column `cancel_card_acceptance_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '销卡承兑费费用明细记录id' after cancel_card_fee_detail_id;
alter table kl_organization_customer_card_info_5 add column `cancel_card_acceptance_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '销卡承兑费费用明细记录id' after cancel_card_fee_detail_id;
alter table kl_organization_customer_card_info_6 add column `cancel_card_acceptance_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '销卡承兑费费用明细记录id' after cancel_card_fee_detail_id;
alter table kl_organization_customer_card_info_7 add column `cancel_card_acceptance_fee_detail_id` varchar(64) DEFAULT NULL COMMENT '销卡承兑费费用明细记录id' after cancel_card_fee_detail_id;


alter table kl_organization_customer_card_info_0 add column `cardholder_first_name` varchar(128) DEFAULT NULL COMMENT '持卡人名' after email;
alter table kl_organization_customer_card_info_1 add column `cardholder_first_name` varchar(128) DEFAULT NULL COMMENT '持卡人名' after email;
alter table kl_organization_customer_card_info_2 add column `cardholder_first_name` varchar(128) DEFAULT NULL COMMENT '持卡人名' after email;
alter table kl_organization_customer_card_info_3 add column `cardholder_first_name` varchar(128) DEFAULT NULL COMMENT '持卡人名' after email;
alter table kl_organization_customer_card_info_4 add column `cardholder_first_name` varchar(128) DEFAULT NULL COMMENT '持卡人名' after email;
alter table kl_organization_customer_card_info_5 add column `cardholder_first_name` varchar(128) DEFAULT NULL COMMENT '持卡人名' after email;
alter table kl_organization_customer_card_info_6 add column `cardholder_first_name` varchar(128) DEFAULT NULL COMMENT '持卡人名' after email;
alter table kl_organization_customer_card_info_7 add column `cardholder_first_name` varchar(128) DEFAULT NULL COMMENT '持卡人名' after email;

alter table kl_organization_customer_card_info_0 add column `cardholder_last_name` varchar(128) DEFAULT NULL COMMENT '持卡人姓' after cardholder_first_name;
alter table kl_organization_customer_card_info_1 add column `cardholder_last_name` varchar(128) DEFAULT NULL COMMENT '持卡人姓' after cardholder_first_name;
alter table kl_organization_customer_card_info_2 add column `cardholder_last_name` varchar(128) DEFAULT NULL COMMENT '持卡人姓' after cardholder_first_name;
alter table kl_organization_customer_card_info_3 add column `cardholder_last_name` varchar(128) DEFAULT NULL COMMENT '持卡人姓' after cardholder_first_name;
alter table kl_organization_customer_card_info_4 add column `cardholder_last_name` varchar(128) DEFAULT NULL COMMENT '持卡人姓' after cardholder_first_name;
alter table kl_organization_customer_card_info_5 add column `cardholder_last_name` varchar(128) DEFAULT NULL COMMENT '持卡人姓' after cardholder_first_name;
alter table kl_organization_customer_card_info_6 add column `cardholder_last_name` varchar(128) DEFAULT NULL COMMENT '持卡人姓' after cardholder_first_name;
alter table kl_organization_customer_card_info_7 add column `cardholder_last_name` varchar(128) DEFAULT NULL COMMENT '持卡人姓' after cardholder_first_name;

alter table vcc_message_send_record add column organization_no varchar(16) DEFAULT NULL COMMENT '机构号' after source_from;


CREATE TABLE `kl_file_upload_record` (
                                         `file_id` varchar(64)  NOT NULL COMMENT '文件唯一标识',
                                         `customer_id` varchar(64) NOT NULL COMMENT '客户ID',
                                         `organization_no` varchar(64)  NOT NULL COMMENT '机构号',
                                         `original_filename` varchar(255) DEFAULT NULL COMMENT '原文件名',
                                         `file_name` varchar(255)  DEFAULT NULL COMMENT '原始文件名',
                                         `file_type` varchar(50)  DEFAULT NULL COMMENT '文件类型（如PDF、JPG）',
                                         `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
                                         `storage_path` varchar(255)  DEFAULT NULL COMMENT '文件存储路径',
                                         `retention_period` int DEFAULT NULL COMMENT '文件保留期限（天）',
                                         `expires_at` datetime DEFAULT NULL COMMENT '文件过期时间',
                                         `upload_time` datetime DEFAULT NULL COMMENT '上传时间',
                                         PRIMARY KEY (`file_id`),
                                         KEY `idx_customer_id` (`customer_id`),
                                         KEY `idx_organization_no` (`organization_no`),
                                         KEY `idx_upload_time` (`upload_time`)
) COMMENT='文件上传记录表';

INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select *
from (select 'boss-kl-organizationFee' as code,
             'KL机构费用管理'           as name,
             (max(id) + 1)              as id,
             now()                      as create_date,
             null                       as update_date
      from vcc_permission) temp;

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORGANIZATION_FEE_TYPE', NULL, '01', '开卡费', 'Open Card Fee', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORGANIZATION_FEE_TYPE', NULL, '02', '充值手续费', 'Recharge Fee', 1, 2, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORGANIZATION_FEE_TYPE', NULL, '03', '承兑费', 'Acceptance Fee', 1, 3, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORGANIZATION_FEE_TYPE', NULL, '04', '交易手续费', 'Transaction Fee', 1, 4, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORGANIZATION_FEE_TYPE', NULL, '05', '清算手续费', 'Clearing Fee', 1, 5, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORGANIZATION_FEE_TYPE', NULL, '06', 'FX Markup', 'FX Markup Fee', 1, 6, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORGANIZATION_FEE_TYPE', NULL, '07', '短信手续费', 'SMS Fee', 1, 7, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORGANIZATION_FEE_TYPE', NULL, '08', '销卡手续费', 'Cancel Card Fee', 1, 8, now(), NULL, now(), NULL);


INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORG_FEE_COLLECTION_METHOD', NULL, 'REAL_TIME', '实时', 'Real Time', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORG_FEE_COLLECTION_METHOD', NULL, 'MONTHLY_SETTLEMENT', '月结', 'Monthly Settlement', 1, 2, now(), NULL, now(), NULL);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORG_FEE_BILLING_DIMENSION', NULL, 'SINGLE_AMOUNT', '单笔金额', 'Single Amount', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORG_FEE_BILLING_DIMENSION', NULL, 'TIERED_SINGLE_AMOUNT', '单笔阶梯金额', 'Tiered Single Amount', 1, 2, now(), NULL, now(), NULL);


INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORGANIZATION_POOL_CCY', NULL, 'USDT', 'USDT', 'USDT', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORGANIZATION_POOL_CCY', NULL, 'USD', 'USD', 'USD', 1, 2, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORGANIZATION_POOL_CCY', NULL, 'HKD', 'HKD', 'HKD', 1, 3, now(), NULL, now(), NULL);


INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORG_FEE_COLLECTION_STATUS', NULL, '0', '未收', 'Not Collected', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_ORG_FEE_COLLECTION_STATUS', NULL, '1', '已收', 'Collected', 1, 2, now(), NULL, now(), NULL);

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('COMMON_REPORT_TYPE', NULL, 'ORGANIZATION_FEE_MONTHLY_REPORT', 'CaaS机构手续费月报表', 'CaaS Organization Fee Monthly Report', 1, 1, now(), NULL, now(), NULL);


INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_OPERATION_STATUS', NULL, 'SUCCESS', '成功', 'Success', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_OPERATION_STATUS', NULL, 'FAIL', '失败', 'Fail', 1, 2, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_OPERATION_STATUS', NULL, 'PENDING', '处理中', 'Pending', 1, 3, now(), NULL, now(), NULL);

-- 报表记录表 --
CREATE TABLE `common_report_record` (
                                        `id` varchar(32) NOT NULL COMMENT '主键id',
                                        `report_data_date` varchar(8) DEFAULT NULL COMMENT '报表数据日期(月报表为YYYYMM,日报表为YYYYMMDD)',
                                        `report_type` varchar(64) DEFAULT NULL COMMENT '报表类型',
                                        `report_file_name` varchar(128) DEFAULT NULL COMMENT '报表文件名称',
                                        `report_file_full_path` varchar(256) DEFAULT NULL COMMENT '报表文件全路径',
                                        `report_generate_status` varchar(16) DEFAULT NULL COMMENT '报表文件生成状态',
                                        `report_generate_date` varchar(8) DEFAULT NULL COMMENT '报表文件生成日期(YYYYMMDD)',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `last_modify_time` datetime DEFAULT NULL COMMENT '最后一次修改时间',
                                        PRIMARY KEY (`id`),
                                        KEY idx_rt_rgs_rgd(`report_type`,`report_generate_status`,report_generate_date),
                                        KEY idx_rt_rdd(`report_type`,`report_data_date`)
) COMMENT='报表记录表';

INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select *
from (select 'boss-kl-organizationFeeDetail' as code,
             'KL机构费用明细'           as name,
             (max(id) + 1)              as id,
             now()                      as create_date,
             null                       as update_date
      from vcc_permission) temp;

INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select *
from (select 'boss-common-reportRecord' as code,
             '报表管理'           as name,
             (max(id) + 1)              as id,
             now()                      as create_date,
             null                       as update_date
      from vcc_permission) temp;

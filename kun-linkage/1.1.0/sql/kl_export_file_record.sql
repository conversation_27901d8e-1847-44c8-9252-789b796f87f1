-- 导出文件记录表
CREATE TABLE `kl_export_file_record` (
  `file_record_id` varchar(64) NOT NULL COMMENT '文件记录ID',
  `organization_no` varchar(512) DEFAULT NULL COMMENT '机构号',
  `file_name` varchar(512) NOT NULL COMMENT '文件名',
  `file_type` varchar(255) NOT NULL COMMENT '文件类型',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `s3_url` varchar(500) DEFAULT NULL COMMENT 'S3存储URL',
  `file_status` varchar(20) NOT NULL COMMENT '文件状态：PROCESSING/SUCCESS/FAILED',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`file_record_id`),
  KEY `idx_organization_no` (`organization_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='导出文件记录表';

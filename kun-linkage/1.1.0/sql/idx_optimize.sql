create index idx_0 ON kl_auth_flow_202505 (create_time);
create index idx_1 ON kl_auth_flow_202505 (merchant_no);
create index idx_2 ON kl_auth_flow_202505 (customer_id);
create index idx_3 ON kl_auth_flow_202505 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202505 (trans_type);
create index idx_5 ON kl_auth_flow_202505 (status);
create index idx_6 ON kl_auth_flow_202505 (processor);
create index idx_7 ON kl_auth_flow_202505 (processor_request_id);
create index idx_8 ON kl_auth_flow_202505 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202505 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202505 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202505 (clear_flag);
create index idx_12 ON kl_auth_flow_202505 (release_flag);
create index idx_13 ON kl_auth_flow_202505 (release_time);
create index idx_14 ON kl_auth_flow_202505 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202506 (create_time);
create index idx_1 ON kl_auth_flow_202506 (merchant_no);
create index idx_2 ON kl_auth_flow_202506 (customer_id);
create index idx_3 ON kl_auth_flow_202506 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202506 (trans_type);
create index idx_5 ON kl_auth_flow_202506 (status);
create index idx_6 ON kl_auth_flow_202506 (processor);
create index idx_7 ON kl_auth_flow_202506 (processor_request_id);
create index idx_8 ON kl_auth_flow_202506 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202506 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202506 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202506 (clear_flag);
create index idx_12 ON kl_auth_flow_202506 (release_flag);
create index idx_13 ON kl_auth_flow_202506 (release_time);
create index idx_14 ON kl_auth_flow_202506 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202507 (create_time);
create index idx_1 ON kl_auth_flow_202507 (merchant_no);
create index idx_2 ON kl_auth_flow_202507 (customer_id);
create index idx_3 ON kl_auth_flow_202507 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202507 (trans_type);
create index idx_5 ON kl_auth_flow_202507 (status);
create index idx_6 ON kl_auth_flow_202507 (processor);
create index idx_7 ON kl_auth_flow_202507 (processor_request_id);
create index idx_8 ON kl_auth_flow_202507 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202507 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202507 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202507 (clear_flag);
create index idx_12 ON kl_auth_flow_202507 (release_flag);
create index idx_13 ON kl_auth_flow_202507 (release_time);
create index idx_14 ON kl_auth_flow_202507 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202508 (create_time);
create index idx_1 ON kl_auth_flow_202508 (merchant_no);
create index idx_2 ON kl_auth_flow_202508 (customer_id);
create index idx_3 ON kl_auth_flow_202508 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202508 (trans_type);
create index idx_5 ON kl_auth_flow_202508 (status);
create index idx_6 ON kl_auth_flow_202508 (processor);
create index idx_7 ON kl_auth_flow_202508 (processor_request_id);
create index idx_8 ON kl_auth_flow_202508 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202508 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202508 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202508 (clear_flag);
create index idx_12 ON kl_auth_flow_202508 (release_flag);
create index idx_13 ON kl_auth_flow_202508 (release_time);
create index idx_14 ON kl_auth_flow_202508 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202509 (create_time);
create index idx_1 ON kl_auth_flow_202509 (merchant_no);
create index idx_2 ON kl_auth_flow_202509 (customer_id);
create index idx_3 ON kl_auth_flow_202509 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202509 (trans_type);
create index idx_5 ON kl_auth_flow_202509 (status);
create index idx_6 ON kl_auth_flow_202509 (processor);
create index idx_7 ON kl_auth_flow_202509 (processor_request_id);
create index idx_8 ON kl_auth_flow_202509 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202509 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202509 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202509 (clear_flag);
create index idx_12 ON kl_auth_flow_202509 (release_flag);
create index idx_13 ON kl_auth_flow_202509 (release_time);
create index idx_14 ON kl_auth_flow_202509 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202510 (create_time);
create index idx_1 ON kl_auth_flow_202510 (merchant_no);
create index idx_2 ON kl_auth_flow_202510 (customer_id);
create index idx_3 ON kl_auth_flow_202510 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202510 (trans_type);
create index idx_5 ON kl_auth_flow_202510 (status);
create index idx_6 ON kl_auth_flow_202510 (processor);
create index idx_7 ON kl_auth_flow_202510 (processor_request_id);
create index idx_8 ON kl_auth_flow_202510 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202510 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202510 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202510 (clear_flag);
create index idx_12 ON kl_auth_flow_202510 (release_flag);
create index idx_13 ON kl_auth_flow_202510 (release_time);
create index idx_14 ON kl_auth_flow_202510 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202511 (create_time);
create index idx_1 ON kl_auth_flow_202511 (merchant_no);
create index idx_2 ON kl_auth_flow_202511 (customer_id);
create index idx_3 ON kl_auth_flow_202511 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202511 (trans_type);
create index idx_5 ON kl_auth_flow_202511 (status);
create index idx_6 ON kl_auth_flow_202511 (processor);
create index idx_7 ON kl_auth_flow_202511 (processor_request_id);
create index idx_8 ON kl_auth_flow_202511 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202511 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202511 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202511 (clear_flag);
create index idx_12 ON kl_auth_flow_202511 (release_flag);
create index idx_13 ON kl_auth_flow_202511 (release_time);
create index idx_14 ON kl_auth_flow_202511 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202512 (create_time);
create index idx_1 ON kl_auth_flow_202512 (merchant_no);
create index idx_2 ON kl_auth_flow_202512 (customer_id);
create index idx_3 ON kl_auth_flow_202512 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202512 (trans_type);
create index idx_5 ON kl_auth_flow_202512 (status);
create index idx_6 ON kl_auth_flow_202512 (processor);
create index idx_7 ON kl_auth_flow_202512 (processor_request_id);
create index idx_8 ON kl_auth_flow_202512 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202512 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202512 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202512 (clear_flag);
create index idx_12 ON kl_auth_flow_202512 (release_flag);
create index idx_13 ON kl_auth_flow_202512 (release_time);
create index idx_14 ON kl_auth_flow_202512 (third_party_authorization_flag);

create index idx_0 ON kl_auth_flow_202601 (create_time);
create index idx_1 ON kl_auth_flow_202601 (merchant_no);
create index idx_2 ON kl_auth_flow_202601 (customer_id);
create index idx_3 ON kl_auth_flow_202601 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202601 (trans_type);
create index idx_5 ON kl_auth_flow_202601 (status);
create index idx_6 ON kl_auth_flow_202601 (processor);
create index idx_7 ON kl_auth_flow_202601 (processor_request_id);
create index idx_8 ON kl_auth_flow_202601 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202601 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202601 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202601 (clear_flag);
create index idx_12 ON kl_auth_flow_202601 (release_flag);
create index idx_13 ON kl_auth_flow_202601 (release_time);
create index idx_14 ON kl_auth_flow_202601 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202602 (create_time);
create index idx_1 ON kl_auth_flow_202602 (merchant_no);
create index idx_2 ON kl_auth_flow_202602 (customer_id);
create index idx_3 ON kl_auth_flow_202602 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202602 (trans_type);
create index idx_5 ON kl_auth_flow_202602 (status);
create index idx_6 ON kl_auth_flow_202602 (processor);
create index idx_7 ON kl_auth_flow_202602 (processor_request_id);
create index idx_8 ON kl_auth_flow_202602 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202602 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202602 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202602 (clear_flag);
create index idx_12 ON kl_auth_flow_202602 (release_flag);
create index idx_13 ON kl_auth_flow_202602 (release_time);
create index idx_14 ON kl_auth_flow_202602 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202603 (create_time);
create index idx_1 ON kl_auth_flow_202603 (merchant_no);
create index idx_2 ON kl_auth_flow_202603 (customer_id);
create index idx_3 ON kl_auth_flow_202603 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202603 (trans_type);
create index idx_5 ON kl_auth_flow_202603 (status);
create index idx_6 ON kl_auth_flow_202603 (processor);
create index idx_7 ON kl_auth_flow_202603 (processor_request_id);
create index idx_8 ON kl_auth_flow_202603 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202603 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202603 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202603 (clear_flag);
create index idx_12 ON kl_auth_flow_202603 (release_flag);
create index idx_13 ON kl_auth_flow_202603 (release_time);
create index idx_14 ON kl_auth_flow_202603 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202604 (create_time);
create index idx_1 ON kl_auth_flow_202604 (merchant_no);
create index idx_2 ON kl_auth_flow_202604 (customer_id);
create index idx_3 ON kl_auth_flow_202604 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202604 (trans_type);
create index idx_5 ON kl_auth_flow_202604 (status);
create index idx_6 ON kl_auth_flow_202604 (processor);
create index idx_7 ON kl_auth_flow_202604 (processor_request_id);
create index idx_8 ON kl_auth_flow_202604 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202604 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202604 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202604 (clear_flag);
create index idx_12 ON kl_auth_flow_202604 (release_flag);
create index idx_13 ON kl_auth_flow_202604 (release_time);
create index idx_14 ON kl_auth_flow_202604 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202605 (create_time);
create index idx_1 ON kl_auth_flow_202605 (merchant_no);
create index idx_2 ON kl_auth_flow_202605 (customer_id);
create index idx_3 ON kl_auth_flow_202605 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202605 (trans_type);
create index idx_5 ON kl_auth_flow_202605 (status);
create index idx_6 ON kl_auth_flow_202605 (processor);
create index idx_7 ON kl_auth_flow_202605 (processor_request_id);
create index idx_8 ON kl_auth_flow_202605 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202605 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202605 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202605 (clear_flag);
create index idx_12 ON kl_auth_flow_202605 (release_flag);
create index idx_13 ON kl_auth_flow_202605 (release_time);
create index idx_14 ON kl_auth_flow_202605 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202606 (create_time);
create index idx_1 ON kl_auth_flow_202606 (merchant_no);
create index idx_2 ON kl_auth_flow_202606 (customer_id);
create index idx_3 ON kl_auth_flow_202606 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202606 (trans_type);
create index idx_5 ON kl_auth_flow_202606 (status);
create index idx_6 ON kl_auth_flow_202606 (processor);
create index idx_7 ON kl_auth_flow_202606 (processor_request_id);
create index idx_8 ON kl_auth_flow_202606 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202606 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202606 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202606 (clear_flag);
create index idx_12 ON kl_auth_flow_202606 (release_flag);
create index idx_13 ON kl_auth_flow_202606 (release_time);
create index idx_14 ON kl_auth_flow_202606 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202607 (create_time);
create index idx_1 ON kl_auth_flow_202607 (merchant_no);
create index idx_2 ON kl_auth_flow_202607 (customer_id);
create index idx_3 ON kl_auth_flow_202607 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202607 (trans_type);
create index idx_5 ON kl_auth_flow_202607 (status);
create index idx_6 ON kl_auth_flow_202607 (processor);
create index idx_7 ON kl_auth_flow_202607 (processor_request_id);
create index idx_8 ON kl_auth_flow_202607 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202607 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202607 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202607 (clear_flag);
create index idx_12 ON kl_auth_flow_202607 (release_flag);
create index idx_13 ON kl_auth_flow_202607 (release_time);
create index idx_14 ON kl_auth_flow_202607 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202608 (create_time);
create index idx_1 ON kl_auth_flow_202608 (merchant_no);
create index idx_2 ON kl_auth_flow_202608 (customer_id);
create index idx_3 ON kl_auth_flow_202608 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202608 (trans_type);
create index idx_5 ON kl_auth_flow_202608 (status);
create index idx_6 ON kl_auth_flow_202608 (processor);
create index idx_7 ON kl_auth_flow_202608 (processor_request_id);
create index idx_8 ON kl_auth_flow_202608 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202608 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202608 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202608 (clear_flag);
create index idx_12 ON kl_auth_flow_202608 (release_flag);
create index idx_13 ON kl_auth_flow_202608 (release_time);
create index idx_14 ON kl_auth_flow_202608 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202609 (create_time);
create index idx_1 ON kl_auth_flow_202609 (merchant_no);
create index idx_2 ON kl_auth_flow_202609 (customer_id);
create index idx_3 ON kl_auth_flow_202609 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202609 (trans_type);
create index idx_5 ON kl_auth_flow_202609 (status);
create index idx_6 ON kl_auth_flow_202609 (processor);
create index idx_7 ON kl_auth_flow_202609 (processor_request_id);
create index idx_8 ON kl_auth_flow_202609 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202609 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202609 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202609 (clear_flag);
create index idx_12 ON kl_auth_flow_202609 (release_flag);
create index idx_13 ON kl_auth_flow_202609 (release_time);
create index idx_14 ON kl_auth_flow_202609 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202610 (create_time);
create index idx_1 ON kl_auth_flow_202610 (merchant_no);
create index idx_2 ON kl_auth_flow_202610 (customer_id);
create index idx_3 ON kl_auth_flow_202610 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202610 (trans_type);
create index idx_5 ON kl_auth_flow_202610 (status);
create index idx_6 ON kl_auth_flow_202610 (processor);
create index idx_7 ON kl_auth_flow_202610 (processor_request_id);
create index idx_8 ON kl_auth_flow_202610 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202610 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202610 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202610 (clear_flag);
create index idx_12 ON kl_auth_flow_202610 (release_flag);
create index idx_13 ON kl_auth_flow_202610 (release_time);
create index idx_14 ON kl_auth_flow_202610 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202611 (create_time);
create index idx_1 ON kl_auth_flow_202611 (merchant_no);
create index idx_2 ON kl_auth_flow_202611 (customer_id);
create index idx_3 ON kl_auth_flow_202611 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202611 (trans_type);
create index idx_5 ON kl_auth_flow_202611 (status);
create index idx_6 ON kl_auth_flow_202611 (processor);
create index idx_7 ON kl_auth_flow_202611 (processor_request_id);
create index idx_8 ON kl_auth_flow_202611 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202611 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202611 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202611 (clear_flag);
create index idx_12 ON kl_auth_flow_202611 (release_flag);
create index idx_13 ON kl_auth_flow_202611 (release_time);
create index idx_14 ON kl_auth_flow_202611 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202612 (create_time);
create index idx_1 ON kl_auth_flow_202612 (merchant_no);
create index idx_2 ON kl_auth_flow_202612 (customer_id);
create index idx_3 ON kl_auth_flow_202612 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202612 (trans_type);
create index idx_5 ON kl_auth_flow_202612 (status);
create index idx_6 ON kl_auth_flow_202612 (processor);
create index idx_7 ON kl_auth_flow_202612 (processor_request_id);
create index idx_8 ON kl_auth_flow_202612 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202612 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202612 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202612 (clear_flag);
create index idx_12 ON kl_auth_flow_202612 (release_flag);
create index idx_13 ON kl_auth_flow_202612 (release_time);
create index idx_14 ON kl_auth_flow_202612 (third_party_authorization_flag);

create index idx_0 ON kl_auth_flow_202701 (create_time);
create index idx_1 ON kl_auth_flow_202701 (merchant_no);
create index idx_2 ON kl_auth_flow_202701 (customer_id);
create index idx_3 ON kl_auth_flow_202701 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202701 (trans_type);
create index idx_5 ON kl_auth_flow_202701 (status);
create index idx_6 ON kl_auth_flow_202701 (processor);
create index idx_7 ON kl_auth_flow_202701 (processor_request_id);
create index idx_8 ON kl_auth_flow_202701 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202701 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202701 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202701 (clear_flag);
create index idx_12 ON kl_auth_flow_202701 (release_flag);
create index idx_13 ON kl_auth_flow_202701 (release_time);
create index idx_14 ON kl_auth_flow_202701 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202702 (create_time);
create index idx_1 ON kl_auth_flow_202702 (merchant_no);
create index idx_2 ON kl_auth_flow_202702 (customer_id);
create index idx_3 ON kl_auth_flow_202702 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202702 (trans_type);
create index idx_5 ON kl_auth_flow_202702 (status);
create index idx_6 ON kl_auth_flow_202702 (processor);
create index idx_7 ON kl_auth_flow_202702 (processor_request_id);
create index idx_8 ON kl_auth_flow_202702 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202702 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202702 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202702 (clear_flag);
create index idx_12 ON kl_auth_flow_202702 (release_flag);
create index idx_13 ON kl_auth_flow_202702 (release_time);
create index idx_14 ON kl_auth_flow_202702 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202703 (create_time);
create index idx_1 ON kl_auth_flow_202703 (merchant_no);
create index idx_2 ON kl_auth_flow_202703 (customer_id);
create index idx_3 ON kl_auth_flow_202703 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202703 (trans_type);
create index idx_5 ON kl_auth_flow_202703 (status);
create index idx_6 ON kl_auth_flow_202703 (processor);
create index idx_7 ON kl_auth_flow_202703 (processor_request_id);
create index idx_8 ON kl_auth_flow_202703 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202703 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202703 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202703 (clear_flag);
create index idx_12 ON kl_auth_flow_202703 (release_flag);
create index idx_13 ON kl_auth_flow_202703 (release_time);
create index idx_14 ON kl_auth_flow_202703 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202704 (create_time);
create index idx_1 ON kl_auth_flow_202704 (merchant_no);
create index idx_2 ON kl_auth_flow_202704 (customer_id);
create index idx_3 ON kl_auth_flow_202704 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202704 (trans_type);
create index idx_5 ON kl_auth_flow_202704 (status);
create index idx_6 ON kl_auth_flow_202704 (processor);
create index idx_7 ON kl_auth_flow_202704 (processor_request_id);
create index idx_8 ON kl_auth_flow_202704 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202704 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202704 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202704 (clear_flag);
create index idx_12 ON kl_auth_flow_202704 (release_flag);
create index idx_13 ON kl_auth_flow_202704 (release_time);
create index idx_14 ON kl_auth_flow_202704 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202705 (create_time);
create index idx_1 ON kl_auth_flow_202705 (merchant_no);
create index idx_2 ON kl_auth_flow_202705 (customer_id);
create index idx_3 ON kl_auth_flow_202705 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202705 (trans_type);
create index idx_5 ON kl_auth_flow_202705 (status);
create index idx_6 ON kl_auth_flow_202705 (processor);
create index idx_7 ON kl_auth_flow_202705 (processor_request_id);
create index idx_8 ON kl_auth_flow_202705 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202705 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202705 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202705 (clear_flag);
create index idx_12 ON kl_auth_flow_202705 (release_flag);
create index idx_13 ON kl_auth_flow_202705 (release_time);
create index idx_14 ON kl_auth_flow_202705 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202706 (create_time);
create index idx_1 ON kl_auth_flow_202706 (merchant_no);
create index idx_2 ON kl_auth_flow_202706 (customer_id);
create index idx_3 ON kl_auth_flow_202706 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202706 (trans_type);
create index idx_5 ON kl_auth_flow_202706 (status);
create index idx_6 ON kl_auth_flow_202706 (processor);
create index idx_7 ON kl_auth_flow_202706 (processor_request_id);
create index idx_8 ON kl_auth_flow_202706 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202706 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202706 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202706 (clear_flag);
create index idx_12 ON kl_auth_flow_202706 (release_flag);
create index idx_13 ON kl_auth_flow_202706 (release_time);
create index idx_14 ON kl_auth_flow_202706 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202707 (create_time);
create index idx_1 ON kl_auth_flow_202707 (merchant_no);
create index idx_2 ON kl_auth_flow_202707 (customer_id);
create index idx_3 ON kl_auth_flow_202707 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202707 (trans_type);
create index idx_5 ON kl_auth_flow_202707 (status);
create index idx_6 ON kl_auth_flow_202707 (processor);
create index idx_7 ON kl_auth_flow_202707 (processor_request_id);
create index idx_8 ON kl_auth_flow_202707 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202707 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202707 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202707 (clear_flag);
create index idx_12 ON kl_auth_flow_202707 (release_flag);
create index idx_13 ON kl_auth_flow_202707 (release_time);
create index idx_14 ON kl_auth_flow_202707 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202708 (create_time);
create index idx_1 ON kl_auth_flow_202708 (merchant_no);
create index idx_2 ON kl_auth_flow_202708 (customer_id);
create index idx_3 ON kl_auth_flow_202708 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202708 (trans_type);
create index idx_5 ON kl_auth_flow_202708 (status);
create index idx_6 ON kl_auth_flow_202708 (processor);
create index idx_7 ON kl_auth_flow_202708 (processor_request_id);
create index idx_8 ON kl_auth_flow_202708 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202708 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202708 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202708 (clear_flag);
create index idx_12 ON kl_auth_flow_202708 (release_flag);
create index idx_13 ON kl_auth_flow_202708 (release_time);
create index idx_14 ON kl_auth_flow_202708 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202709 (create_time);
create index idx_1 ON kl_auth_flow_202709 (merchant_no);
create index idx_2 ON kl_auth_flow_202709 (customer_id);
create index idx_3 ON kl_auth_flow_202709 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202709 (trans_type);
create index idx_5 ON kl_auth_flow_202709 (status);
create index idx_6 ON kl_auth_flow_202709 (processor);
create index idx_7 ON kl_auth_flow_202709 (processor_request_id);
create index idx_8 ON kl_auth_flow_202709 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202709 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202709 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202709 (clear_flag);
create index idx_12 ON kl_auth_flow_202709 (release_flag);
create index idx_13 ON kl_auth_flow_202709 (release_time);
create index idx_14 ON kl_auth_flow_202709 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202710 (create_time);
create index idx_1 ON kl_auth_flow_202710 (merchant_no);
create index idx_2 ON kl_auth_flow_202710 (customer_id);
create index idx_3 ON kl_auth_flow_202710 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202710 (trans_type);
create index idx_5 ON kl_auth_flow_202710 (status);
create index idx_6 ON kl_auth_flow_202710 (processor);
create index idx_7 ON kl_auth_flow_202710 (processor_request_id);
create index idx_8 ON kl_auth_flow_202710 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202710 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202710 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202710 (clear_flag);
create index idx_12 ON kl_auth_flow_202710 (release_flag);
create index idx_13 ON kl_auth_flow_202710 (release_time);
create index idx_14 ON kl_auth_flow_202710 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202711 (create_time);
create index idx_1 ON kl_auth_flow_202711 (merchant_no);
create index idx_2 ON kl_auth_flow_202711 (customer_id);
create index idx_3 ON kl_auth_flow_202711 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202711 (trans_type);
create index idx_5 ON kl_auth_flow_202711 (status);
create index idx_6 ON kl_auth_flow_202711 (processor);
create index idx_7 ON kl_auth_flow_202711 (processor_request_id);
create index idx_8 ON kl_auth_flow_202711 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202711 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202711 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202711 (clear_flag);
create index idx_12 ON kl_auth_flow_202711 (release_flag);
create index idx_13 ON kl_auth_flow_202711 (release_time);
create index idx_14 ON kl_auth_flow_202711 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202712 (create_time);
create index idx_1 ON kl_auth_flow_202712 (merchant_no);
create index idx_2 ON kl_auth_flow_202712 (customer_id);
create index idx_3 ON kl_auth_flow_202712 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202712 (trans_type);
create index idx_5 ON kl_auth_flow_202712 (status);
create index idx_6 ON kl_auth_flow_202712 (processor);
create index idx_7 ON kl_auth_flow_202712 (processor_request_id);
create index idx_8 ON kl_auth_flow_202712 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202712 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202712 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202712 (clear_flag);
create index idx_12 ON kl_auth_flow_202712 (release_flag);
create index idx_13 ON kl_auth_flow_202712 (release_time);
create index idx_14 ON kl_auth_flow_202712 (third_party_authorization_flag);

create index idx_0 ON kl_auth_flow_202801 (create_time);
create index idx_1 ON kl_auth_flow_202801 (merchant_no);
create index idx_2 ON kl_auth_flow_202801 (customer_id);
create index idx_3 ON kl_auth_flow_202801 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202801 (trans_type);
create index idx_5 ON kl_auth_flow_202801 (status);
create index idx_6 ON kl_auth_flow_202801 (processor);
create index idx_7 ON kl_auth_flow_202801 (processor_request_id);
create index idx_8 ON kl_auth_flow_202801 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202801 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202801 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202801 (clear_flag);
create index idx_12 ON kl_auth_flow_202801 (release_flag);
create index idx_13 ON kl_auth_flow_202801 (release_time);
create index idx_14 ON kl_auth_flow_202801 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202802 (create_time);
create index idx_1 ON kl_auth_flow_202802 (merchant_no);
create index idx_2 ON kl_auth_flow_202802 (customer_id);
create index idx_3 ON kl_auth_flow_202802 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202802 (trans_type);
create index idx_5 ON kl_auth_flow_202802 (status);
create index idx_6 ON kl_auth_flow_202802 (processor);
create index idx_7 ON kl_auth_flow_202802 (processor_request_id);
create index idx_8 ON kl_auth_flow_202802 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202802 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202802 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202802 (clear_flag);
create index idx_12 ON kl_auth_flow_202802 (release_flag);
create index idx_13 ON kl_auth_flow_202802 (release_time);
create index idx_14 ON kl_auth_flow_202802 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202803 (create_time);
create index idx_1 ON kl_auth_flow_202803 (merchant_no);
create index idx_2 ON kl_auth_flow_202803 (customer_id);
create index idx_3 ON kl_auth_flow_202803 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202803 (trans_type);
create index idx_5 ON kl_auth_flow_202803 (status);
create index idx_6 ON kl_auth_flow_202803 (processor);
create index idx_7 ON kl_auth_flow_202803 (processor_request_id);
create index idx_8 ON kl_auth_flow_202803 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202803 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202803 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202803 (clear_flag);
create index idx_12 ON kl_auth_flow_202803 (release_flag);
create index idx_13 ON kl_auth_flow_202803 (release_time);
create index idx_14 ON kl_auth_flow_202803 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202804 (create_time);
create index idx_1 ON kl_auth_flow_202804 (merchant_no);
create index idx_2 ON kl_auth_flow_202804 (customer_id);
create index idx_3 ON kl_auth_flow_202804 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202804 (trans_type);
create index idx_5 ON kl_auth_flow_202804 (status);
create index idx_6 ON kl_auth_flow_202804 (processor);
create index idx_7 ON kl_auth_flow_202804 (processor_request_id);
create index idx_8 ON kl_auth_flow_202804 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202804 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202804 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202804 (clear_flag);
create index idx_12 ON kl_auth_flow_202804 (release_flag);
create index idx_13 ON kl_auth_flow_202804 (release_time);
create index idx_14 ON kl_auth_flow_202804 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202805 (create_time);
create index idx_1 ON kl_auth_flow_202805 (merchant_no);
create index idx_2 ON kl_auth_flow_202805 (customer_id);
create index idx_3 ON kl_auth_flow_202805 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202805 (trans_type);
create index idx_5 ON kl_auth_flow_202805 (status);
create index idx_6 ON kl_auth_flow_202805 (processor);
create index idx_7 ON kl_auth_flow_202805 (processor_request_id);
create index idx_8 ON kl_auth_flow_202805 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202805 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202805 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202805 (clear_flag);
create index idx_12 ON kl_auth_flow_202805 (release_flag);
create index idx_13 ON kl_auth_flow_202805 (release_time);
create index idx_14 ON kl_auth_flow_202805 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202806 (create_time);
create index idx_1 ON kl_auth_flow_202806 (merchant_no);
create index idx_2 ON kl_auth_flow_202806 (customer_id);
create index idx_3 ON kl_auth_flow_202806 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202806 (trans_type);
create index idx_5 ON kl_auth_flow_202806 (status);
create index idx_6 ON kl_auth_flow_202806 (processor);
create index idx_7 ON kl_auth_flow_202806 (processor_request_id);
create index idx_8 ON kl_auth_flow_202806 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202806 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202806 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202806 (clear_flag);
create index idx_12 ON kl_auth_flow_202806 (release_flag);
create index idx_13 ON kl_auth_flow_202806 (release_time);
create index idx_14 ON kl_auth_flow_202806 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202807 (create_time);
create index idx_1 ON kl_auth_flow_202807 (merchant_no);
create index idx_2 ON kl_auth_flow_202807 (customer_id);
create index idx_3 ON kl_auth_flow_202807 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202807 (trans_type);
create index idx_5 ON kl_auth_flow_202807 (status);
create index idx_6 ON kl_auth_flow_202807 (processor);
create index idx_7 ON kl_auth_flow_202807 (processor_request_id);
create index idx_8 ON kl_auth_flow_202807 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202807 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202807 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202807 (clear_flag);
create index idx_12 ON kl_auth_flow_202807 (release_flag);
create index idx_13 ON kl_auth_flow_202807 (release_time);
create index idx_14 ON kl_auth_flow_202807 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202808 (create_time);
create index idx_1 ON kl_auth_flow_202808 (merchant_no);
create index idx_2 ON kl_auth_flow_202808 (customer_id);
create index idx_3 ON kl_auth_flow_202808 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202808 (trans_type);
create index idx_5 ON kl_auth_flow_202808 (status);
create index idx_6 ON kl_auth_flow_202808 (processor);
create index idx_7 ON kl_auth_flow_202808 (processor_request_id);
create index idx_8 ON kl_auth_flow_202808 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202808 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202808 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202808 (clear_flag);
create index idx_12 ON kl_auth_flow_202808 (release_flag);
create index idx_13 ON kl_auth_flow_202808 (release_time);
create index idx_14 ON kl_auth_flow_202808 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202809 (create_time);
create index idx_1 ON kl_auth_flow_202809 (merchant_no);
create index idx_2 ON kl_auth_flow_202809 (customer_id);
create index idx_3 ON kl_auth_flow_202809 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202809 (trans_type);
create index idx_5 ON kl_auth_flow_202809 (status);
create index idx_6 ON kl_auth_flow_202809 (processor);
create index idx_7 ON kl_auth_flow_202809 (processor_request_id);
create index idx_8 ON kl_auth_flow_202809 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202809 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202809 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202809 (clear_flag);
create index idx_12 ON kl_auth_flow_202809 (release_flag);
create index idx_13 ON kl_auth_flow_202809 (release_time);
create index idx_14 ON kl_auth_flow_202809 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202810 (create_time);
create index idx_1 ON kl_auth_flow_202810 (merchant_no);
create index idx_2 ON kl_auth_flow_202810 (customer_id);
create index idx_3 ON kl_auth_flow_202810 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202810 (trans_type);
create index idx_5 ON kl_auth_flow_202810 (status);
create index idx_6 ON kl_auth_flow_202810 (processor);
create index idx_7 ON kl_auth_flow_202810 (processor_request_id);
create index idx_8 ON kl_auth_flow_202810 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202810 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202810 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202810 (clear_flag);
create index idx_12 ON kl_auth_flow_202810 (release_flag);
create index idx_13 ON kl_auth_flow_202810 (release_time);
create index idx_14 ON kl_auth_flow_202810 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202811 (create_time);
create index idx_1 ON kl_auth_flow_202811 (merchant_no);
create index idx_2 ON kl_auth_flow_202811 (customer_id);
create index idx_3 ON kl_auth_flow_202811 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202811 (trans_type);
create index idx_5 ON kl_auth_flow_202811 (status);
create index idx_6 ON kl_auth_flow_202811 (processor);
create index idx_7 ON kl_auth_flow_202811 (processor_request_id);
create index idx_8 ON kl_auth_flow_202811 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202811 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202811 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202811 (clear_flag);
create index idx_12 ON kl_auth_flow_202811 (release_flag);
create index idx_13 ON kl_auth_flow_202811 (release_time);
create index idx_14 ON kl_auth_flow_202811 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202812 (create_time);
create index idx_1 ON kl_auth_flow_202812 (merchant_no);
create index idx_2 ON kl_auth_flow_202812 (customer_id);
create index idx_3 ON kl_auth_flow_202812 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202812 (trans_type);
create index idx_5 ON kl_auth_flow_202812 (status);
create index idx_6 ON kl_auth_flow_202812 (processor);
create index idx_7 ON kl_auth_flow_202812 (processor_request_id);
create index idx_8 ON kl_auth_flow_202812 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202812 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202812 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202812 (clear_flag);
create index idx_12 ON kl_auth_flow_202812 (release_flag);
create index idx_13 ON kl_auth_flow_202812 (release_time);
create index idx_14 ON kl_auth_flow_202812 (third_party_authorization_flag);

create index idx_0 ON kl_auth_flow_202901 (create_time);
create index idx_1 ON kl_auth_flow_202901 (merchant_no);
create index idx_2 ON kl_auth_flow_202901 (customer_id);
create index idx_3 ON kl_auth_flow_202901 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202901 (trans_type);
create index idx_5 ON kl_auth_flow_202901 (status);
create index idx_6 ON kl_auth_flow_202901 (processor);
create index idx_7 ON kl_auth_flow_202901 (processor_request_id);
create index idx_8 ON kl_auth_flow_202901 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202901 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202901 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202901 (clear_flag);
create index idx_12 ON kl_auth_flow_202901 (release_flag);
create index idx_13 ON kl_auth_flow_202901 (release_time);
create index idx_14 ON kl_auth_flow_202901 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202902 (create_time);
create index idx_1 ON kl_auth_flow_202902 (merchant_no);
create index idx_2 ON kl_auth_flow_202902 (customer_id);
create index idx_3 ON kl_auth_flow_202902 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202902 (trans_type);
create index idx_5 ON kl_auth_flow_202902 (status);
create index idx_6 ON kl_auth_flow_202902 (processor);
create index idx_7 ON kl_auth_flow_202902 (processor_request_id);
create index idx_8 ON kl_auth_flow_202902 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202902 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202902 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202902 (clear_flag);
create index idx_12 ON kl_auth_flow_202902 (release_flag);
create index idx_13 ON kl_auth_flow_202902 (release_time);
create index idx_14 ON kl_auth_flow_202902 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202903 (create_time);
create index idx_1 ON kl_auth_flow_202903 (merchant_no);
create index idx_2 ON kl_auth_flow_202903 (customer_id);
create index idx_3 ON kl_auth_flow_202903 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202903 (trans_type);
create index idx_5 ON kl_auth_flow_202903 (status);
create index idx_6 ON kl_auth_flow_202903 (processor);
create index idx_7 ON kl_auth_flow_202903 (processor_request_id);
create index idx_8 ON kl_auth_flow_202903 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202903 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202903 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202903 (clear_flag);
create index idx_12 ON kl_auth_flow_202903 (release_flag);
create index idx_13 ON kl_auth_flow_202903 (release_time);
create index idx_14 ON kl_auth_flow_202903 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202904 (create_time);
create index idx_1 ON kl_auth_flow_202904 (merchant_no);
create index idx_2 ON kl_auth_flow_202904 (customer_id);
create index idx_3 ON kl_auth_flow_202904 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202904 (trans_type);
create index idx_5 ON kl_auth_flow_202904 (status);
create index idx_6 ON kl_auth_flow_202904 (processor);
create index idx_7 ON kl_auth_flow_202904 (processor_request_id);
create index idx_8 ON kl_auth_flow_202904 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202904 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202904 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202904 (clear_flag);
create index idx_12 ON kl_auth_flow_202904 (release_flag);
create index idx_13 ON kl_auth_flow_202904 (release_time);
create index idx_14 ON kl_auth_flow_202904 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202905 (create_time);
create index idx_1 ON kl_auth_flow_202905 (merchant_no);
create index idx_2 ON kl_auth_flow_202905 (customer_id);
create index idx_3 ON kl_auth_flow_202905 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202905 (trans_type);
create index idx_5 ON kl_auth_flow_202905 (status);
create index idx_6 ON kl_auth_flow_202905 (processor);
create index idx_7 ON kl_auth_flow_202905 (processor_request_id);
create index idx_8 ON kl_auth_flow_202905 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202905 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202905 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202905 (clear_flag);
create index idx_12 ON kl_auth_flow_202905 (release_flag);
create index idx_13 ON kl_auth_flow_202905 (release_time);
create index idx_14 ON kl_auth_flow_202905 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202906 (create_time);
create index idx_1 ON kl_auth_flow_202906 (merchant_no);
create index idx_2 ON kl_auth_flow_202906 (customer_id);
create index idx_3 ON kl_auth_flow_202906 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202906 (trans_type);
create index idx_5 ON kl_auth_flow_202906 (status);
create index idx_6 ON kl_auth_flow_202906 (processor);
create index idx_7 ON kl_auth_flow_202906 (processor_request_id);
create index idx_8 ON kl_auth_flow_202906 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202906 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202906 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202906 (clear_flag);
create index idx_12 ON kl_auth_flow_202906 (release_flag);
create index idx_13 ON kl_auth_flow_202906 (release_time);
create index idx_14 ON kl_auth_flow_202906 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202907 (create_time);
create index idx_1 ON kl_auth_flow_202907 (merchant_no);
create index idx_2 ON kl_auth_flow_202907 (customer_id);
create index idx_3 ON kl_auth_flow_202907 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202907 (trans_type);
create index idx_5 ON kl_auth_flow_202907 (status);
create index idx_6 ON kl_auth_flow_202907 (processor);
create index idx_7 ON kl_auth_flow_202907 (processor_request_id);
create index idx_8 ON kl_auth_flow_202907 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202907 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202907 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202907 (clear_flag);
create index idx_12 ON kl_auth_flow_202907 (release_flag);
create index idx_13 ON kl_auth_flow_202907 (release_time);
create index idx_14 ON kl_auth_flow_202907 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202908 (create_time);
create index idx_1 ON kl_auth_flow_202908 (merchant_no);
create index idx_2 ON kl_auth_flow_202908 (customer_id);
create index idx_3 ON kl_auth_flow_202908 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202908 (trans_type);
create index idx_5 ON kl_auth_flow_202908 (status);
create index idx_6 ON kl_auth_flow_202908 (processor);
create index idx_7 ON kl_auth_flow_202908 (processor_request_id);
create index idx_8 ON kl_auth_flow_202908 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202908 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202908 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202908 (clear_flag);
create index idx_12 ON kl_auth_flow_202908 (release_flag);
create index idx_13 ON kl_auth_flow_202908 (release_time);
create index idx_14 ON kl_auth_flow_202908 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202909 (create_time);
create index idx_1 ON kl_auth_flow_202909 (merchant_no);
create index idx_2 ON kl_auth_flow_202909 (customer_id);
create index idx_3 ON kl_auth_flow_202909 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202909 (trans_type);
create index idx_5 ON kl_auth_flow_202909 (status);
create index idx_6 ON kl_auth_flow_202909 (processor);
create index idx_7 ON kl_auth_flow_202909 (processor_request_id);
create index idx_8 ON kl_auth_flow_202909 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202909 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202909 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202909 (clear_flag);
create index idx_12 ON kl_auth_flow_202909 (release_flag);
create index idx_13 ON kl_auth_flow_202909 (release_time);
create index idx_14 ON kl_auth_flow_202909 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202910 (create_time);
create index idx_1 ON kl_auth_flow_202910 (merchant_no);
create index idx_2 ON kl_auth_flow_202910 (customer_id);
create index idx_3 ON kl_auth_flow_202910 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202910 (trans_type);
create index idx_5 ON kl_auth_flow_202910 (status);
create index idx_6 ON kl_auth_flow_202910 (processor);
create index idx_7 ON kl_auth_flow_202910 (processor_request_id);
create index idx_8 ON kl_auth_flow_202910 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202910 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202910 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202910 (clear_flag);
create index idx_12 ON kl_auth_flow_202910 (release_flag);
create index idx_13 ON kl_auth_flow_202910 (release_time);
create index idx_14 ON kl_auth_flow_202910 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202911 (create_time);
create index idx_1 ON kl_auth_flow_202911 (merchant_no);
create index idx_2 ON kl_auth_flow_202911 (customer_id);
create index idx_3 ON kl_auth_flow_202911 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202911 (trans_type);
create index idx_5 ON kl_auth_flow_202911 (status);
create index idx_6 ON kl_auth_flow_202911 (processor);
create index idx_7 ON kl_auth_flow_202911 (processor_request_id);
create index idx_8 ON kl_auth_flow_202911 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202911 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202911 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202911 (clear_flag);
create index idx_12 ON kl_auth_flow_202911 (release_flag);
create index idx_13 ON kl_auth_flow_202911 (release_time);
create index idx_14 ON kl_auth_flow_202911 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_202912 (create_time);
create index idx_1 ON kl_auth_flow_202912 (merchant_no);
create index idx_2 ON kl_auth_flow_202912 (customer_id);
create index idx_3 ON kl_auth_flow_202912 (gateway_card_id);
create index idx_4 ON kl_auth_flow_202912 (trans_type);
create index idx_5 ON kl_auth_flow_202912 (status);
create index idx_6 ON kl_auth_flow_202912 (processor);
create index idx_7 ON kl_auth_flow_202912 (processor_request_id);
create index idx_8 ON kl_auth_flow_202912 (processor_trans_id);
create index idx_9 ON kl_auth_flow_202912 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_202912 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_202912 (clear_flag);
create index idx_12 ON kl_auth_flow_202912 (release_flag);
create index idx_13 ON kl_auth_flow_202912 (release_time);
create index idx_14 ON kl_auth_flow_202912 (third_party_authorization_flag);

create index idx_0 ON kl_auth_flow_203001 (create_time);
create index idx_1 ON kl_auth_flow_203001 (merchant_no);
create index idx_2 ON kl_auth_flow_203001 (customer_id);
create index idx_3 ON kl_auth_flow_203001 (gateway_card_id);
create index idx_4 ON kl_auth_flow_203001 (trans_type);
create index idx_5 ON kl_auth_flow_203001 (status);
create index idx_6 ON kl_auth_flow_203001 (processor);
create index idx_7 ON kl_auth_flow_203001 (processor_request_id);
create index idx_8 ON kl_auth_flow_203001 (processor_trans_id);
create index idx_9 ON kl_auth_flow_203001 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_203001 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_203001 (clear_flag);
create index idx_12 ON kl_auth_flow_203001 (release_flag);
create index idx_13 ON kl_auth_flow_203001 (release_time);
create index idx_14 ON kl_auth_flow_203001 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_203002 (create_time);
create index idx_1 ON kl_auth_flow_203002 (merchant_no);
create index idx_2 ON kl_auth_flow_203002 (customer_id);
create index idx_3 ON kl_auth_flow_203002 (gateway_card_id);
create index idx_4 ON kl_auth_flow_203002 (trans_type);
create index idx_5 ON kl_auth_flow_203002 (status);
create index idx_6 ON kl_auth_flow_203002 (processor);
create index idx_7 ON kl_auth_flow_203002 (processor_request_id);
create index idx_8 ON kl_auth_flow_203002 (processor_trans_id);
create index idx_9 ON kl_auth_flow_203002 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_203002 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_203002 (clear_flag);
create index idx_12 ON kl_auth_flow_203002 (release_flag);
create index idx_13 ON kl_auth_flow_203002 (release_time);
create index idx_14 ON kl_auth_flow_203002 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_203003 (create_time);
create index idx_1 ON kl_auth_flow_203003 (merchant_no);
create index idx_2 ON kl_auth_flow_203003 (customer_id);
create index idx_3 ON kl_auth_flow_203003 (gateway_card_id);
create index idx_4 ON kl_auth_flow_203003 (trans_type);
create index idx_5 ON kl_auth_flow_203003 (status);
create index idx_6 ON kl_auth_flow_203003 (processor);
create index idx_7 ON kl_auth_flow_203003 (processor_request_id);
create index idx_8 ON kl_auth_flow_203003 (processor_trans_id);
create index idx_9 ON kl_auth_flow_203003 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_203003 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_203003 (clear_flag);
create index idx_12 ON kl_auth_flow_203003 (release_flag);
create index idx_13 ON kl_auth_flow_203003 (release_time);
create index idx_14 ON kl_auth_flow_203003 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_203004 (create_time);
create index idx_1 ON kl_auth_flow_203004 (merchant_no);
create index idx_2 ON kl_auth_flow_203004 (customer_id);
create index idx_3 ON kl_auth_flow_203004 (gateway_card_id);
create index idx_4 ON kl_auth_flow_203004 (trans_type);
create index idx_5 ON kl_auth_flow_203004 (status);
create index idx_6 ON kl_auth_flow_203004 (processor);
create index idx_7 ON kl_auth_flow_203004 (processor_request_id);
create index idx_8 ON kl_auth_flow_203004 (processor_trans_id);
create index idx_9 ON kl_auth_flow_203004 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_203004 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_203004 (clear_flag);
create index idx_12 ON kl_auth_flow_203004 (release_flag);
create index idx_13 ON kl_auth_flow_203004 (release_time);
create index idx_14 ON kl_auth_flow_203004 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_203005 (create_time);
create index idx_1 ON kl_auth_flow_203005 (merchant_no);
create index idx_2 ON kl_auth_flow_203005 (customer_id);
create index idx_3 ON kl_auth_flow_203005 (gateway_card_id);
create index idx_4 ON kl_auth_flow_203005 (trans_type);
create index idx_5 ON kl_auth_flow_203005 (status);
create index idx_6 ON kl_auth_flow_203005 (processor);
create index idx_7 ON kl_auth_flow_203005 (processor_request_id);
create index idx_8 ON kl_auth_flow_203005 (processor_trans_id);
create index idx_9 ON kl_auth_flow_203005 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_203005 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_203005 (clear_flag);
create index idx_12 ON kl_auth_flow_203005 (release_flag);
create index idx_13 ON kl_auth_flow_203005 (release_time);
create index idx_14 ON kl_auth_flow_203005 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_203006 (create_time);
create index idx_1 ON kl_auth_flow_203006 (merchant_no);
create index idx_2 ON kl_auth_flow_203006 (customer_id);
create index idx_3 ON kl_auth_flow_203006 (gateway_card_id);
create index idx_4 ON kl_auth_flow_203006 (trans_type);
create index idx_5 ON kl_auth_flow_203006 (status);
create index idx_6 ON kl_auth_flow_203006 (processor);
create index idx_7 ON kl_auth_flow_203006 (processor_request_id);
create index idx_8 ON kl_auth_flow_203006 (processor_trans_id);
create index idx_9 ON kl_auth_flow_203006 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_203006 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_203006 (clear_flag);
create index idx_12 ON kl_auth_flow_203006 (release_flag);
create index idx_13 ON kl_auth_flow_203006 (release_time);
create index idx_14 ON kl_auth_flow_203006 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_203007 (create_time);
create index idx_1 ON kl_auth_flow_203007 (merchant_no);
create index idx_2 ON kl_auth_flow_203007 (customer_id);
create index idx_3 ON kl_auth_flow_203007 (gateway_card_id);
create index idx_4 ON kl_auth_flow_203007 (trans_type);
create index idx_5 ON kl_auth_flow_203007 (status);
create index idx_6 ON kl_auth_flow_203007 (processor);
create index idx_7 ON kl_auth_flow_203007 (processor_request_id);
create index idx_8 ON kl_auth_flow_203007 (processor_trans_id);
create index idx_9 ON kl_auth_flow_203007 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_203007 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_203007 (clear_flag);
create index idx_12 ON kl_auth_flow_203007 (release_flag);
create index idx_13 ON kl_auth_flow_203007 (release_time);
create index idx_14 ON kl_auth_flow_203007 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_203008 (create_time);
create index idx_1 ON kl_auth_flow_203008 (merchant_no);
create index idx_2 ON kl_auth_flow_203008 (customer_id);
create index idx_3 ON kl_auth_flow_203008 (gateway_card_id);
create index idx_4 ON kl_auth_flow_203008 (trans_type);
create index idx_5 ON kl_auth_flow_203008 (status);
create index idx_6 ON kl_auth_flow_203008 (processor);
create index idx_7 ON kl_auth_flow_203008 (processor_request_id);
create index idx_8 ON kl_auth_flow_203008 (processor_trans_id);
create index idx_9 ON kl_auth_flow_203008 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_203008 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_203008 (clear_flag);
create index idx_12 ON kl_auth_flow_203008 (release_flag);
create index idx_13 ON kl_auth_flow_203008 (release_time);
create index idx_14 ON kl_auth_flow_203008 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_203009 (create_time);
create index idx_1 ON kl_auth_flow_203009 (merchant_no);
create index idx_2 ON kl_auth_flow_203009 (customer_id);
create index idx_3 ON kl_auth_flow_203009 (gateway_card_id);
create index idx_4 ON kl_auth_flow_203009 (trans_type);
create index idx_5 ON kl_auth_flow_203009 (status);
create index idx_6 ON kl_auth_flow_203009 (processor);
create index idx_7 ON kl_auth_flow_203009 (processor_request_id);
create index idx_8 ON kl_auth_flow_203009 (processor_trans_id);
create index idx_9 ON kl_auth_flow_203009 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_203009 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_203009 (clear_flag);
create index idx_12 ON kl_auth_flow_203009 (release_flag);
create index idx_13 ON kl_auth_flow_203009 (release_time);
create index idx_14 ON kl_auth_flow_203009 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_203010 (create_time);
create index idx_1 ON kl_auth_flow_203010 (merchant_no);
create index idx_2 ON kl_auth_flow_203010 (customer_id);
create index idx_3 ON kl_auth_flow_203010 (gateway_card_id);
create index idx_4 ON kl_auth_flow_203010 (trans_type);
create index idx_5 ON kl_auth_flow_203010 (status);
create index idx_6 ON kl_auth_flow_203010 (processor);
create index idx_7 ON kl_auth_flow_203010 (processor_request_id);
create index idx_8 ON kl_auth_flow_203010 (processor_trans_id);
create index idx_9 ON kl_auth_flow_203010 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_203010 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_203010 (clear_flag);
create index idx_12 ON kl_auth_flow_203010 (release_flag);
create index idx_13 ON kl_auth_flow_203010 (release_time);
create index idx_14 ON kl_auth_flow_203010 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_203011 (create_time);
create index idx_1 ON kl_auth_flow_203011 (merchant_no);
create index idx_2 ON kl_auth_flow_203011 (customer_id);
create index idx_3 ON kl_auth_flow_203011 (gateway_card_id);
create index idx_4 ON kl_auth_flow_203011 (trans_type);
create index idx_5 ON kl_auth_flow_203011 (status);
create index idx_6 ON kl_auth_flow_203011 (processor);
create index idx_7 ON kl_auth_flow_203011 (processor_request_id);
create index idx_8 ON kl_auth_flow_203011 (processor_trans_id);
create index idx_9 ON kl_auth_flow_203011 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_203011 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_203011 (clear_flag);
create index idx_12 ON kl_auth_flow_203011 (release_flag);
create index idx_13 ON kl_auth_flow_203011 (release_time);
create index idx_14 ON kl_auth_flow_203011 (third_party_authorization_flag);
create index idx_0 ON kl_auth_flow_203012 (create_time);
create index idx_1 ON kl_auth_flow_203012 (merchant_no);
create index idx_2 ON kl_auth_flow_203012 (customer_id);
create index idx_3 ON kl_auth_flow_203012 (gateway_card_id);
create index idx_4 ON kl_auth_flow_203012 (trans_type);
create index idx_5 ON kl_auth_flow_203012 (status);
create index idx_6 ON kl_auth_flow_203012 (processor);
create index idx_7 ON kl_auth_flow_203012 (processor_request_id);
create index idx_8 ON kl_auth_flow_203012 (processor_trans_id);
create index idx_9 ON kl_auth_flow_203012 (acquire_reference_no);
create index idx_10 ON kl_auth_flow_203012 (card_acceptor_id);
create index idx_11 ON kl_auth_flow_203012 (clear_flag);
create index idx_12 ON kl_auth_flow_203012 (release_flag);
create index idx_13 ON kl_auth_flow_203012 (release_time);
create index idx_14 ON kl_auth_flow_203012 (third_party_authorization_flag);
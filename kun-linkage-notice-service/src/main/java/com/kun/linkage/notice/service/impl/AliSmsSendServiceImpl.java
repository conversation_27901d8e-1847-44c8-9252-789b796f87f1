package com.kun.linkage.notice.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.dysmsapi20180501.models.*;
import com.kun.linkage.notice.config.AlibabaSmsConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.dysmsapi20180501.Client;

import javax.annotation.Resource;

@Slf4j
@Service
public class AliSmsSendServiceImpl {

    @Resource
    private AlibabaSmsConfig alibabaSmsConfig;

    private volatile static Client globeClient = null;
    private volatile static Client client = null;

    /**
     * 国际初始化
     * @return
     * @throws Exception
     */
    private Client getGlobeClient() throws Exception {
        if (null == globeClient) {
            synchronized (AliSmsSendServiceImpl.class) {
                if (null == globeClient) {
                    Config config = new Config()
                            .setAccessKeyId(alibabaSmsConfig.getAccessKeyId())
                            .setAccessKeySecret(alibabaSmsConfig.getAccessKeySecret());
                    config.endpoint = alibabaSmsConfig.getGlobeEndpoint();
                    globeClient = new Client(config);
                }
            }

        }
        return globeClient;
    }

    private Client getClient() throws Exception {
        if (null == client) {
            synchronized (AliSmsSendServiceImpl.class) {
                if (null == client) {
                    Config config = new Config()
                            .setAccessKeyId(alibabaSmsConfig.getAccessKeyId())
                            .setAccessKeySecret(alibabaSmsConfig.getAccessKeySecret());
                    config.endpoint = alibabaSmsConfig.getGlobeEndpoint();
                    client = new Client(config);
                }
            }

        }
        return client;
    }



    /**
     * 发送短信到港澳台及中国境外
     * @param to 接收方号码。号码格式为：国际区号+号码 例如：8521245567****
     * @param from 可以为空; 发送方号码。支持 Sender ID 的发送，只允许数字、字母，含有字母标识最长 11 位，纯数字标识支持 15 位。
     * @param message 短信的内容。
     * @return 成功或者失败
     */
    public Boolean sendMessageToGlobe(String to, String from, String message) {
        try {
            globeClient = getGlobeClient();
            SendMessageToGlobeRequest sendMessageToGlobeRequest = new SendMessageToGlobeRequest();

            sendMessageToGlobeRequest.setMessage(message);
            sendMessageToGlobeRequest.setTo(to);
            if(StringUtils.isNotBlank(from)) {
                sendMessageToGlobeRequest.setFrom(from);
            }

            SendMessageToGlobeResponse sendMessageToGlobeResponse = globeClient.sendMessageToGlobe(sendMessageToGlobeRequest);
            log.info("SendMessageToGlobeResponse: " + JSON.toJSONString(sendMessageToGlobeResponse));

            SendMessageToGlobeResponseBody body = sendMessageToGlobeResponse.getBody();
            if(body != null && "OK".equals(body.getResponseCode())) {
                return true;
            }

        } catch (Exception e) {
            log.error("短信调用发送失败,to:{}; from:{};message:{}; 失败原因",to,from,message,e);
        }
        return false;
    }

    /**
     * 使用短信模板发送短信，只支持发往中国内地 86 的手机区号
     * @param to 接收方号码。号码格式为：国际区号+号码 例如：861503871****
     * @param from 发送方标识短信签名名称
     * @param templateCode 短信模板编码
     * @param templateParam 短信模板变量对应的实际值 example {"code":"1234","product":"ytx"}
     * @return 成功失败
     */
    public Boolean sendMessageWithTemplate(String to, String from, String templateCode,String templateParam) {

        try {
            client = getClient();

            SendMessageWithTemplateRequest sendMessageWithTemplateRequest = new SendMessageWithTemplateRequest();

            sendMessageWithTemplateRequest.setTo(to);
            sendMessageWithTemplateRequest.setFrom(alibabaSmsConfig.getSignName());
            sendMessageWithTemplateRequest.setTemplateCode(templateCode);
            sendMessageWithTemplateRequest.setTemplateParam(templateParam);

            SendMessageWithTemplateResponse sendMessageWithTemplateResponse = client.sendMessageWithTemplate(sendMessageWithTemplateRequest);
            log.info("SendMessageWithTemplateResponse: " + JSON.toJSONString(sendMessageWithTemplateResponse));

            SendMessageWithTemplateResponseBody body = sendMessageWithTemplateResponse.getBody();
            if(body != null && "OK".equals(body.getResponseCode())) {
                return true;
            }
        } catch (Exception e) {
            log.error("短信调用发送失败,to:{}; from:{};templateCode:{};templateParam:{}; 失败原因",to,from,templateCode,templateParam,e);
        }
        return false;
    }

}

package com.kun.linkage.notice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kun.linkage.common.db.entity.EmailTemplate;
import com.kun.linkage.common.db.mapper.EmailTemplateMapper;
import com.kun.linkage.facade.enums.TemplateStatus;
import com.kun.linkage.notice.service.EmailTemplateService;
import org.springframework.stereotype.Service;

@Service
public class EmailTemplateServiceImpl extends ServiceImpl<EmailTemplateMapper, EmailTemplate> implements EmailTemplateService {

    @Override
    public EmailTemplate selectByTemplateId(String templateNo,String templateLanguage) {
        LambdaQueryWrapper<EmailTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmailTemplate::getTemplateNo, templateNo);
        queryWrapper.eq(EmailTemplate::getTemplateLanguage, templateLanguage);
        queryWrapper.eq(EmailTemplate::getStatus, TemplateStatus.ACTIVE.getValue());
        return baseMapper.selectOne(queryWrapper);
    }
}

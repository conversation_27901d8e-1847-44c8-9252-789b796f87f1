package com.kun.linkage.notice.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.kun.linkage.common.redis.utils.RedissonCacheUtil;
import com.kun.linkage.facade.api.bean.req.SenEmailReq;
import com.kun.linkage.facade.constants.RedisKeyConstant;
import com.kun.linkage.facade.enums.MailPoolEnum;
import com.kun.linkage.notice.config.MailPoolUPlusConfig;
import com.kun.linkage.notice.service.impl.AliSmsSendServiceImpl;
import com.kun.linkage.notice.service.impl.MessageSendServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("test")
public class TestController {

    @Resource
    private AliSmsSendServiceImpl aliSmsSendServiceImpl;
    @Resource
    private MessageSendServiceImpl messageSendServiceImpl;
    @Resource
    private RedissonCacheUtil redissonCacheUtil;
    @Resource
    private MailPoolUPlusConfig mailPoolUPlusConfig;

    @GetMapping("/sms")
    public Boolean testSms(@RequestParam String mobile) {
        Boolean b = aliSmsSendServiceImpl.sendMessageToGlobe(mobile, null, "尊敬的用户，您好！您的验证码是：654321。请在 5 分钟内完成注册操作。如非本人操作，请忽略此短信。感谢您的使用！");
        return b;
    }

    @GetMapping("/email")
    public Boolean testEmail(@RequestParam String templateContent,@RequestParam String recipientEmail) {
        String emailTitle = "邮箱标题";
        SenEmailReq senEmailReq = new SenEmailReq();
        senEmailReq.setRecipientEmail(recipientEmail);
        senEmailReq.setTemplateNo("UPtest1");
        return messageSendServiceImpl.sendEmailPool(templateContent,emailTitle,senEmailReq);
    }


    @GetMapping("/removeRedis")
    public Boolean removeRedis() {
        String today = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        String redisKey = RedisKeyConstant.KL_NOTICE_SEND_SMS_LIMIT+ today;
        String accountTotalRedisKey = RedisKeyConstant.KL_NOTICE_SEND_SMS_LIMIT+ today;
        List<String> recipientEmailList = Arrays.asList(mailPoolUPlusConfig.getAccount().split(","));
        for (String email : recipientEmailList) {
            redissonCacheUtil.delete(redisKey+email);
        }

        redissonCacheUtil.delete(accountTotalRedisKey);
        return true;
    }
}

package com.kun.linkage.notice.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kun.linkage.common.db.entity.MessageSendRecord;
import com.kun.linkage.facade.api.bean.MessageNoticeBaseBean;

public interface MessageSendRecordService extends IService<MessageSendRecord> {

    /**
     * 新增短信/邮件发送记录
     * @param messageNoticeBaseBean 消息通知数据
     * @return true;false
     */
    boolean addMessage(MessageNoticeBaseBean messageNoticeBaseBean);
}

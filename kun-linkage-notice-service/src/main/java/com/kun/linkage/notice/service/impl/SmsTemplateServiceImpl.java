package com.kun.linkage.notice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kun.linkage.common.db.entity.SmsTemplate;
import com.kun.linkage.common.db.mapper.SmsTemplateMapper;
import com.kun.linkage.facade.enums.TemplateStatus;
import com.kun.linkage.notice.service.SmsTemplateService;
import org.springframework.stereotype.Service;

@Service
public class SmsTemplateServiceImpl extends ServiceImpl<SmsTemplateMapper, SmsTemplate> implements SmsTemplateService {


    @Override
    public SmsTemplate selectByTemplateId(String templateNo,String templateLanguage) {
        LambdaQueryWrapper<SmsTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SmsTemplate::getTemplateNo, templateNo);
        queryWrapper.eq(SmsTemplate::getTemplateLanguage, templateLanguage);
        queryWrapper.eq(SmsTemplate::getStatus, TemplateStatus.ACTIVE.getValue());
        return baseMapper.selectOne(queryWrapper);
    }
}

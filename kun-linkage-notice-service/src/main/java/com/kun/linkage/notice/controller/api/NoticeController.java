package com.kun.linkage.notice.controller.api;


import com.alibaba.fastjson.JSON;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.facade.api.bean.req.OtpNoticeReq;
import com.kun.linkage.facade.api.bean.res.OtpNoticeRes;
import com.kun.linkage.notice.service.impl.NoticeServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("api/notice")
public class NoticeController {

    private static final Logger log = LoggerFactory.getLogger(NoticeController.class);

    @Autowired
    private NoticeServiceImpl noticeService;

    /**
     * opt通知转发接口
     * @param otpNoticeReq 请求入参
     * @return 成功或者失败，看具体响应码
     */
    @PostMapping("/opt")
    public OtpNoticeRes optNotice(@RequestBody OtpNoticeReq otpNoticeReq) {
        log.info("[opt通知转发]入参:{}", JSON.toJSONString(otpNoticeReq));
        OtpNoticeRes otpNoticeRes = new OtpNoticeRes();
        otpNoticeRes.setRequestId(otpNoticeReq.getRequestId());
        otpNoticeRes.setReturnCode(CommonTipConstant.SUCCESS);
        otpNoticeRes.setErrorMessage("Success");
        try {
            return noticeService.optNotice(otpNoticeReq,otpNoticeRes);
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            otpNoticeRes.setReturnCode("9999");
            otpNoticeRes.setErrorMessage("System inside error");
           return otpNoticeRes;
        }
    }
}

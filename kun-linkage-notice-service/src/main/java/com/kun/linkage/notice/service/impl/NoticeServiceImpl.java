package com.kun.linkage.notice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.db.entity.CardInfo;
import com.kun.linkage.common.db.mapper.CardInfoMapper;
import com.kun.linkage.facade.api.bean.req.OtpNoticeReq;
import com.kun.linkage.facade.api.bean.res.OtpNoticeRes;
import com.kun.linkage.facade.api.bean.res.OtpNoticeResData;
import com.kun.linkage.facade.enums.ChannelCodeEnum;
import com.kun.linkage.facade.enums.SystemEnum;
import com.kun.linkage.notice.external.facade.api.OtpNoticeBaseReq;
import com.kun.linkage.notice.external.facade.api.OtpNoticeBaseRes;
import com.kun.linkage.notice.external.facade.api.kl.KLOtpNoticeFacade;
import com.kun.linkage.notice.external.facade.api.vcc.VccOtpNoticeFacade;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.github.pagehelper.util.MetaObjectUtil.method;

@Service
public class NoticeServiceImpl {

    private static final Logger log = LoggerFactory.getLogger(NoticeServiceImpl.class);

    @Resource
    private CardInfoMapper cardInfoMapper;
    @Resource
    private KLOtpNoticeFacade klOtpNoticeFacade;
    @Resource
    private VccOtpNoticeFacade vccOtpNoticeFacade;


    /**
     * otp 通知转发；调用对应的系统然后拿到通知
     * 调用链路 YEEWALLEX ====> 调用到通知项目 ======> 根据调用方的cardId 转到对应的系统KL/VCC =====> KL/VCC 给到发短信还是邮件 =====>返回给YEEWALLEX
     * @param otpNoticeReq otp 通知转发
     * @return 成功失败
     */
    public OtpNoticeRes optNotice(OtpNoticeReq otpNoticeReq,OtpNoticeRes otpNoticeRes) {

        //目前只有YEEWALLEX发送通知短信
        ChannelCodeEnum channelCodeEnum = ChannelCodeEnum.BPC_GW;


        String cardId = otpNoticeReq.getCardId();
        LambdaQueryWrapper<CardInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CardInfo::getCardId, cardId)
                        .eq(CardInfo::getChannelNo,channelCodeEnum.getCode());

        List<CardInfo> cardInfos = cardInfoMapper.selectList(queryWrapper);

        if((null == cardInfos || cardInfos.isEmpty()) || cardInfos.size() > 1) {
            log.error("根据cardId查询对应卡信息不存在;cardId:{}", cardId);
            otpNoticeRes.setReturnCode("9999");
            otpNoticeRes.setErrorMessage("cardId data not found");
            return otpNoticeRes;
        }

        CardInfo cardInfo = cardInfos.get(0);
        //卡号后4位
        String lastFour = cardInfo.getLastFour();
        String kcardId = cardInfo.getKcardId();
        //调用对应的系统;下发短信通知
        String system = cardInfo.getSystem();

        SystemEnum systemEnum = SystemEnum.getEnumByValue(system);
        if(null == systemEnum){
            log.error("转发opt通知,未知的发送来源系统;system:{}", system);
            otpNoticeRes.setReturnCode("9999");
            otpNoticeRes.setErrorMessage("cardId not exist");
            return otpNoticeRes;
        }

        OtpNoticeResData otpNoticeResData = null;

        //拼接请求参数
        OtpNoticeBaseReq otpNoticeBaseReq = new OtpNoticeBaseReq(otpNoticeRes.getRequestId(),kcardId,lastFour,otpNoticeReq.getPhone(),
                otpNoticeReq.getEmail(),otpNoticeReq.getPwd(),otpNoticeReq.getAmount(), otpNoticeReq.getCrnc(),otpNoticeReq.getMrchtName());

        switch (systemEnum) {
            case VCC:

                Object vccResult = vccOtpNoticeFacade.optNotice(otpNoticeBaseReq);
                log.info("调用cvv 接口返回结果:{}", JSON.toJSONString(vccResult));
                if(null != vccResult && JSON.parseObject(JSON.toJSONString(vccResult)).getInteger("status").equals(200)) {
                    JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(vccResult));
                    OtpNoticeBaseRes otpNoticeBaseRes = jsonObject.getJSONObject("data").toJavaObject(OtpNoticeBaseRes.class);
                    //调用对应的系统拿到返回的成功失败,以及对应的message,给出去的参数需要把cardId 替换，然后把卡号后4位给加上
                    otpNoticeResData = new OtpNoticeResData(otpNoticeBaseRes.getMethod(),otpNoticeBaseRes.getDestination());
                }else {
                    log.error("转发调用Vcc opt通知,接口失败;返回的结果:{}", JSON.toJSONString(vccResult));
                    otpNoticeRes.setReturnCode("9999");
                    otpNoticeRes.setErrorMessage("unknow error");
                    return otpNoticeRes;
                }

                break;
            case KL:

                /*if(true){
                    String email = cardInfo.getEmail();
                    otpNoticeResData = new OtpNoticeResData(2,email);
                    otpNoticeRes.setData(otpNoticeResData);
                    return  otpNoticeRes;
                }*/
                //这里调用的是KL接口
                Result<OtpNoticeBaseRes> otpNoticeBaseResResult = klOtpNoticeFacade.optNotice(otpNoticeBaseReq);
                if(Result.isSuccess(otpNoticeBaseResResult)) {
                    OtpNoticeBaseRes otpNoticeBaseRes = otpNoticeBaseResResult.getData();
                    //调用对应的系统拿到返回的成功失败,以及对应的message,给出去的参数需要把cardId 替换，然后把卡号后4位给加上
                    otpNoticeResData = new OtpNoticeResData(otpNoticeBaseRes.getMethod(),otpNoticeBaseRes.getDestination());
                }else {
                    log.error("转发调用kun-linkage opt通知,接口失败;返回的结果:{}", JSON.toJSONString(otpNoticeBaseResResult));
                    otpNoticeRes.setReturnCode("9999");
                    otpNoticeRes.setErrorMessage("unknow error");
                    return otpNoticeRes;
                }
                break;
            default:
                log.error("转发opt通知,未知的发送类型;method:{}", method);
                otpNoticeRes.setReturnCode("9999");
                otpNoticeRes.setErrorMessage("param method error");
                return otpNoticeRes;
        }

        otpNoticeRes.setData(otpNoticeResData);
        return otpNoticeRes;

    }

}

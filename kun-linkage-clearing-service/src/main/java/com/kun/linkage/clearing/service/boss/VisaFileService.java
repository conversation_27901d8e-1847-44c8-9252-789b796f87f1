package com.kun.linkage.clearing.service.boss;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.aws.AwsS3Util;
import com.kun.common.util.aws.AwzS3Properties;
import com.kun.linkage.clearing.config.BpcFileconfig;
import com.kun.linkage.clearing.enums.ChannelSourceEnum;
import com.kun.linkage.clearing.facade.vo.boss.KCClearingPageResVO;
import com.kun.linkage.clearing.facade.vo.boss.VisaRepotPageVO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.VisaFile;
import com.kun.linkage.common.db.mapper.VisaFileMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class VisaFileService {

    @Resource
    private VisaFileMapper visaFileMapper;
    @Resource
    private BpcFileconfig bpcFileconfig;
    @Resource
    private AwzS3Properties awzS3Properties;
    @Resource
    private AwsS3Util awsS3Util;
    @Value("${kun.aws.s3.fileFolder:}")
    private String fileFolder;


    /**
     * 分页查询
     * @param pageQueryVO
     * @return
     */
    public Result<PageResult<KCClearingPageResVO>> pageList(VisaRepotPageVO pageQueryVO) {
        PageResult<VisaFile> page = PageHelperUtil.getPage(pageQueryVO, () -> {
            return visaFileMapper.selectList(
                    Wrappers.<VisaFile>lambdaQuery()
                            .ge(null != pageQueryVO.getFileStartTime(), VisaFile::getFileTime, pageQueryVO.getFileStartTime())
                            .le(null != pageQueryVO.getFileEndTime(), VisaFile::getFileTime, pageQueryVO.getFileEndTime())
                            .eq(StringUtils.isNotBlank(pageQueryVO.getChannelSource()), VisaFile::getChannelSource, pageQueryVO.getChannelSource())
                            .eq(null != pageQueryVO.getFileTime(), VisaFile::getFileTime, pageQueryVO.getFileTime())
                            .orderByDesc(VisaFile::getDownloadTime));
        });

        PageResult<KCClearingPageResVO> result = convertToPageResult(page);
        return Result.success(result);
    }

    /**
     * 数据转换
     * @param page
     * @return
     */
    private PageResult<KCClearingPageResVO> convertToPageResult(PageResult<VisaFile> page) {
        List<KCClearingPageResVO> list = page.getData().stream().map( visaFile ->{
            KCClearingPageResVO vo = new KCClearingPageResVO();
            vo.setId(visaFile.getId().toString());
            vo.setChannelSource(visaFile.getChannelSource());
            vo.setOriginalFilename(visaFile.getOriginalFilename());
            vo.setFileName(visaFile.getFileName());
            vo.setFileType(visaFile.getFileType());
            vo.setStoragePath(visaFile.getStoragePath());
            vo.setFileTime(visaFile.getFileTime());
            vo.setDownloadTime(visaFile.getDownloadTime());
            return vo;
        }).collect(Collectors.toList());
        return new PageResult<>(list,page.getPageNum(),page.getPageSize(),page.getTotal(),page.getExtraInfo());
    }

    /**
     * 根据文件时间和来源查询visa报表数据
     * @param fileTime
     * @param channelSource
     * @return
     */
    public VisaFile selectByFileTimeAndChannelSource(LocalDate fileTime, String channelSource) {
        LambdaQueryWrapper<VisaFile> queryWrapper = Wrappers.<VisaFile>lambdaQuery()
                .eq(VisaFile::getFileTime, fileTime)
                .eq(VisaFile::getChannelSource, channelSource);
        return visaFileMapper.selectOne(queryWrapper);
    }

    /**
     * 获取visa 报表
     * @param fileTime
     * @return
     */
    public Result<Boolean> getReport(LocalDate fileTime,String channelSource) {

        log.info("开始下载visa报表;fileTime:{},channelSource:{}", fileTime, channelSource);
        //判断下载日期是否已经有文件记录，如果已经存在就不下载了
        ChannelSourceEnum channelSourceEnum = ChannelSourceEnum.fromCode(channelSource);
        if(null == channelSourceEnum) {
            log.error("未知的通道数据;channelSource:{}", channelSource);
            return Result.fail(CommonTipConstant.REQUEST_PARAM_ERROR);
        }

        VisaFile visaFile = this.selectByFileTimeAndChannelSource(fileTime, channelSource);
        if(null != visaFile) {
            if(StringUtils.isNotBlank(visaFile.getStoragePath())){
                return Result.success(true);
            }
            //如果是空文件直接删除,方便后续下载
            visaFileMapper.deleteById(visaFile.getId());
        }

        // 创建 S3 客户端 (支持路径风格)
        BasicAWSCredentials awsCreds = new BasicAWSCredentials(bpcFileconfig.getAccessKeyId(), bpcFileconfig.getSecretAccessKey());
        AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(bpcFileconfig.getUrl(), bpcFileconfig.getRegion()))
                .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                .withPathStyleAccessEnabled(true) // 启用路径风格访问
                .build();

        // 格式化日期用于文件名匹配
        String datePattern = fileTime.format(DateTimeFormatter.ofPattern("yyMMdd"));
        log.info("搜索包含日期 {} 的文件", datePattern);

        // 列出并遍历 bucket 下所有文件
        ListObjectsV2Request listReq = new ListObjectsV2Request()
                .withBucketName(bpcFileconfig.getBucketName())
                .withPrefix(bpcFileconfig.getReportPath());

        ListObjectsV2Result listRes = s3Client.listObjectsV2(listReq);

        // 遍历文件列表，处理匹配的文件
        for (S3ObjectSummary summary : listRes.getObjectSummaries()) {
            String key = summary.getKey();
            log.info("检查文件: {}", key);

            // 匹配包含目标日期的文件
            if (key.contains(datePattern)) {
                log.info("找到匹配文件: {}", key);

                // 调用工具方法：从bpc S3下载并上传到本地S3
                String url = downloadFromSourceS3AndUploadToLocalS3(s3Client, bpcFileconfig.getBucketName(), key,"VISAReport");
                if (url == null) {
                    log.error("文件处理失败，key:{}", key);
                    return Result.fail(CommonTipConstant.FAIL);
                }

                // 提取原始文件名，保存记录到数据库
                String originalFilename = key.substring(key.lastIndexOf('/') + 1);
                String fileName = generateUniqueFileName(originalFilename);
                String fileType = getFileType(originalFilename);
                this.insertVisa(originalFilename, fileName, fileType, url, fileTime, channelSource);

                log.info("文件 {} 已成功上传并记录，URL:{}", key, url);
                return Result.success(true);
            }
        }

        //未找到匹配文件
        log.info("未找到包含日期 {} 的文件", datePattern);
        return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
    }

    /**
     * 从bpc S3 服务器下载文件上传到自己的S3服务器
     * @param s3Client bpc S3 服务器连接
     * @param bpcBucketName bpc 桶名称
     * @param key S3 中对象的完整路径标识符（包含路径和文件名）
     * @return
     */
    public String downloadFromSourceS3AndUploadToLocalS3(AmazonS3 s3Client,String bpcBucketName,String key,String downloadFileFolder){

        if (s3Client == null || StringUtils.isEmpty(bpcBucketName) || StringUtils.isEmpty(key)) {
            log.error("参数异常: s3Client={}, bpcBucketName={}, key={}", s3Client, bpcBucketName, key);
            return null;
        }

        // 2. 解析源文件信息
        String originalFilename = key.substring(key.lastIndexOf('/') + 1); // 提取原始文件名
        String suffix = originalFilename.contains(".") ? originalFilename.substring(originalFilename.lastIndexOf('.') + 1) : ""; // 提取文件后缀

        // 3. 定义临时文件路径（使用系统临时目录）
        Path localFilePath;
        try {
            // 创建临时文件（自动生成文件名，避免冲突）
            localFilePath = Files.createTempFile("download_", "." + (StringUtils.isEmpty(suffix) ? "tmp" : suffix));
        } catch (IOException e) {
            log.error("创建本地临时文件失败", e);
            return null;
        }

        try (S3Object s3Object = s3Client.getObject(new GetObjectRequest(bpcBucketName, key));
             S3ObjectInputStream inputStream = s3Object.getObjectContent();
             OutputStream fileOutputStream = Files.newOutputStream(localFilePath)) {

            // 4. 下载文件到本地临时文件
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, bytesRead);
            }
            log.info("文件已成功下载到本地临时路径: {}", localFilePath);

            // 5. 生成目标S3的唯一文件名（避免覆盖）
            String fileName = generateUniqueFileName(originalFilename);

            // 6. 上传到目标S3桶
            File tempFile = localFilePath.toFile();
            String url = awsS3Util.uploadChunkedFile(fileName, tempFile, awzS3Properties.getBucket(), fileFolder+"/"+downloadFileFolder, CannedAccessControlList.PublicReadWrite);

            log.info("文件上传成功: 源文件={}, 目标URL={}", key, url);
            return url;

        } catch (IOException e) {
            log.error("文件下载或写入本地失败; key={}", key, e);
            return null;
        } catch (Exception e) { // 捕获上传过程中的所有异常
            log.error("文件上传到目标S3失败; key={}", key, e);
            return null;
        } finally {
            // 7. 清理本地临时文件（无论成功失败都删除）
            try {
                Files.deleteIfExists(localFilePath);
                log.info("本地临时文件已清理: {}", localFilePath);
            } catch (IOException e) {
                log.warn("清理本地临时文件失败: {}", localFilePath, e);
                // 非致命错误，不影响返回结果
            }
        }
    }



    // 生成唯一文件名
    private String generateUniqueFileName(String originalFilename) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        String baseName = FilenameUtils.getBaseName(originalFilename);
        String extension = FilenameUtils.getExtension(originalFilename);
        return baseName + "_" + timestamp + (extension.isEmpty() ? "" : "." + extension);
    }

    // 获取文件类型
    private String getFileType(String fileName) {
        return FilenameUtils.getExtension(fileName).toUpperCase();
    }

    // 递归删除目录及其内容的辅助方法
    private static void deleteDirectory(Path directory) throws IOException {
        Files.walk(directory)
                .sorted(Comparator.reverseOrder())
                .map(Path::toFile)
                .forEach(File::delete);
    }

    /**
     * 新增visa报表文件
     * @param originalFilename
     * @param fileName
     * @param fileType
     * @param storagePath
     * @param fileTime
     * @return
     */
    public VisaFile insertVisa(String originalFilename,String fileName,String fileType,String storagePath,LocalDate fileTime,String channelSource) {
        VisaFile visaFile = new VisaFile();
        visaFile.setChannelSource(channelSource);
        visaFile.setOriginalFilename(originalFilename);
        visaFile.setFileName(fileName);
        visaFile.setFileType(fileType);
        visaFile.setStoragePath(storagePath);
        visaFile.setFileTime(fileTime);
        visaFile.setDownloadTime(new Date());
        visaFileMapper.insert(visaFile);
        return visaFile;

    }
}

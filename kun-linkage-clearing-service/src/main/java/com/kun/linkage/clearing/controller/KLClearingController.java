package com.kun.linkage.clearing.controller;

import com.kun.linkage.clearing.facade.vo.kunlinkage.PostTransRequestVO;
import com.kun.linkage.clearing.facade.vo.kunlinkage.PostTransResponseVO;
import com.kun.linkage.clearing.service.kunlinkage.KLClearingHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/api/v1/kl-clearing")
public class KLClearingController {

    private static final Logger log = LoggerFactory.getLogger(KLClearingController.class);

    @Resource
    private KLClearingHandler klClearingHandler;

    @PostMapping("/process")
    public PostTransResponseVO processClearing(@RequestBody PostTransRequestVO postTransRequestVO) {
        return klClearingHandler.handleClearing(postTransRequestVO);
    }
}

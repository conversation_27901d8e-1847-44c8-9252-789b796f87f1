package com.kun.linkage.clearing.controller.boss;

import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.clearing.enums.ChannelSourceEnum;
import com.kun.linkage.clearing.facade.vo.boss.KCClearingPageResVO;
import com.kun.linkage.clearing.facade.vo.boss.VisaRepotPageVO;
import com.kun.linkage.clearing.service.boss.VisaFileService;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.annotation.EnumValue;
import com.kun.linkage.common.base.page.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;

@Tag(name = "visa报表", description = "visa报表")
@RestController
@RequestMapping("/boss/visa/report")
public class VisaFileController extends BaseVccBossController {

    @Resource
    private VisaFileService visaFileService;

    /**
     * 分页查询visa报表数据
     *
     * @param pageQueryVO
     * @return
     */
    @Operation(description = "分页查询visa报表数据", summary = "分页查询visa报表数据")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-visa-report"})
    @PostMapping("/pageList")
    public Result<PageResult<KCClearingPageResVO>> pageList(@RequestBody VisaRepotPageVO pageQueryVO) {
        return visaFileService.pageList(pageQueryVO);
    }


    /**
     * 获取visa报表
     *
     * @param
     * @return
     */
    @Operation(description = "获取visa报表", summary = "获取visa报表")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-visa-report"})
    @GetMapping("/getReport")
    public Result<Boolean> getReport(@RequestParam(name = "fileTime") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fileTime,
                                     @RequestParam(name = "channelSource") String channelSource) {
        return visaFileService.getReport(fileTime,channelSource);
    }


}

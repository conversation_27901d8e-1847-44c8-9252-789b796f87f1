package com.kun.linkage.clearing.task;

import brave.Tracer;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.kun.linkage.clearing.facade.vo.kunlinkage.PostTransRequestVO;
import com.kun.linkage.clearing.facade.vo.kunlinkage.PostTransResponseVO;
import com.kun.linkage.clearing.service.kunlinkage.KLClearingHandler;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.db.entity.ManualTriggerParam;
import com.kun.linkage.common.db.mapper.ManualTriggerParamMapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class KLClearingTrigger {

    @Resource
    private KLClearingHandler klClearingHandler;

    @Resource
    private ManualTriggerParamMapper manualTriggerParamMapper;

    @Resource
    Tracer tracer;

    @XxlJob("KLClearingTrigger")
    @NewSpan
    public boolean triggerClearing() {
        XxlJobHelper.log(">>> KLClearingTrigger 开始处理清算任务, traceId: {}, spandId:{}",
            tracer.currentSpan().context().traceIdString(), tracer.currentSpan().context().spanIdString());
        log.info(">>> KLClearingTrigger 开始处理清算任务");
        try {
            String ids = XxlJobHelper.getJobParam();
            XxlJobHelper.log("接收参数：" + ids);
            if (StringUtils.isBlank(ids)) {
                log.info("KLClearingTrigger 必须带参数执行,不带参数执行直接返回成功");
                return XxlJobHelper.handleFail("KLClearingTrigger 必须带参数执行");
            }
            String[] split = ids.split(",");
            if (split.length == 0) {
                log.info("KLClearingTrigger 接收参数为空");
                return XxlJobHelper.handleFail("KLClearingTrigger 接收参数为空");
            }
            for (int i = 0; i < split.length; i++) {
                split[i] = split[i].trim();
            }
            // 校验参数都是Long类型的ID
            for (String id : split) {
                if (!StringUtils.isNumeric(id.trim())) {
                    log.warn("KLClearingTrigger 接收参数无效, 非法ID: {}", id);
                    return XxlJobHelper.handleFail("接收参数无效, 非法ID: " + id);
                }
            }
            List<ManualTriggerParam> manualTriggerParams = manualTriggerParamMapper.selectList(
                new QueryWrapper<ManualTriggerParam>().lambda().in(ManualTriggerParam::getId, Arrays.asList(split)));
            log.info("KLClearingTrigger 查询到手动触发参数数据条数: {}", manualTriggerParams.size());
            if (manualTriggerParams.isEmpty()) {
                log.info("KLClearingTrigger 接收参数无效, 无法找到对应的手动触发参数");
                return XxlJobHelper.log("接收参数无效, 无法找到对应的手动触发参数");
            }
            // 从manualTriggerParams中找出不在split中的ID
            List<Integer> idsList =
                manualTriggerParams.stream().map(ManualTriggerParam::getId).collect(Collectors.toList());
            List<String> invalidIds =
                Arrays.stream(split).filter(id -> !idsList.contains(Integer.parseInt(id))).collect(Collectors.toList());
            if (!invalidIds.isEmpty()) {
                log.warn("KLClearingTrigger 接收参数无效, 无法找到对应的手动触发参数: {}", invalidIds);
                return XxlJobHelper.handleFail("无法找到对应的手动触发参数: " + invalidIds.toString());
            }
            for (ManualTriggerParam manualTriggerParam : manualTriggerParams) {
                if (StringUtils.isBlank(manualTriggerParam.getParam())) {
                    log.error("KLClearingTrigger 接收参数无效, 手动触发参数为空: {}", manualTriggerParam.getId());
                    XxlJobHelper.log("接收参数无效, 手动触发参数为空: {}", manualTriggerParam.getId());
                    continue;
                }
                PostTransRequestVO postTransRequestVO =
                    JSON.parseObject(manualTriggerParam.getParam(), PostTransRequestVO.class);
                if (postTransRequestVO == null) {
                    log.error("KLClearingTrigger 接收参数解析失败, 手动触发参数ID: {}", manualTriggerParam.getId());
                    XxlJobHelper.log("接收参数解析失败, 手动触发参数ID: {}", manualTriggerParam.getId());
                    continue;
                }
                PostTransResponseVO postTransResponseVO = klClearingHandler.handleClearing(postTransRequestVO);
                String resultJson = JSON.toJSONString(postTransResponseVO);
                if (postTransResponseVO == null) {
                    log.error("KLClearingTrigger 清算处理失败, 手动触发参数ID: {}", manualTriggerParam.getId());
                    XxlJobHelper.log("清算处理失败, 手动触发参数ID: {}", manualTriggerParam.getId());
                } else if (!CommonTipConstant.SUCCESS.equals(postTransResponseVO.getCode())) {
                    log.error("KLClearingTrigger 清算处理失败, 手动触发参数ID: {}, 响应: {}",
                        manualTriggerParam.getId(), resultJson);
                    XxlJobHelper.log("清算处理失败, 手动触发参数ID: {}, 响应: {}", manualTriggerParam.getId(), resultJson);
                } else {
                    log.info("KLClearingTrigger 清算处理成功, 手动触发参数ID: {}, 响应: {}", manualTriggerParam.getId(),
                        resultJson);
                    XxlJobHelper.log(
                        "清算处理成功, 手动触发参数ID: {}, 响应: {}", manualTriggerParam.getId(), resultJson);
                }
            }
            log.info(">>> KLClearingTrigger 清算任务处理完成, traceId: {}, spandId:{}",
                tracer.currentSpan().context().traceIdString(), tracer.currentSpan().context().spanIdString());
            return true;
        } catch (Exception e) {
            log.error(">>> KLClearingTrigger 清算任务处理异常, traceId: {}, spandId:{}, {}",
                tracer.currentSpan().context().traceIdString(), tracer.currentSpan().context().spanIdString(), e.getMessage(), e);
            return false;
        }
    }
}

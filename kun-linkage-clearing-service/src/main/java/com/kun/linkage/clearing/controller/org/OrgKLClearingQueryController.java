package com.kun.linkage.clearing.controller.org;

import com.kun.linkage.clearing.facade.constant.ClearingApplicationRequestParamNameConstant;
import com.kun.linkage.clearing.facade.constant.KunLinkageClearingResponseCodeEnum;
import com.kun.linkage.clearing.facade.vo.org.OrgClearingInquiryPageVO;
import com.kun.linkage.clearing.facade.vo.org.OrgClearingInquiryRequestVO;
import com.kun.linkage.clearing.service.kunlinkage.OrgKLClearingTransService;
import com.kun.linkage.clearing.utils.I18nMessageService;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "OrgKLClearingQueryController", description = "商户端清算数据查询")
@RestController
@RequestMapping("/org/clearing")
public class OrgKLClearingQueryController {

    @Resource
    private I18nMessageService i18nMessageService;

    @Resource
    private OrgKLClearingTransService clearingTransService;

    /**
     * 分页查询清算数据
     *
     * @param requestVO 查询请求对象
     * @return 分页查询结果
     */
    @Operation(description = "分页查询清算数据", summary = "分页查询清算数据")
    @PostMapping("/pageList")
    public Result<PageResult<OrgClearingInquiryPageVO>> pageList(@RequestBody OrgClearingInquiryRequestVO requestVO) {
        if (StringUtils.isBlank(requestVO.getClearingDateFrom())) {
            return Result.fail(KunLinkageClearingResponseCodeEnum.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageClearingResponseCodeEnum.PARAMETER_MISSING.getCode(),
                            i18nMessageService.getMessage(ClearingApplicationRequestParamNameConstant.CLEARING_DATE_FROM)));
        }
        if (StringUtils.isBlank(requestVO.getClearingDateUntil())) {
            return Result.fail(KunLinkageClearingResponseCodeEnum.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageClearingResponseCodeEnum.PARAMETER_MISSING.getCode(),
                            i18nMessageService.getMessage(ClearingApplicationRequestParamNameConstant.CLEARING_DATE_TO)));
        }
        PageResult<OrgClearingInquiryPageVO> pageList = clearingTransService.pageList(requestVO);
        return Result.success(pageList);
    }

}

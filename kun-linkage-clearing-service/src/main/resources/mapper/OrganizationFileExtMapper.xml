<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kun.linkage.clearing.ext.mapper.OrganizationFileExtMapper">
    <select id="pageListSettlementFileData"
            resultType="com.kun.linkage.clearing.facade.vo.kunlinkage.OrganizationSettlementFileVO">
        select kct.clearing_date                    as clearDate,
               kct.clearing_id                      as clearId,
               kct.original_clearing_id             as originalClearId,
               kct.auth_flow_id                     as transactionId,
               kct.gateway_card_id                  as cardTkId,
               kct.customer_id                      as customerTkId,
               kct.clearing_type                    as transactionType,
               kct.trans_currency                   as transactionCurrencyCode,
               kct.trans_amount                     as transactionAmount,
               kct.cardholder_billing_currency      as settlementCurrencyCode,
               kct.cardholder_markup_billing_amount as settlementAmount,
               kct.trans_time                       as transactionDatetime,
               kct.acquire_reference_no             as referenceNo,
               kct.approve_code                     as approveCode,
               kct.mcc                              as merchantType,
               kct.card_acceptor_id                 as cardAcceptorIdentification,
               kct.card_acceptor_name               as cardAcceptorName,
               kct.card_acceptor_city               as cardAcceptorCity,
               kct.card_acceptor_country_code       as cardAcceptorCountry
        from kl_clearing_trans_${partition} kct
        where kct.clearing_date = #{fileDate}
          and kct.merchant_no = #{organizationNo}
          and kct.clearing_id > #{startId}
          and kct.status = 'SUCCESS'
        order by kct.clearing_id asc limit 200
    </select>

    <select id="pageListReleaseFileData"
            resultType="com.kun.linkage.clearing.facade.vo.kunlinkage.OrganizationReleaseFileVO">
        select kal.id                             as transactionId,
               kal.gateway_card_id                as cardTkId,
               kal.customer_id                    as customerTkId,
               kal.trans_type                     as transactionType,
               kal.trans_currency                 as transactionCurrencyCode,
               kal.trans_amount                   as transactionAmount,
               kal.merchant_release_currency_code as settleCurrencyCode,
               kal.merchant_release_amount        as settleAmount,
               kal.cardholder_billing_currency    as cardholderCurrencyCode,
               kal.release_markup_billing_amount  as cardholderAmount,
               kal.acquire_reference_no           as referenceNo,
               kal.approve_code                   as approveCode,
               kal.mcc                            as merchantType,
               kal.card_acceptor_tid              as cardAcceptorTerminalCode,
               kal.card_acceptor_id               as cardAcceptorIdentification,
               kal.card_acceptor_name             as cardAcceptorName,
               kal.card_acceptor_city             as cardAcceptorCity,
               kal.card_acceptor_country_code     as cardAcceptorCountry
        from kl_auth_flow kal
        where kal.release_time between #{startReleaseTime} and #{endReleaseTime}
          and kal.create_time between #{startCreateTime} and #{endCreateTime}
          and kal.merchant_no = #{organizationNo}
          and kal.id > #{startId}
          and kal.status = 'SUCCESS'
        order by kal.id asc limit 200
    </select>
</mapper>
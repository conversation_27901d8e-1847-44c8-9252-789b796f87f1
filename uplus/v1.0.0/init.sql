-- ----------------------------
-- 用户信息表
-- ----------------------------
create table up_boss_user
(
    id          bigint       not null auto_increment
        primary key comment '主键',
    username    varchar(128) not null comment '用户名',
    account     varchar(64)  not null comment '账号',
    password    varchar(256) not null comment '密码',
    status      int          not null comment '状态',
    create_date datetime default null comment '创建时间',
    update_date datetime default null comment '更新时间',
    constraint uniq_boss_user_account unique (account)
) comment 'BOSS用户表';

-- ----------------------------
-- 角色信息表
-- ----------------------------
create table up_boss_role
(
    id          bigint      not null auto_increment primary key comment '主键',
    role_name   varchar(64) not null comment '角色名称',
    role_code   varchar(64) not null comment '角色编码',
    status      int         not null comment '角色状态',
    create_date datetime default null comment '创建时间',
    update_date datetime default null comment '更新时间',
    constraint uniq_boss_role_role_code
        unique (role_code),
    constraint uniq_boss_role_role_name
        unique (role_name)
) comment 'BOSS角色表';

-- ----------------------------
-- 用户和角色关联表  用户N-1角色
-- ----------------------------
create table up_boss_user_boss_role
(
    id           bigint   not null auto_increment primary key comment '主键',
    user_id      bigint   not null comment '用户ID',
    boss_role_id bigint   not null comment '角色ID',
    create_date  datetime null default null comment '创建时间',
    update_date  datetime null default null comment '更新时间',
    key idx_boss_user_role_0 (boss_role_id),
    key idx_boss_user_role_1 (user_id)
) comment 'BOSS用户角色关联表';

-- ----------------------------
-- 操作日志记录
-- ----------------------------
create table up_boss_operation_log
(
    id          bigint       not null auto_increment primary key comment '主键',
    operator_id bigint       not null comment '操作人ID',
    object      int          not null comment '操作对象类型',
    object_id   bigint       null comment '操作对象ID',
    type        int          not null comment '操作类型',
    subject     varchar(64)  null comment '操作主题',
    data_before varchar(256) null comment '操作前数据',
    data_after  varchar(256) null comment '操作后数据',
    remark      varchar(128) null comment '备注',
    create_date datetime default null comment '创建时间',
    update_date datetime default null comment '更新时间'
) comment 'BOSS操作日志表';

-- ----------------------------
-- 角色权限关联表
-- ----------------------------
create table up_boss_role_permission
(
    id            bigint not null auto_increment primary key comment '主键',
    boss_role_id  bigint not null comment '角色ID',
    permission_id bigint not null comment '权限ID',
    status        int    not null comment '状态',
    create_date   datetime default null comment '创建时间',
    update_date   datetime default null comment '更新时间'
) comment 'BOSS角色权限关联表';

-- ----------------------------
-- 权限信息表
-- ----------------------------
create table up_permission
(
    id          bigint      not null auto_increment primary key comment '主键',
    code        varchar(64) not null comment '权限编码',
    name        varchar(64) not null comment '权限名称',
    create_date datetime default null comment '创建时间',
    update_date datetime default null comment '更新时间'
) comment 'BOSS权限表';


create table up_kyc_audit_record
(
    audit_id          bigint comment '自增主键 id' primary key,
    app_user_login_id bigint       not null comment 'app用户登录主键id',
    case_no           varchar(64)  not null comment '案件号',
    case_type         varchar(12)  not null comment '案件类型 申请卡片、kyc升级、资料修改',
    kyc_level         varchar(64)  not null comment 'KYC等级 无、一级、二级、三级',
    kyc_level_list    varchar(64)  null comment 'KYC 提交的等级;LEVEL_1,LEVEL_2,LEVEL_3',
    merchant_name     varchar(128) null comment '商户名称',
    card_product_name varchar(128) null comment '卡产品名称',
    cardholder_name   varchar(12)  null comment '持卡人姓名',
    hit_result        varchar(12)  not null comment '命中结果 无、未命中、疑似命中',
    case_status       varchar(12)  null comment '案件状态 待审核、通过、拒绝、待补充',
    audit_description varchar(128) null comment '审核描述',
    remark            varchar(128) null comment '备注',
    create_user       varchar(32)  not null comment '创建人',
    create_time       datetime     not null comment '创建时间',
    update_user       varchar(32)  null comment '更新人',
    update_time       datetime     null comment '更新时间',
    unique KEY uniq_case_no (case_no)
)
    comment 'kyc信息审核表';

create table up_kyc_level1_info
(
    kyc_id                   bigint comment 'kyc一级认证信息主键ID' primary key,
    case_no                  varchar(64)   not null comment '案件号',
    app_user_login_id        bigint        not null comment 'app用户登录主键id',
    user_level               varchar(64)   null comment '用户等级',
    organization_no          varchar(32)   null comment '来源；UCard:ECT2025052799991;',
    agent_id                 bigint        null comment '代理商主键id',
    referrer_uuid            bigint        null comment '推荐人',
    referral_code            varchar(64)   null comment '推荐码',
    realname_flag            int default 0 null comment '是否实名;0:未实名;1:已实名',
    last_name                varchar(64)   null comment '姓',
    middle_name              varchar(64)   null comment '中间名',
    first_name               varchar(64)   null comment '名',
    id_type                  varchar(16)   null comment '证件类型',
    id_no                    varchar(255)  null comment '加密,证件号',
    masked_id_no             varchar(64)   null comment '脱敏的证件号',
    birth_date               varchar(10)   null comment '出生日期;yyyy/mm/dd',
    gender                   int           null comment '性别;1:男;2:女',
    id_issue_date            varchar(10)   null comment '证件签发日',
    id_expiry_date           varchar(10)   null comment '证件有效期 ',
    nationality              varchar(32)   null comment '国籍',
    country_code             varchar(3)    null comment '国家代码,3位字母',
    country_no               varchar(3)    null comment '国家地区代码,3位数字',
    id_card_front_image      varchar(256)  null comment '证件正面地址',
    face_photo_image         varchar(256)  null comment '人脸图片地址',
    email                    varchar(255)  null comment '邮箱',
    phone_area               varchar(5)    null comment '手机区号',
    mobile_no                varchar(32)   null comment '手机号码',
    residence_country_code   varchar(3)    null comment '居住地国家/地区;3位字母',
    residence_state_province varchar(255)  null comment '居住地州/省',
    residence_city           varchar(255)  null comment '居住地城市',
    residence_address_detail varchar(512)  null comment '居住地详细地址',
    postal_code              varchar(16)   null comment '邮编',
    avatar_url               varchar(256)  null comment '用户头像信息',
    create_datetime          datetime     null comment '创建时间',
    update_datetime          datetime     null comment '更新时间',
    unique KEY uniq_case_no (case_no)
)
    comment 'kyc一级认证信息';

create table up_kyc_level2_info
(
    kyc_id                bigint comment '自增主键id' primary key,
    case_no               varchar(64)  not null comment '案件号',
    app_user_info_id      bigint       null comment 'app用户主键id',
    app_user_login_id     bigint       null comment 'app用户登录主键id',
    address_proof_photo_1 varchar(255) null comment '地址证明图片1',
    address_proof_photo_2 varchar(255) null comment '地址证明图片2',
    address_proof_photo_3 varchar(255) null comment '地址证明图片3',
    create_time           datetime     null comment '创建时间',
    update_time           datetime     null comment '更新时间',
    KEY idx_case_no (case_no)
)
    comment 'kyc二级认证信息';

create table up_kyc_level3_info
(
    kyc_id                                    bigint comment '自增主键id' primary key,
    case_no                                   varchar(64)  not null comment '案件号',
    app_user_info_id                          bigint       null comment 'app用户主键id',
    app_user_login_id                         bigint       null comment 'app用户登录主键id',
    employment_status                         varchar(32)  null comment '雇佣状态；枚举',
    company_name                              varchar(32)  null comment '公司名称',
    industry                                  varchar(32)  null comment '行业;枚举',
    position                                  varchar(32)  null comment '职位;枚举',
    years_of_experience                       varchar(32)  null comment '工作年限；枚举',
    annual_income                             varchar(32)  null comment '年收入;枚举',
    asset_proof                               varchar(512) null comment '资产证明；枚举集合;'',''号分割',
    asset_proof_other_notes                   varchar(512) null comment '资产证明其他说明',
    asset_proof_document                      text         null comment '资产证明文件url集合;'',''分割',
    cryptocurrency_funding_source             varchar(255) null comment '数币资金来源;枚举集合;‘，’分割',
    cryptocurrency_funding_source_other_notes varchar(255) null comment '数币资金来源其他说明',
    cryptocurrency_funding_source_document    text         null comment '数币资金来源证明文件url集合;'',''分割',
    create_time                               datetime     null comment '创建时间',
    update_time                               datetime     null comment '更新时间',
    KEY idx_case_no (case_no)
)
    comment 'kyc三级认证信息';
CREATE TABLE `up_app_user_address_proof` (
                                             `address_id` bigint NOT NULL COMMENT '地址证明主键id',
                                             `app_user_info_id` bigint DEFAULT NULL COMMENT 'app用户主键id',
                                             `app_user_login_id` bigint DEFAULT NULL COMMENT 'app用户登录主键id',
                                             `address_proof_photo_1` varchar(255) DEFAULT NULL COMMENT '地址证明图片1',
                                             `address_proof_photo_2` varchar(255) DEFAULT NULL COMMENT '地址证明图片2',
                                             `address_proof_photo_3` varchar(255) DEFAULT NULL COMMENT '地址证明图片3',
                                             `risk_case_no` varchar(64)  DEFAULT NULL COMMENT '风控返回案件号',
                                             `risk_status` varchar(2)  DEFAULT '0' COMMENT '0:风控审核中；1：风控审核通过；2：风控审核拒绝',
                                             `risk_reject_reason` varchar(255)  DEFAULT NULL COMMENT '风控拒绝原因',
                                             `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                             `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
                                             PRIMARY KEY (`address_id`),
                                             KEY `idx_app_user_login_id` (`app_user_login_id`)
)   COMMENT='用户地址证明';

CREATE TABLE `up_app_user_employment_info` (
                                               `id` bigint NOT NULL COMMENT '主键，唯一标识每一行数据',
                                               `app_user_info_id` bigint DEFAULT NULL COMMENT 'app用户主键id',
                                               `app_user_login_id` bigint DEFAULT NULL COMMENT 'app用户登录主键id',
                                               `employment_status` varchar(32)  DEFAULT NULL COMMENT '雇佣状态；枚举',
                                               `company_name` varchar(32)  DEFAULT NULL COMMENT '公司名称',
                                               `industry` varchar(32)  DEFAULT NULL COMMENT '行业;枚举',
                                               `position` varchar(32)  DEFAULT NULL COMMENT '职位;枚举',
                                               `years_of_experience` varchar(32)  DEFAULT NULL COMMENT '工作年限；枚举',
                                               `annual_income` varchar(32)  DEFAULT NULL COMMENT '年收入;枚举',
                                               `asset_proof` varchar(512)  DEFAULT NULL COMMENT '资产证明；枚举集合;'',''号分割',
                                               `asset_proof_other_notes` varchar(512)  DEFAULT NULL COMMENT '资产证明其他说明',
                                               `asset_proof_document` text COMMENT '资产证明文件url集合;'',''分割',
                                               `cryptocurrency_funding_source` varchar(255)  DEFAULT NULL COMMENT '数币资金来源;枚举集合;‘，’分割',
                                               `cryptocurrency_funding_source_other_notes` varchar(255)  DEFAULT NULL COMMENT '数币资金来源其他说明',
                                               `cryptocurrency_funding_source_document` text COMMENT '数币资金来源证明文件url集合;'',''分割',
                                               `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                               `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
                                               PRIMARY KEY (`id`)
)   COMMENT='用户职业/资产证明信息表';

CREATE TABLE `up_app_user_info` (
                                    `app_user_info_id` bigint NOT NULL COMMENT 'app用户主键id',
                                    `app_user_login_id` bigint NOT NULL COMMENT 'app用户登录主键id',
                                    `user_level` varchar(64)  DEFAULT NULL COMMENT '用户等级',
                                    `organization_no` varchar(32)  DEFAULT NULL COMMENT '来源；UCard:ECT2025052799991;',
                                    `agent_id` bigint DEFAULT NULL COMMENT '代理商主键id',
                                    `referrer_uuid` bigint DEFAULT NULL COMMENT '推荐人',
                                    `referral_code` varchar(64)  NOT NULL COMMENT '推荐码',
                                    `realname_flag` int DEFAULT '0' COMMENT '是否实名;0:未实名;1:已实名',
                                    `last_name` varchar(64) DEFAULT NULL COMMENT '姓',
                                    `middle_name` varchar(64) DEFAULT NULL COMMENT '中间名',
                                    `first_name` varchar(64) DEFAULT NULL COMMENT '名',
                                    `id_type` varchar(16) DEFAULT NULL COMMENT '证件类型',
                                    `id_no` varchar(255)  DEFAULT NULL COMMENT '加密,证件号',
                                    `masked_id_no` varchar(64) DEFAULT NULL COMMENT '脱敏的证件号',
                                    `birth_date` varchar(10)  DEFAULT NULL COMMENT '出生日期;yyyy/mm/dd',
                                    `gender` int DEFAULT NULL COMMENT '性别;1:男;2:女',
                                    `id_issue_date` varchar(10)  DEFAULT NULL COMMENT '证件签发日',
                                    `id_expiry_date` varchar(10)  DEFAULT NULL COMMENT '证件有效期 ',
                                    `nationality` varchar(32) DEFAULT NULL COMMENT '国籍',
                                    `country_code` varchar(3) DEFAULT NULL COMMENT '国家代码,3位字母',
                                    `country_no` varchar(3) DEFAULT NULL COMMENT '国家地区代码,3位数字',
                                    `id_card_front_image` varchar(256) DEFAULT NULL COMMENT '证件正面地址',
                                    `face_photo_image` varchar(256) DEFAULT NULL COMMENT '人脸图片地址',
                                    `email` varchar(255) DEFAULT NULL COMMENT '邮箱',
                                    `phone_area` varchar(5) DEFAULT NULL COMMENT '手机区号',
                                    `mobile_no` varchar(32) DEFAULT NULL COMMENT '手机号码',
                                    `residence_country_code` varchar(3) DEFAULT NULL COMMENT '居住地国家/地区;3位字母',
                                    `residence_state_province` varchar(255) DEFAULT NULL COMMENT '居住地州/省',
                                    `residence_city` varchar(255) DEFAULT NULL COMMENT '居住地城市',
                                    `residence_address_detail` varchar(512) DEFAULT NULL COMMENT '居住地详细地址',
                                    `postal_code` varchar(16) DEFAULT NULL COMMENT '邮编',
                                    `avatar_url` varchar(256) DEFAULT NULL COMMENT '用户头像信息',
                                    `risk_case_no` varchar(64)  DEFAULT NULL COMMENT '风控返回案件号',
                                    `risk_status` varchar(2)  DEFAULT '0' COMMENT '0:风控审核中；1：风控审核通过；2：风控审核拒绝',
                                    `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                    `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
                                    PRIMARY KEY (`app_user_info_id`),
                                    UNIQUE KEY `uniq_app_user_login_id` (`app_user_login_id`) USING BTREE COMMENT '登录用户id唯一',
                                    UNIQUE KEY `uniq_referral_code` (`referral_code`) USING BTREE COMMENT '推荐码唯一'
)   COMMENT='app_用户信息';


CREATE TABLE `up_app_user_login` (
                                     `app_user_login_id` bigint NOT NULL COMMENT 'app用户主键',
                                     `mobile_no` varchar(32)  DEFAULT NULL COMMENT '手机号',
                                     `phone_area` varchar(5) DEFAULT NULL COMMENT '手机区号',
                                     `user_password` varchar(255) DEFAULT NULL COMMENT '用户密码',
                                     `salt` varchar(128) DEFAULT NULL COMMENT '用户加密盐',
                                     `email` varchar(255) DEFAULT NULL COMMENT '用户邮箱',
                                     `status` int DEFAULT '1' COMMENT '状态;1:有效;2:禁用;3:注销',
                                     `lock_status` int DEFAULT '0' COMMENT '锁定状态;0:未锁定;1:锁定',
                                     `organization_no` varchar(32)  DEFAULT NULL COMMENT '机构号；UCard:01;',
                                     `agent_id` bigint DEFAULT NULL COMMENT '代理商主键id',
                                     `country_alpha3_code` varchar(3) DEFAULT NULL COMMENT '手机注册地国家码',
                                     `last_login_date` datetime DEFAULT NULL COMMENT '上次登录时间',
                                     `last_login_ip` varchar(32) DEFAULT NULL COMMENT '上次登录ip',
                                     `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
                                     PRIMARY KEY (`app_user_login_id`)
)  COMMENT='app 用户登录信息';

CREATE TABLE `up_card_product_info` (
                                        `card_product_id` bigint NOT NULL COMMENT '卡产品主键id',
                                        `organization_no` varchar(16)  DEFAULT NULL COMMENT '机构号',
                                        `agent_id` bigint DEFAULT NULL COMMENT '代理商主键id',
                                        `card_product_code` varchar(32) DEFAULT NULL COMMENT '卡产品号',
                                        `language` varchar(6) DEFAULT NULL COMMENT '语言;en；zh',
                                        `card_policy_url` varchar(255) DEFAULT NULL COMMENT '卡策略url',
                                        `card_name` varchar(64)  DEFAULT NULL COMMENT '卡名称',
                                        `card_scheme` varchar(8) DEFAULT NULL COMMENT '卡组',
                                        `card_currency` varchar(3) DEFAULT NULL COMMENT '卡币种；3位字母',
                                        `card_face_image` varchar(255) DEFAULT NULL COMMENT '卡面图片地址',
                                        `card_back_image` varchar(255) DEFAULT NULL COMMENT '卡背面图片地址',
                                        `card_font_color` varchar(32)  DEFAULT '#FFFFFF' COMMENT '卡字体颜色',
                                        `description` text  COMMENT '卡描述',
                                        `fee` varchar(1024)  DEFAULT NULL COMMENT '卡费用相关数据;保存的是json',
                                        `card_summary` varchar(1024)  DEFAULT NULL COMMENT '卡片简介',
                                        `status` int DEFAULT '1' COMMENT '卡产品状态;0:Invalid;1:Valid;',
                                        `create_user` varchar(100)  DEFAULT NULL COMMENT '创建用户',
                                        `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_user` varchar(100)  DEFAULT NULL COMMENT '更新用户',
                                        `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
                                        PRIMARY KEY (`card_product_id`)
)  COMMENT='卡产品表';


CREATE TABLE `up_card_product_info_review` (
                                               `card_product_review_id` bigint NOT NULL COMMENT '卡产品审核表主键id',
                                               `review_type` varchar(8)  DEFAULT NULL COMMENT '审核类型;ADD;MODIFY',
                                               `review_status` varchar(8)  DEFAULT NULL COMMENT '审核状态;PENDING:等待审核 PASS:通过 REJECT:拒绝',
                                               `review_reason` varchar(255)  DEFAULT NULL COMMENT '审核原因',
                                               `card_product_id` bigint DEFAULT NULL COMMENT '卡产品主键id',
                                               `organization_no` varchar(16)  DEFAULT NULL COMMENT '机构号',
                                               `agent_id` bigint DEFAULT NULL COMMENT '代理商主键id',
                                               `card_product_code` varchar(32)  DEFAULT NULL COMMENT '卡产品号',
                                               `card_policy_url` varchar(255)  DEFAULT NULL COMMENT '卡策略url',
                                               `language` varchar(8)  DEFAULT NULL COMMENT '语言：ZH EN',
                                               `card_name` varchar(64)  DEFAULT NULL COMMENT '卡名称',
                                               `card_scheme` varchar(8)  DEFAULT NULL COMMENT '卡组',
                                               `card_currency` varchar(3)  DEFAULT NULL COMMENT '卡币种；3位字母',
                                               `card_face_image` varchar(255)  DEFAULT NULL COMMENT '卡面图片地址',
                                               `card_back_image` varchar(255)  DEFAULT NULL COMMENT '卡背面图片地址',
                                               `card_font_color` varchar(32)  DEFAULT '#FFFFFF' COMMENT '卡字体颜色',
                                               `description` text  COMMENT '卡描述',
                                               `fee` varchar(1024)  DEFAULT NULL COMMENT '卡费用相关数据；保存的是json',
                                               `card_summary` varchar(1024)  DEFAULT NULL COMMENT '卡片简介',
                                               `status` int DEFAULT '1' COMMENT '卡产品状态;0:Invalid;1:Valid;',
                                               `create_user` varchar(100)  DEFAULT NULL COMMENT '创建用户',
                                               `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                               `review_user` varchar(100)  DEFAULT NULL COMMENT '审核人',
                                               `review_datetime` datetime DEFAULT NULL COMMENT '更新时间',
                                               PRIMARY KEY (`card_product_review_id`) USING BTREE
)  COMMENT='卡产品审核表';


CREATE TABLE `up_country_region` (
                                     `country_region_id` bigint NOT NULL COMMENT '主键id',
                                     `country_name` varchar(128) DEFAULT NULL COMMENT '国家/地区名称',
                                     `country_code_2_alpha` varchar(2) DEFAULT NULL COMMENT '国家2位字母代码',
                                     `country_code_3_alpha` varchar(3) DEFAULT NULL COMMENT '国家3位字母代码',
                                     `country_code_3_numeric` varchar(3) DEFAULT NULL COMMENT '国家3位数字代码',
                                     `country_name_zh` varchar(128)  DEFAULT NULL COMMENT '国家/地区名称_中文',
                                     `registration_flag` int DEFAULT NULL COMMENT '注册标志，0为No, 1为Yes',
                                     `login_flag` int DEFAULT NULL COMMENT '登录标志，0为No, 1为Yes',
                                     `is_valid` tinyint DEFAULT NULL COMMENT '是否有效(1:有效,0:无效)',
                                     `create_user` varchar(100) DEFAULT NULL COMMENT '创建用户',
                                     `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_user` varchar(100) DEFAULT NULL COMMENT '更新用户',
                                     `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
                                     PRIMARY KEY (`country_region_id`) USING BTREE
)  COMMENT='国家地区信息';

CREATE TABLE `up_country_region_review` (
                                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '国家/地区审核主键id',
                                            `review_type` varchar(8) DEFAULT NULL COMMENT '审核类型;ADD;MODIFY',
                                            `review_status` varchar(8) DEFAULT NULL COMMENT '审核状态;PENDING:等待审核 PASS:通过 REJECT:拒绝',
                                            `reject_reason` varchar(255) DEFAULT NULL COMMENT '拒绝原因',
                                            `country_region_id` bigint DEFAULT NULL COMMENT '国家/地区主键id;修改的时候必填',
                                            `country_name` varchar(128)  DEFAULT NULL COMMENT '国家/地区名称_英文',
                                            `country_code_2_alpha` varchar(2) DEFAULT NULL COMMENT '国家2位字母代码',
                                            `country_code_3_alpha` varchar(3) DEFAULT NULL COMMENT '国家3位字母代码',
                                            `country_code_3_numeric` varchar(3) DEFAULT NULL COMMENT '国家3位数字代码',
                                            `country_name_zh` varchar(128)  DEFAULT NULL COMMENT '国家/地区名称_中文',
                                            `registration_flag` int DEFAULT '1' COMMENT '注册标志，0为No, 1为Yes',
                                            `login_flag` int DEFAULT '1' COMMENT '登录标志，0为No, 1为Yes',
                                            `is_valid` tinyint DEFAULT NULL COMMENT '是否有效(1:有效,0:无效)',
                                            `create_user` varchar(100) DEFAULT NULL COMMENT '创建用户',
                                            `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                            `review_user` varchar(100) DEFAULT NULL COMMENT '审核用户',
                                            `review_datetime` datetime DEFAULT NULL COMMENT '审核时间',
                                            PRIMARY KEY (`id`) USING BTREE
)  COMMENT='国家地区审核信息';


CREATE TABLE `up_currency_info` (
                                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '币种信息主键id',
                                    `currency_type` varchar(16) DEFAULT NULL COMMENT '币种类型;法币:Fiat;Crypto:加密货币',
                                    `currency_name` varchar(255) DEFAULT NULL COMMENT '币种名称',
                                    `currency_alpha_code` varchar(16) DEFAULT NULL COMMENT '币种字母码值',
                                    `currency_numeric_code` varchar(3) DEFAULT NULL COMMENT '币种数字码值',
                                    `exponent` int DEFAULT NULL COMMENT '币种精度',
                                    `status` int DEFAULT '1' COMMENT '币种状态;0:Invalid;1:Valid;',
                                    `create_user` varchar(100)  DEFAULT NULL COMMENT '创建用户',
                                    `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                    `update_user` varchar(100)  DEFAULT NULL COMMENT '更新用户',
                                    `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
                                    PRIMARY KEY (`id`)
)  COMMENT='币种信息';

CREATE TABLE `up_currency_review_info` (
                                           `id` bigint NOT NULL AUTO_INCREMENT COMMENT '币种信息审核主键id',
                                           `review_type` varchar(8) DEFAULT NULL COMMENT '审核类型;ADD;MODIFY',
                                           `review_status` varchar(8) DEFAULT NULL COMMENT '审核状态;PENDING:等待审核 PASS:通过 REJECT:拒绝',
                                           `reject_reason` varchar(255) DEFAULT NULL COMMENT '拒绝原因',
                                           `currency_id` bigint DEFAULT NULL COMMENT '币种信息主键id;修改的时候必填',
                                           `currency_type` varchar(16) DEFAULT NULL COMMENT '币种类型;法币:Fiat;Crypto:加密货币',
                                           `currency_name` varchar(255) DEFAULT NULL COMMENT '币种名称',
                                           `currency_alpha_code` varchar(16) DEFAULT NULL COMMENT '币种字母码值',
                                           `currency_numeric_code` varchar(3) DEFAULT NULL COMMENT '币种数字码值',
                                           `exponent` int DEFAULT NULL COMMENT '币种精度',
                                           `status` int DEFAULT '1' COMMENT '币种状态;0:Invalid;1:Valid;',
                                           `create_user` varchar(100)  DEFAULT NULL COMMENT '创建用户',
                                           `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                           `review_user` varchar(100)  DEFAULT NULL COMMENT '审核用户',
                                           `review_datetime` datetime DEFAULT NULL COMMENT '审核时间',
                                           PRIMARY KEY (`id`)
)  COMMENT='币种审核信息';

CREATE TABLE `up_data_dict` (
                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键id',
                                `dict_type` varchar(32)  DEFAULT NULL COMMENT '数据类型代码',
                                `dict_type_second` varchar(32)  DEFAULT NULL COMMENT '子类型',
                                `dict_value` varchar(32)  DEFAULT NULL COMMENT '数据值',
                                `en_desc` varchar(255)  DEFAULT NULL COMMENT '英文描述',
                                `cn_desc` varchar(255)  DEFAULT NULL COMMENT '中文简体描述',
                                `valid_flag` int DEFAULT NULL COMMENT '1有效 0无效',
                                `dict_index` int DEFAULT NULL COMMENT '排序字段',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `create_user_id` bigint DEFAULT NULL COMMENT '创建用户id',
                                `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                `update_user_id` bigint DEFAULT NULL COMMENT '更新用户id',
                                PRIMARY KEY (`id`) USING BTREE
)  COMMENT='数据字典表';


CREATE TABLE `up_file_info` (
                                `file_id` bigint NOT NULL COMMENT '文件主键id',
                                `associated_id` varchar(32) DEFAULT NULL COMMENT '关联id',
                                `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
                                `org_file_name` varchar(255) DEFAULT NULL COMMENT '原文件名称',
                                `bucket_url` varchar(255) DEFAULT NULL COMMENT '文件存储url',
                                `file_format` varchar(32) DEFAULT NULL COMMENT '文件格式',
                                `operation_type` varchar(32) DEFAULT NULL COMMENT '操作类型;证件号正面；证件号反面；人脸识别',
                                `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
                                PRIMARY KEY (`file_id`) USING BTREE
) comment '文件信息表';


CREATE TABLE `up_phone_area_info` (
                                      `id` int NOT NULL AUTO_INCREMENT COMMENT '手机区号主键id',
                                      `country_name` varchar(100)  NOT NULL COMMENT '国家/地区名称',
                                      `country_name_zh` varchar(100)  NOT NULL COMMENT '国家/地区名称中文',
                                      `alpha2_code` varchar(2)  NOT NULL COMMENT '2-Alpha Code',
                                      `alpha3_code` varchar(3)  NOT NULL COMMENT '3-Alpha Code',
                                      `phone_area` varchar(10)  DEFAULT NULL COMMENT 'Phone Area',
                                      `phone_rule_regex` varchar(20)  DEFAULT NULL COMMENT '手机号规则正则表达式',
                                      `rule_description` varchar(50)  DEFAULT NULL COMMENT '规则描述',
                                      `is_valid` tinyint(1) DEFAULT '1' COMMENT '是否有效(1:有效,0:无效)',
                                      `create_time` datetime DEFAULT NULL  COMMENT '创建时间',
                                      `update_time` datetime DEFAULT NULL  COMMENT '更新时间',
                                      PRIMARY KEY (`id`) USING BTREE,
                                      UNIQUE KEY `uniq_alpha2` (`alpha2_code`) USING BTREE,
                                      UNIQUE KEY `uniq_alpha3` (`alpha3_code`) USING BTREE,
                                      KEY `idx_country_name` (`country_name`) USING BTREE
) COMMENT='手机区号信息表';


CREATE TABLE `up_user_card_info` (
                                     `user_card_id` bigint NOT NULL COMMENT '用户卡关联主键id',
                                     `card_name` varchar(255)  DEFAULT NULL COMMENT '用户卡产品名称',
                                     `card_product_code` varchar(32) DEFAULT NULL COMMENT '卡产品号',
                                     `app_user_login_id` bigint DEFAULT NULL COMMENT '用户登录主键id',
                                     `organization_no` varchar(32) DEFAULT NULL COMMENT '机构号',
                                     `card_id` varchar(64)  DEFAULT NULL COMMENT '卡主键id',
                                     `card_number` varchar(255) DEFAULT NULL COMMENT '加密卡号',
                                     `masked_card_number` varchar(32) DEFAULT NULL COMMENT '脱敏卡号',
                                     `case_no` varchar(32) DEFAULT NULL COMMENT '案件号，可重复；风控申请，卡片申请共用这一个',
                                     `card_status` varchar(2) DEFAULT NULL COMMENT '0:申请中;1:有效；2：冻结；3：注销;4：风控拒绝；5：开卡拒绝;6:待激活',
                                     `active_status` varchar(2) DEFAULT NULL COMMENT '0:未激活；1：已激活',
                                     `active_time` datetime DEFAULT NULL COMMENT '激活时间',
                                     `auto_activated` varchar(2) DEFAULT NULL COMMENT '1:自动激活;2:手动激活',
                                     `risk_status` varchar(2) DEFAULT '0' COMMENT '0:风控审核中；1：风控审核通过；2：风控审核拒绝',
                                     `risk_reject_reason` varchar(255) DEFAULT NULL COMMENT '风控拒绝原因',
                                     `risk_case_no` varchar(64) DEFAULT NULL COMMENT '风控案件号',
                                     `risk_start_time` datetime DEFAULT NULL COMMENT '风控申请时间',
                                     `risk_end_time` datetime DEFAULT NULL COMMENT '风控完成时间',
                                     `lock_status` int DEFAULT '0' COMMENT '锁定状态;0:未锁定;1:锁定',
                                     `lock_reason` varchar(255) DEFAULT NULL COMMENT '锁定原因',
                                     `audit_completion_time` datetime DEFAULT NULL COMMENT '申请完成时间',
                                     `open_amount` varchar(32) DEFAULT NULL COMMENT '开卡金额',
                                     `open_currency` varchar(3) DEFAULT NULL COMMENT '开卡币种;3位大写字母',
                                     `card_limit` varchar(8) DEFAULT NULL COMMENT '交易次数限制',
                                     `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
                                     PRIMARY KEY (`user_card_id`),
                                     KEY `idx_app_user_login_id` (`app_user_login_id`),
                                     KEY `idx_card_product_code` (`card_product_code`),
                                     KEY `idx_card_status` (`card_status`)
)   COMMENT='用户-卡关联表';


CREATE TABLE `up_user_card_info_review` (
                                            `user_card_review_id` bigint NOT NULL COMMENT '卡操作审核主键id',
                                            `user_card_id` bigint DEFAULT NULL COMMENT '用户卡关联主键id',
                                            `review_type` varchar(8)  DEFAULT NULL COMMENT '审核类型;LOCK;UNLOCK',
                                            `review_status` varchar(8)  DEFAULT NULL COMMENT '审核状态;PENDING:等待审核 PASS:通过 REJECT:拒绝',
                                            `review_reason` varchar(255)  DEFAULT NULL COMMENT '审核原因',
                                            `mobile_no` varchar(16)  DEFAULT NULL COMMENT '手机号',
                                            `card_name` varchar(255)  DEFAULT NULL COMMENT '用户卡产品名称',
                                            `card_product_code` varchar(32)  DEFAULT NULL COMMENT '卡产品号',
                                            `app_user_login_id` bigint DEFAULT NULL COMMENT '用户登录主键id',
                                            `agent_id` bigint DEFAULT NULL COMMENT '代理商主键id',
                                            `organization_no` varchar(32)  DEFAULT NULL COMMENT '机构号',
                                            `card_id` varchar(64)  DEFAULT NULL COMMENT '卡主键id',
                                            `card_number` varchar(255)  DEFAULT NULL COMMENT '加密卡号',
                                            `masked_card_number` varchar(32)  DEFAULT NULL COMMENT '脱敏卡号',
                                            `case_no` varchar(32)  DEFAULT NULL COMMENT '案件号，可重复；风控申请，卡片申请共用这一个',
                                            `card_status` varchar(2)  DEFAULT NULL COMMENT '0:申请中;1:有效；2：冻结；3：注销;4：风控拒绝；5：开卡拒绝;6:待激活',
                                            `active_status` varchar(2)  DEFAULT NULL COMMENT '0:未激活；1：已激活',
                                            `active_time` datetime DEFAULT NULL COMMENT '激活时间',
                                            `auto_activated` varchar(2)  DEFAULT NULL COMMENT '1:自动激活;2:手动激活',
                                            `risk_status` varchar(2)  DEFAULT '0' COMMENT '0:风控审核中；1：风控审核通过；2：风控审核拒绝',
                                            `risk_reject_reason` varchar(255)  DEFAULT NULL COMMENT '风控拒绝原因',
                                            `risk_case_no` varchar(64)  DEFAULT NULL COMMENT '风控案件号',
                                            `risk_start_time` datetime DEFAULT NULL COMMENT '风控申请时间',
                                            `risk_end_time` datetime DEFAULT NULL COMMENT '风控完成时间',
                                            `lock_status` int DEFAULT '0' COMMENT '锁定状态;0:未锁定;1:锁定',
                                            `lock_reason` varchar(255)  DEFAULT NULL COMMENT '锁定原因',
                                            `audit_completion_time` datetime DEFAULT NULL COMMENT '申请完成时间',
                                            `open_amount` varchar(32)  DEFAULT NULL COMMENT '开卡金额',
                                            `open_currency` varchar(3)  DEFAULT NULL COMMENT '开卡币种;3位大写字母',
                                            `card_limit` varchar(8)  DEFAULT NULL COMMENT '交易次数限制',
                                            `create_user` varchar(100)  DEFAULT NULL COMMENT '创建用户',
                                            `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                            `review_user` varchar(100)  DEFAULT NULL COMMENT '审核人',
                                            `review_datetime` datetime DEFAULT NULL COMMENT '审核时间',
                                            PRIMARY KEY (`user_card_review_id`) USING BTREE
)  COMMENT='用户卡审核表';


CREATE TABLE `up_user_card_record` (
                                       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                       `app_user_login_id` bigint DEFAULT NULL COMMENT '登录用户id',
                                       `user_card_id` bigint DEFAULT NULL COMMENT '用户卡主键id',
                                       `operate_type` varchar(255) DEFAULT NULL COMMENT '操作类型',
                                       `request_param` text COMMENT '请求参数',
                                       `response_param` text COMMENT '响应参数',
                                       `create_datetime` datetime DEFAULT NULL COMMENT '请求时间',
                                       PRIMARY KEY (`id`)
) comment '用户卡操作记录表';


CREATE TABLE `up_user_wallet` (
                                  `user_wallet_id` bigint NOT NULL COMMENT '钱包主键id',
                                  `app_user_login_id` bigint DEFAULT NULL COMMENT '登录用户id',
                                  `request_id` varchar(64) DEFAULT NULL COMMENT '请求号',
                                  `wallet_network` varchar(255) DEFAULT NULL COMMENT '钱包来源',
                                  `chain_network` varchar(32)  DEFAULT NULL COMMENT '充值的链',
                                  `wallet_address` varchar(255) DEFAULT NULL COMMENT '钱包地址',
                                  `wallet_status` int DEFAULT '1' COMMENT '钱包状态;0:无效;1:有效',
                                  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`user_wallet_id`)
)   COMMENT='用户钱包信息';


CREATE TABLE `up_user_wallet_record` (
                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '钱包请求记录id',
                                         `app_user_login_id` bigint DEFAULT NULL COMMENT '登录用户id',
                                         `request_id` varchar(64)  DEFAULT NULL COMMENT '请求号',
                                         `request_param` text COMMENT '请求入参',
                                         `response_status` varchar(32) DEFAULT NULL COMMENT '响应状态',
                                         `wallet_address` varchar(255) DEFAULT NULL COMMENT '钱包地址',
                                         `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                         `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
                                         PRIMARY KEY (`id`)
) COMMENT='钱包申请记录';


CREATE TABLE `up_wallet_pollution_record` (
                                              `id` bigint NOT NULL AUTO_INCREMENT COMMENT '钱包污染通知记录主键id',
                                              `app_user_login_id` bigint DEFAULT NULL COMMENT '登录主键id',
                                              `wallet_address` varchar(255) DEFAULT NULL COMMENT '钱包地址',
                                              `chain_network` varchar(32) DEFAULT NULL COMMENT '充值的链网络',
                                              `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                              PRIMARY KEY (`id`) USING BTREE
) COMMENT='钱包污染记录';


-- 2025年7月22 新加 --
CREATE TABLE `up_app_register_white_list` (
                                              `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                              `email` varchar(255) DEFAULT NULL COMMENT '白名单邮箱',
                                              `mobile_no` varchar(255) DEFAULT NULL COMMENT '手机号;需要加区号',
                                              `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                              `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
                                              PRIMARY KEY (`id`)
) COMMENT='App 注册白名单';



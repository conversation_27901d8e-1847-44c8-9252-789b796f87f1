server:
  port: 8080
  undertow:
    # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认值为: Runtime.getRuntime().availableProcessors()
    io-threads: 4
    # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,默认值为: io-threads * 8
    worker-threads: 32
    # 每块buffer的空间,越小空间被利用越充分
    buffer-size: 1024
    # 是否分配的直接内存
    direct-buffers: true
    # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认值为: Runtime.getRuntime().availableProcessors()
    threads:
      io: 4
      worker: 32
  forward-headers-strategy: framework
sa-token:
  timeout: 1800 # 30分钟
spring:
  cloud:
    nacos:
      discovery:
        server-addr: nacos-0.nacos-headless.payx.svc.cluster.local:8848,nacos-1.nacos-headless.payx.svc.cluster.local:8848,nacos-2.nacos-headless.payx.svc.cluster.local:8848
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 100MB
  shardingsphere:
    enabled: true
    props:
      #是否输出sql
      sql-show: true
      default-data-source-name: ds0
    datasource:
      names: ds0
      ds0:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: **************************************************************************************************************************
        username: kun_uplusapp
        password: mQimOYdX8I3WMnyZ
        #特别提示:配置数据库加密 config这个不能忘掉
        druid:
          # 开启监控统计功能
          filters: stat,wall,log4j
          stat-view-servlet:
            enabled: true
            url-pattern: /druid/*
            # 登录监控页面的用户名和密码
            login-username: admin
            login-password: admin
          web-stat-filter:
            enabled: true
            url-pattern: /*
            exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
    rules:
      sharding:

  redis:
    host: master.pro-aws-hk-payx-redis-withpass.xlpfwo.ape1.cache.amazonaws.com
    port: 6379
    password: xT!x6vwmJ6SBE2u^
    ssl: true
    database: 9
    timeout: 10000
    # 连接池
    lettuce:
      pool:
        # 最大连接数
        max-active: 8
        # 最大阻塞等待时间(负数表示没限制)
        max-wait: -1
        # 最小空闲
        min-idle: 0
        # 最大空闲
        max-idle: 8

mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: com.kun.uplus.common.data.entity

rocketmq:
  name-server: mq.pro.kun:9876
  producer:
    group: UP_CUSTOMER_GROUP


kun:
  aws:
    s3:
      open: true
      needSk: true
      accessKey: ********************
      secretKey: ceUYko2onnWw0kYJbzPQeTs6RoUZWu3CsKd4FkzV
      region: ap-east-1
      bucket: pro-aws-static-s3
      endpoint: https://pro-aws-static-s3.s3.ap-east-1.amazonaws.com
      fileFolder: /kuplus-static-file

springdoc:
  api-docs:
    # 开启api-docs
    enabled: false

uplus:
  aes:
    secretKey: 6QzYQpVVtq2dUqFWJ3+2RLonVEtluzn2+PukzZxhX3k=

zoloz:
  client:
    id: 2188418819528562
  host:
    url: https://hk-production-api.zoloz.net
  product:
    docType: '00000001003'
    serviceLevel: REALID0001
  zoloz:
    pubkey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlOVGsWVDn+GCESafyhjIX35QIuy2NdeV+1U4imcgOHqTm1Liq108pk0et3apepwjR+YzYLMyRkRcG0mgcTaUyup6sDY0vS+37UhcqBfvE+1c1VnUS79cZ6pKc3JLFs/9MCeV/kkxipfRXW2sXWvgII9bDy9NBL9GwuXe90Q6XKfEnkIIfBe7LpP/fo93p135Bq+0SIY2Rqf9OaqlTBMVm2vlpLl2mgwn1D9676sRAWR7oF0nDSKYqyubwi+k7xbnK/0P1CCGiQ/E5Z/4Supl5gFj/Z/0inszCBvM+iLyubPfvyUWGjvaAsRhWQ7S4CuccSVSro3bwoPVjhycqzHc5QIDAQAB
    path: ekyc/zoloz_publick_key_prod.pem
  merchant:
    privkey:
      path: ekyc/merchant_private_key_prod.pem
  realidUrl: https://hk-production-cdn.zoloz.net/page/realid-fe/index.html
  completeCallbackUrl: https://qa.kun.global/uplus-member-h5/certify1
  interruptCallbackUrl: https://qa.kun.global/uplus-member-h5/home?certiry=no

kun-linkage:
  aes:
    iv: 1234567890000000
    secretKey: rw85EKdoILrNaoc/pu0QLTVpsN2uWG+AkNHBqrzFCLU=
  customer:
    url:
  notice:
    url:
kcy-check:
  countryCode: USA,RUS
  age: 18
  certificateExpirationMonth: 6

registration:
  email:
    whitelist: '@easypayfi.io,@kun.global,@yeewallex.com,@qq.com'



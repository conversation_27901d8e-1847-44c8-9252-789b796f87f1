package com.kun.linkage.customer.task;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.log.trace.LogContext;
import com.kun.common.util.mq.RocketMqService;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.db.entity.MpcWalletWebhookRecord;
import com.kun.linkage.common.db.mapper.MpcWalletWebhookRecordMapper;
import com.kun.linkage.wallet.gateway.facade.vo.req.WebhookMqReq;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * MPC钱包充值重试通知任务(从XXL-JOB手动触发)
 */
@Slf4j
@Component
public class MpcWalletEventRetryNotifyTask {
    @Resource
    private RocketMqService rocketMqService;
    @Resource
    private MpcWalletWebhookRecordMapper mpcWalletWebhookRecordMapper;

    @NewSpan
    @XxlJob("mpcWalletEventRetryNotifyTask")
    public boolean mpcWalletEventRetryNotifyTask() {
        XxlJobHelper.log("[MPC钱包充值重试通知任务]开始执行");
        try {
            LogContext.fromContext(LogContext.getContext());
            String jobParam = XxlJobHelper.getJobParam();
            if (StringUtils.isNotBlank(jobParam)){
                XxlJobHelper.log("[MPC钱包充值重试通知任务]任务参数为:{}", jobParam);
                String id = JSON.parseObject(jobParam).getString("id");
                String organizationNo = JSON.parseObject(jobParam).getString("organizationNo");
                MpcWalletWebhookRecord mpcWalletWebhookRecord = mpcWalletWebhookRecordMapper.selectOne(
                        Wrappers.<MpcWalletWebhookRecord>lambdaQuery()
                                .eq(MpcWalletWebhookRecord::getOrganizationNo, organizationNo)
                                .eq(MpcWalletWebhookRecord::getId, id));
                if (mpcWalletWebhookRecord == null) {
                    XxlJobHelper.log("[MPC钱包充值重试通知任务]任务记录不存在");
                } else {
                    WebhookMqReq webhookMqReq = new WebhookMqReq();
                    webhookMqReq.setAddressFrom(mpcWalletWebhookRecord.getAddressFrom());
                    webhookMqReq.setAddressTo(mpcWalletWebhookRecord.getAddressTo());
                    webhookMqReq.setAmount(mpcWalletWebhookRecord.getAmount().toPlainString());
                    webhookMqReq.setBlockNumber(mpcWalletWebhookRecord.getBlockNumber());
                    webhookMqReq.setCurrency(mpcWalletWebhookRecord.getCurrencyCode());
                    webhookMqReq.setGasCurrency(mpcWalletWebhookRecord.getGasCurrency());
                    webhookMqReq.setGasFee(mpcWalletWebhookRecord.getGasFee().toPlainString());
                    webhookMqReq.setNetwork(mpcWalletWebhookRecord.getChainNetwork());
                    webhookMqReq.setRequestId(mpcWalletWebhookRecord.getRequestNo());
                    webhookMqReq.setRiskScore(mpcWalletWebhookRecord.getRiskScore());
                    webhookMqReq.setRiskSuggest(mpcWalletWebhookRecord.getRiskSuggest());
                    webhookMqReq.setScene(mpcWalletWebhookRecord.getScene());
                    webhookMqReq.setStatus(mpcWalletWebhookRecord.getStatus());
                    webhookMqReq.setTenantId(mpcWalletWebhookRecord.getTenantId());
                    webhookMqReq.setTxHash(mpcWalletWebhookRecord.getTxHash());
                    SendResult sendResult = rocketMqService.syncSend(MqTopicConstant.MPC_WALLET_WEBHOOK_EVENT_TOPIC, webhookMqReq);
                    if (SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
                        XxlJobHelper.log("[MPC钱包充值重试通知任务]任务发送成功");
                    }
                }

            } else {
                XxlJobHelper.log("[MPC钱包充值重试通知任务]任务参数不能为空");
            }
            return XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            XxlJobHelper.log(e.getMessage());
            XxlJobHelper.log(e);
            return XxlJobHelper.handleFail(e.getMessage());
        } finally {
            XxlJobHelper.log("[MPC钱包充值重试通知任务]执行结束", LogContext.getContext().getTraceId());
            LogContext.destroy();
        }
    }
}

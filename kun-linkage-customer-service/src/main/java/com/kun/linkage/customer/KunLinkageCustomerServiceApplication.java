package com.kun.linkage.customer;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 启动程序
 */
@EnableFeignClients(basePackages = {"com.kun.linkage.account.facade", "com.kun.linkage.wallet.gateway.facade"})
@EnableAsync
@SpringBootApplication
public class KunLinkageCustomerServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(KunLinkageCustomerServiceApplication.class, args);
    }
}

package com.kun.linkage.customer.service;

import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.external.facade.api.uplus.UPlusOtpNoticeFacade;
import com.kun.linkage.common.external.facade.api.uplus.req.OtpNoticeBaseReq;
import com.kun.linkage.common.external.facade.api.uplus.res.OtpNoticeBaseRes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class OtpNoticeService {
    private static final Logger log = LoggerFactory.getLogger(OtpNoticeService.class);

    @Resource
    private UPlusOtpNoticeFacade uPlusOtpNoticeFacade;

    /**
     * opt 通知转发
     * @param otpNoticeBaseReq
     * @return
     */
    public Result<OtpNoticeBaseRes> optNotice(OtpNoticeBaseReq otpNoticeBaseReq) {
        //todo 目前先全部转到U+
       /* String kCardId = otpNoticeBaseReq.getkCardId();
        organizationCustomerCardInfoService.getOrganizationCustomerCardInfo(kCardId);*/

        return uPlusOtpNoticeFacade.optNotice(otpNoticeBaseReq);
    }
}

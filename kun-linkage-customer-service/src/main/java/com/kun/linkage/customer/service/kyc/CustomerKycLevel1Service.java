package com.kun.linkage.customer.service.kyc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kun.linkage.common.base.enums.KycLevelOperationTypeEnum;
import com.kun.linkage.common.base.enums.YesFlagEnum;
import com.kun.linkage.common.db.entity.CustomerKycLevel1Info;
import com.kun.linkage.common.db.entity.CustomerKycLevel1Record;
import com.kun.linkage.common.db.mapper.CustomerKycLevel1InfoMapper;
import com.kun.linkage.common.db.mapper.CustomerKycLevel1RecordMapper;
import com.kun.linkage.customer.facade.api.bean.req.OpenCardReq;
import com.kun.linkage.customer.facade.api.bean.req.kyc.CustomerKycLevel1Req;
import com.kun.linkage.customer.service.FileUploadService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class CustomerKycLevel1Service {

    @Resource
    private CustomerKycLevel1RecordMapper customerKycLevel1RecordMapper;
    @Resource
    private FileUploadService fileUploadService;
    @Resource
    private CustomerKycLevel1InfoMapper customerKycLevel1InfoMapper;

    /**
     * 保存kyc 一级信息
     * @param customerKycLevel1
     * @param caseNo
     * @param organizationNo
     * @param customerId
     * @param innerEncryptIdNo
     * @return
     */
    public Integer saveCustomerKycLevel1Record(CustomerKycLevel1Req customerKycLevel1, String caseNo,String organizationNo,
                                               String customerId,String innerEncryptIdNo,Date now) {

        //获取证件号正面图片地址
        String idCardFrontImageUploadId = customerKycLevel1.getIdCardFrontImageUploadId();
        String idCardFrontImage = null;
        if(StringUtils.isNotBlank(idCardFrontImageUploadId)){
            List<String> fileIds = Arrays.asList(idCardFrontImageUploadId);
            List<String> idCardFrontImageUrlList = fileUploadService.selectStoragePathByFileId(customerId, organizationNo, fileIds);
            if(null != idCardFrontImageUrlList && !idCardFrontImageUrlList.isEmpty()){
                idCardFrontImage = idCardFrontImageUrlList.get(0);
            }
        }

        //获取人脸图片地址
        String facePhotoImageUploadId = customerKycLevel1.getFacePhotoImageUploadId();
        String facePhotoImage = null;
        if(StringUtils.isNotBlank(facePhotoImageUploadId)){
            List<String> fileIds = Arrays.asList(facePhotoImageUploadId);
            List<String> facePhotoImageUploadList = fileUploadService.selectStoragePathByFileId(customerId, organizationNo, fileIds);
            if(null != facePhotoImageUploadList && !facePhotoImageUploadList.isEmpty()){
                facePhotoImage = facePhotoImageUploadList.get(0);
            }
        }

        CustomerKycLevel1Record customerKycLevel1Record = new CustomerKycLevel1Record();
        customerKycLevel1Record.setCustomerId(customerId);
        customerKycLevel1Record.setOrganizationNo(organizationNo);
        customerKycLevel1Record.setCaseNo(caseNo);
        customerKycLevel1Record.setLastName(customerKycLevel1.getLastName());
        customerKycLevel1Record.setMiddleName(customerKycLevel1.getMiddleName());
        customerKycLevel1Record.setFirstName(customerKycLevel1.getFirstName());
        customerKycLevel1Record.setIdType(customerKycLevel1.getIdType());
        customerKycLevel1Record.setIdNo(innerEncryptIdNo);
        customerKycLevel1Record.setMaskedIdNo(customerKycLevel1.getMaskedIdNo());
        customerKycLevel1Record.setBirthDate(customerKycLevel1.getBirthDate());
        customerKycLevel1Record.setGender(customerKycLevel1.getGender());
        customerKycLevel1Record.setIdIssueDate(customerKycLevel1.getIdIssueDate());
        customerKycLevel1Record.setIdExpiryDate(customerKycLevel1.getIdExpiryDate());
        customerKycLevel1Record.setNationality(customerKycLevel1.getNationality());
        customerKycLevel1Record.setCountryCode(customerKycLevel1.getCountryCode());
        customerKycLevel1Record.setCountryNo(customerKycLevel1.getCountryNo());
        customerKycLevel1Record.setIdCardFrontImageUploadId(customerKycLevel1.getIdCardFrontImageUploadId());
        customerKycLevel1Record.setIdCardFrontImage(idCardFrontImage);
        customerKycLevel1Record.setFacePhotoImageUploadId(customerKycLevel1.getFacePhotoImageUploadId());
        customerKycLevel1Record.setFacePhotoImage(facePhotoImage);
        customerKycLevel1Record.setEmail(customerKycLevel1.getEmail());
        customerKycLevel1Record.setPhoneArea(customerKycLevel1.getPhoneArea());
        customerKycLevel1Record.setMobileNo(customerKycLevel1.getMobileNo());
        customerKycLevel1Record.setResidenceCountryCode(customerKycLevel1.getResidenceCountryCode());
        customerKycLevel1Record.setResidenceStateProvince(customerKycLevel1.getResidenceStateProvince());
        customerKycLevel1Record.setResidenceCity(customerKycLevel1.getResidenceCity());
        customerKycLevel1Record.setResidenceAddressDetail(customerKycLevel1.getResidenceAddressDetail());
        customerKycLevel1Record.setPostalCode(customerKycLevel1.getPostalCode());
        customerKycLevel1Record.setCreateDatetime(now);
        customerKycLevel1Record.setUpdateDatetime(now);
        return customerKycLevel1RecordMapper.insert(customerKycLevel1Record);
    }

    /**
     * 新增或者更新用户一级信息
     * @param caseNo 案件号
     * @param organizationNo 机构号
     * @param customerId 客户号
     */
    public Boolean saveOrUpdateCustomerKycLevel1Info(String caseNo,String organizationNo,String customerId){

        CustomerKycLevel1Record customerKycLevel1Record = selectKycLevel1ByCase(caseNo);
        if(null == customerKycLevel1Record){
            log.info("新增或者更新KYC一级信息失败;根据案件号没有查询到一级审核信息;caseNo:{}",caseNo);
            return false;
        }

        CustomerKycLevel1Info customerKycLevel1Info = this.selectCustomerKycLevel1InfoByOrgNoAndCustomerId(organizationNo, customerId);
        if(null == customerKycLevel1Info){
            //新增
            customerKycLevel1Info = new CustomerKycLevel1Info();
            BeanUtils.copyProperties(customerKycLevel1Record, customerKycLevel1Info);
            customerKycLevel1Info.setSubmissionTime(customerKycLevel1Record.getCreateDatetime());
            customerKycLevel1Info.setOperationType(KycLevelOperationTypeEnum.PROACTIVE_REPORTING.getValue());
            customerKycLevel1Info.setIsComplete(YesFlagEnum.YES.getNumValue());
            return customerKycLevel1InfoMapper.insert(customerKycLevel1Info) > 0;
        }else {
            //更新数据
            CustomerKycLevel1Info updateEntity = new CustomerKycLevel1Info();
            BeanUtils.copyProperties(customerKycLevel1Record,updateEntity);
            updateEntity.setKycLevel1Id(customerKycLevel1Info.getKycLevel1Id());
            updateEntity.setSubmissionTime(customerKycLevel1Record.getCreateDatetime());
            customerKycLevel1Info.setOperationType(KycLevelOperationTypeEnum.PROACTIVE_REPORTING.getValue());
            customerKycLevel1Info.setIsComplete(YesFlagEnum.YES.getNumValue());
            updateEntity.setUpdateDatetime(new Date());
            return customerKycLevel1InfoMapper.updateById(updateEntity) > 0;
        }

    }

    public Boolean saveOrUpdateCustomerKycLevel1InfoByOpenCardReq(OpenCardReq openCardReq) {
        String organizationNo = openCardReq.getOrganizationNo();
        String customerId = openCardReq.getCustomerId();
        String requestNo = openCardReq.getRequestNo();

        CustomerKycLevel1Info customerKycLevel1Info = this.selectCustomerKycLevel1InfoByOrgNoAndCustomerId(organizationNo, customerId);
        if(null == customerKycLevel1Info){
            Date date = new Date();
            //新增数据
            customerKycLevel1Info = new CustomerKycLevel1Info();
            customerKycLevel1Info.setCaseNo(requestNo);
            customerKycLevel1Info.setCustomerId(customerId);
            customerKycLevel1Info.setOrganizationNo(organizationNo);
            customerKycLevel1Info.setLastName(openCardReq.getCardHolderLastName());
            customerKycLevel1Info.setFirstName(openCardReq.getCardHolderFirstName());
            customerKycLevel1Info.setEmail(openCardReq.getEmail());
            customerKycLevel1Info.setPhoneArea(openCardReq.getMobilePhoneArea());
            customerKycLevel1Info.setMobileNo(openCardReq.getMobilePhone());
            customerKycLevel1Info.setCountryNo(openCardReq.getCardHolderCountryNo());
            customerKycLevel1Info.setOperationType(KycLevelOperationTypeEnum.CARD_APPLICATION.getValue());
            customerKycLevel1Info.setIsComplete(YesFlagEnum.NO.getNumValue());
            customerKycLevel1Info.setSubmissionTime(date);
            customerKycLevel1Info.setCreateDatetime(date);
            customerKycLevel1Info.setUpdateDatetime(date);
            return customerKycLevel1InfoMapper.insert(customerKycLevel1Info) > 0;
        }else {
            CustomerKycLevel1Info updateEntity = new CustomerKycLevel1Info();
            updateEntity.setKycLevel1Id(customerKycLevel1Info.getKycLevel1Id());
            updateEntity.setEmail(openCardReq.getEmail());
            updateEntity.setOperationType(KycLevelOperationTypeEnum.CARD_APPLICATION_UPDATE.getValue());
            updateEntity.setCaseNo(requestNo);

            if(StringUtils.isNotBlank(openCardReq.getCardHolderLastName())){
                updateEntity.setLastName(openCardReq.getCardHolderLastName());
            }
            if(StringUtils.isNotBlank(openCardReq.getCardHolderFirstName())){
                updateEntity.setFirstName(openCardReq.getCardHolderFirstName());
            }
            if(StringUtils.isNotBlank(openCardReq.getMobilePhoneArea())){
                updateEntity.setPhoneArea(openCardReq.getMobilePhoneArea());
            }
            if(StringUtils.isNotBlank(openCardReq.getMobilePhone())){
                updateEntity.setMobileNo(openCardReq.getMobilePhone());
            }
            if(StringUtils.isNotBlank(openCardReq.getCardHolderCountryNo())){
                updateEntity.setCountryNo(openCardReq.getCardHolderCountryNo());
            }
            updateEntity.setUpdateDatetime(new Date());
           return customerKycLevel1InfoMapper.updateById(updateEntity) > 0;
        }

    }

        /**
         * 根据机构号和客户号
         * @param organizationNo 机构号
         * @param customerId 客户号
         * @return
         */
    public CustomerKycLevel1Info selectCustomerKycLevel1InfoByOrgNoAndCustomerId(String organizationNo,String customerId){
        LambdaQueryWrapper<CustomerKycLevel1Info> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerKycLevel1Info::getOrganizationNo, organizationNo)
                .eq(CustomerKycLevel1Info::getCustomerId, customerId);
        return customerKycLevel1InfoMapper.selectOne(queryWrapper);
    }

    /**
     * 根据案件号查询KYC1级信息
     * @param caseNo 案件号
     */
    public CustomerKycLevel1Record selectKycLevel1ByCase(String caseNo) {
        LambdaQueryWrapper<CustomerKycLevel1Record> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerKycLevel1Record::getCaseNo, caseNo);
        return customerKycLevel1RecordMapper.selectOne(queryWrapper);
    }
}

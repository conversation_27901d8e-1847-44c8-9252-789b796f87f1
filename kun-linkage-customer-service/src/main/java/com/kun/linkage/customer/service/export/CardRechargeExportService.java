package com.kun.linkage.customer.service.export;

import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.kun.common.util.aws.AwsS3Util;
import com.kun.common.util.aws.AwzS3Properties;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.db.entity.CardRechargeDetail;
import com.kun.linkage.common.db.entity.KLExportFileRecord;
import com.kun.linkage.common.db.mapper.CardRechargeDetailMapper;
import com.kun.linkage.customer.facade.api.bean.req.OrgPageQueryCardRechargeDetailReq;
import com.kun.linkage.customer.facade.constant.CardRechargeExportConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;

/**
 * 卡充值记录异步导出服务
 */
@Slf4j
@Service
public class CardRechargeExportService {

    @Resource
    private CardRechargeDetailMapper cardRechargeDetailMapper;

    @Resource
    private ExportFileRecordService exportFileRecordService;

    @Resource
    private AwsS3Util awsS3Util;

    @Resource
    private AwzS3Properties awzS3Properties;
    @Value("${kun.aws.s3.fileFolder:}")
    private String fileFolder;

    /**
     * 创建导出任务
     */
    public String createExportTask(OrgPageQueryCardRechargeDetailReq req) {
        // 生成文件名
        String fileName = generateFileName(req.getOrganizationNo());
        
        // 创建文件记录
        KLExportFileRecord fileRecord = exportFileRecordService.createFileRecord(
                req.getOrganizationNo(), 
                fileName, 
                CardRechargeExportConstant.FileType.CARD_RECHARGE_EXPORT
        );
        
        // 异步执行导出
        asyncExportData(fileRecord, req);
        
        return fileRecord.getFileRecordId();
    }

    /**
     * 异步导出卡充值记录数据
     */
    @Async("externalApiAsyncExecutor")
    public void asyncExportData(KLExportFileRecord fileRecord, OrgPageQueryCardRechargeDetailReq req) {
        String fileRecordId = fileRecord.getFileRecordId();
        try {
            log.info("开始异步导出卡充值记录数据，文件记录ID: {}", fileRecordId);

            // 1. 查询数据
            List<CardRechargeDetail> dataList = queryExportData(req);
            log.info("查询到 {} 条卡充值记录数据", dataList.size());

            // 2. 生成CSV文件
            File csvFile = generateCsvFile(dataList, fileRecord.getFileName());
            log.info("CSV文件生成完成，文件大小: {} bytes", csvFile.length());

            // 3. 上传到S3
            String s3Url = uploadToS3(csvFile, fileRecord.getFileName());
            log.info("文件上传S3成功，URL: {}", s3Url);

            // 4. 更新文件记录为成功状态
            exportFileRecordService.updateFileRecordSuccess(fileRecordId, s3Url, csvFile.length());
            log.info("卡充值记录导出任务完成，文件记录ID: {}", fileRecordId);

            // 5. 删除临时文件
            if (csvFile.exists()) {
                csvFile.delete();
                log.info("临时文件删除成功: {}", csvFile.getAbsolutePath());
            }

        } catch (Exception e) {
            log.error("卡充值记录导出任务失败，文件记录ID: {}", fileRecordId, e);
            exportFileRecordService.updateFileRecordFailed(fileRecordId, e.getMessage());
        }
    }

    /**
     * 查询导出数据
     */
    private List<CardRechargeDetail> queryExportData(OrgPageQueryCardRechargeDetailReq req) {
        // 复用现有的业务逻辑验证
        if (req.getEndDate().isBefore(LocalDate.of(2025, 5, 1))) {
            log.warn("[导出卡充值记录]结束时间不能早于2025-05-01,直接返回空集合");
            return Collections.emptyList();
        }
        
        LocalDate now = LocalDate.now();
        if (req.getEndDate().isAfter(now)) {
            log.warn("[导出卡充值记录]结束时间不能超过当前,重置结束时间为当前时间");
            req.setEndDate(now);
        }
        
        long days = ChronoUnit.DAYS.between(req.getStartDate(), req.getEndDate());
        if (days > 365) {
            log.error("[导出卡充值记录]查询时间跨度不能超过365天");
            throw new BusinessException(CommonTipConstant.QUERY_TIME_SPAN_CANNOT_EXCEED_365_DAYS);
        }
        // 构建查询条件
        LambdaQueryWrapper<CardRechargeDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CardRechargeDetail::getOrganizationNo, req.getOrganizationNo())
                .eq(CardRechargeDetail::getCustomerId, req.getCustomerId())
                .ge(CardRechargeDetail::getRechargeDatetime, req.getStartDate().atTime(0, 0, 0))
                .le(CardRechargeDetail::getRechargeDatetime, req.getEndDate().atTime(23, 59, 59))
                .orderByDesc(CardRechargeDetail::getRechargeDatetime);

        if (StringUtils.isNotBlank(req.getCardId())) {
            queryWrapper.eq(CardRechargeDetail::getCardId, req.getCardId());
        }

        // 添加金额范围查询条件
        if (req.getRechargeAmountFrom() != null) {
            queryWrapper.ge(CardRechargeDetail::getRechargeAmount, req.getRechargeAmountFrom());
        }
        if (req.getRechargeAmountTo() != null) {
            queryWrapper.le(CardRechargeDetail::getRechargeAmount, req.getRechargeAmountTo());
        }

        // 添加状态查询条件
        if (StringUtils.isNotBlank(req.getRechargeStatus())) {
            queryWrapper.eq(CardRechargeDetail::getRechargeStatus, req.getRechargeStatus());
        }

        return cardRechargeDetailMapper.selectList(queryWrapper);
    }

    /**
     * 生成CSV文件
     */
    private File generateCsvFile(List<CardRechargeDetail> dataList, String fileName) throws IOException {
        File tempFile = File.createTempFile("recharge_export_", ".csv");

        try (FileOutputStream fos = new FileOutputStream(tempFile);
             OutputStreamWriter osw = new OutputStreamWriter(fos, StandardCharsets.UTF_8);
             CSVPrinter csvPrinter = new CSVPrinter(osw, CSVFormat.DEFAULT.withHeader(CardRechargeExportConstant.CSV_HEADERS))) {

            // 写入UTF-8 BOM头，确保中文正确显示
            fos.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});
            
            for (CardRechargeDetail detail : dataList) {
                csvPrinter.printRecord(
                        formatDateTime(detail.getRechargeDatetime()),        // 充值日期时间
                        formatText(detail.getId()),                          // 充值单号
                        formatText(detail.getRequestNo()),                   // 充值请求流水号
                        formatText(detail.getOrganizationNo()),              // 商户号
                        formatText(detail.getCustomerId()),                  // UUID
                        formatText(detail.getCardId()),                      // Card ID
                        formatText(detail.getRechargeCurrencyCode()),        // 币种
                        formatAmount(detail.getRechargeAmount()),            // 金额
                        formatText(detail.getRechargeStatus()),              // 状态
                        formatText(detail.getFailMessage()),                 // 失败原因
                        formatText(detail.getDeductCurrencyCode()),          // 商户扣款币种
                        formatAmount(detail.getDeductTotalAmount())          // 商户扣款金额
                );
            }
        }
        
        return tempFile;
    }

    /**
     * 上传文件到S3
     */
    private String uploadToS3(File file, String fileName) {
        LocalDateTime now = LocalDateTime.now();
        String yearMonth = now.format(DateTimeFormatter.ofPattern("yyyyMM"));
        String s3Path = CardRechargeExportConstant.S3_FOLDER_PATH + "/" + yearMonth;

        return awsS3Util.uploadChunkedFile(
                fileName,
                file,
                awzS3Properties.getBucket(),
                fileFolder + "/" + s3Path,
                CannedAccessControlList.PublicReadWrite
        );
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String organizationNo) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return String.format(CardRechargeExportConstant.FILE_NAME_TEMPLATE, organizationNo, timestamp);
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 格式化文本，处理null值
     */
    private String formatText(String text) {
        return text == null ? "" : ("\t" + text);
    }

    /**
     * 格式化金额
     */
    private String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "";
        }
        return amount.toPlainString();
    }

    /**
     * 验证日期范围
     */
    public void validateDateRange(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }
        
        if (startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }
        
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        if (daysBetween > CardRechargeExportConstant.MAX_QUERY_DAYS) {
            throw new IllegalArgumentException(
                String.format("查询日期范围不能超过%d天，当前范围：%d天", 
                    CardRechargeExportConstant.MAX_QUERY_DAYS, daysBetween)
            );
        }
    }
}

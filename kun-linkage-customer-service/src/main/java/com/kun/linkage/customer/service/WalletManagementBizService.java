package com.kun.linkage.customer.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.mq.RocketMqService;
import com.kun.linkage.account.facade.api.AccountTransactionFacade;
import com.kun.linkage.account.facade.api.bean.req.AccountChangeBalanceReq;
import com.kun.linkage.account.facade.api.bean.res.AccountChangeBalanceRes;
import com.kun.linkage.account.facade.enums.AccountingActionEnum;
import com.kun.linkage.account.facade.enums.BusinessActionEnum;
import com.kun.linkage.account.facade.enums.BusinessSystemEnum;
import com.kun.linkage.account.facade.enums.BusinessTypeEnum;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.base.enums.DigitalCurrencyEnum;
import com.kun.linkage.common.base.enums.OperationStatusEnum;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.common.base.enums.YesFlagEnum;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageParam;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.*;
import com.kun.linkage.common.db.mapper.*;
import com.kun.linkage.common.external.facade.api.kcard.KCardKunAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXDirectionEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXRemarkEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunSideTypeEnum;
import com.kun.linkage.common.external.facade.api.kcard.req.KunAskPriceReq;
import com.kun.linkage.common.external.facade.api.kcard.req.KunDebitSubReq;
import com.kun.linkage.common.external.facade.api.kcard.res.KunAskPriceRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.KunDebitSubRsp;
import com.kun.linkage.customer.ext.mapper.WalletTransactionDetailExtMapper;
import com.kun.linkage.customer.facade.api.bean.req.CreateWalletReq;
import com.kun.linkage.customer.facade.api.bean.req.PageQueryWalletRechargeDetailReq;
import com.kun.linkage.customer.facade.api.bean.res.CreateWalletRes;
import com.kun.linkage.customer.facade.api.bean.res.PageQueryWalletRechargeDetailRes;
import com.kun.linkage.customer.facade.constants.CustomerTipConstant;
import com.kun.linkage.customer.facade.enums.*;
import com.kun.linkage.customer.facade.vo.mq.WalletRechargeRiskExceptionEventVO;
import com.kun.linkage.wallet.gateway.facade.api.MpcApiFacade;
import com.kun.linkage.wallet.gateway.facade.vo.KunApiHeaderVO;
import com.kun.linkage.wallet.gateway.facade.vo.req.ApplyWalletAddressReq;
import com.kun.linkage.wallet.gateway.facade.vo.req.ApplyWalletAddressVO;
import com.kun.linkage.wallet.gateway.facade.vo.rsp.ApplyWalletAddressRsp;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Collections;

/**
 * <p>
 * 机构客户钱包信息管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-08
 */
@Service
@RefreshScope
public class WalletManagementBizService {
    private static final Logger log = LoggerFactory.getLogger(WalletManagementBizService.class);
    @Resource
    private OrganizationCustomerWalletInfoMapper organizationCustomerWalletInfoMapper;
    @Resource
    private OrganizationCustomerWalletOperationRecordMapper organizationCustomerWalletOperationRecordMapper;
    @Resource
    private OrganizationCustomerCardInfoMapper organizationCustomerCardInfoMapper;
    @Resource
    private OrganizationCustomerWalletOperationRecordBizService organizationCustomerWalletOperationRecordBizService;
    @Resource
    private WalletManagementBizService walletManagementBizService;
    @Resource
    private OrganizationBasicInfoMapper organizationBasicInfoMapper;
    @Resource
    private KCardKunAccountFacade kCardKunAccountFacade;
    @Resource
    private CardholderFeeMapper cardholderFeeMapper;
    @Resource
    private CardholderFeeDetailMapper cardholderFeeDetailMapper;
    @Resource
    private AccountTransactionFacade accountTransactionFacade;
    @Resource
    private WalletTransactionDetailMapper walletTransactionDetailMapper;
    @Resource
    private WalletTransactionDetailExtMapper walletTransactionDetailExtMapper;
    @Resource
    private MpcApiFacade mpcApiFacade;
    @Resource
    private RocketMqService rocketMqService;
    @Value(value = "${kl.mpcWalletMock:false}")
    private boolean mpcWalletMock;
    @Value(value = "${kl.mpcOrganizationBookkeepSwitch:true}")
    private boolean mpcOrganizationBookkeepSwitch;

    /**
     * 创建钱包
     *
     * @param createWalletReq
     * @return
     */
    public Result<CreateWalletRes> createWallet(CreateWalletReq createWalletReq) {
        // 校验是否重复请求
        Long num = organizationCustomerWalletOperationRecordMapper.selectCount(Wrappers.<OrganizationCustomerWalletOperationRecord>lambdaQuery()
                .eq(OrganizationCustomerWalletOperationRecord::getOrganizationNo, createWalletReq.getOrganizationNo())
                .eq(OrganizationCustomerWalletOperationRecord::getOperationType, WalletOperationTypeEnum.CREATE.getValue())
                .eq(OrganizationCustomerWalletOperationRecord::getRequestNo, createWalletReq.getRequestNo()));
        if (num != null && num > 0) {
            log.error("[钱包管理-创建钱包]重复请求,机构号:{},请求流水号:{}", createWalletReq.getOrganizationNo(), createWalletReq.getRequestNo());
            return Result.fail(CommonTipConstant.DUPLICATE_REQUEST);
        }
        // 校验机构是否配置了MPC相关的信息
        OrganizationBasicInfo organizationBasicInfo = organizationBasicInfoMapper.selectOne(Wrappers.<OrganizationBasicInfo>lambdaQuery()
                .eq(OrganizationBasicInfo::getOrganizationNo, createWalletReq.getOrganizationNo())
                .eq(OrganizationBasicInfo::getStatus, ValidStatusEnum.VALID.getValue()));
        if (organizationBasicInfo == null) {
            log.error("[钱包管理-创建钱包]机构不存在,机构号:{}", createWalletReq.getOrganizationNo());
            return Result.fail(CustomerTipConstant.ORGANIZATION_NOT_FOUND);
        }
        if (StringUtils.isBlank(organizationBasicInfo.getMpcTenantId()) || StringUtils.isBlank(organizationBasicInfo.getMpcGroupCode())
                || StringUtils.isBlank(organizationBasicInfo.getMpcToken())) {
            log.error("[钱包管理-创建钱包]机构未配置MPC信息,请先配置,机构号:{}", createWalletReq.getOrganizationNo());
            return Result.fail(CustomerTipConstant.ORGANIZATION_MPC_CONFIG_NOT_FOUND);
        }
        // 校验客户是否存在有效的卡信息
        num = organizationCustomerCardInfoMapper.selectCount(Wrappers.<OrganizationCustomerCardInfo>lambdaQuery()
                .eq(OrganizationCustomerCardInfo::getOrganizationNo, createWalletReq.getOrganizationNo())
                .eq(OrganizationCustomerCardInfo::getCustomerId, createWalletReq.getCustomerId())
                .ne(OrganizationCustomerCardInfo::getCardStatus, CardStatusEnum.CANCEL.getStatus()));
        if (num == null || num <= 0) {
            log.error("[钱包管理-创建钱包]客户下不存在有效的卡信息,不能创建钱包,机构号:{},客户号:{}", createWalletReq.getOrganizationNo(), createWalletReq.getCustomerId());
            return Result.fail(CustomerTipConstant.CARD_INFO_NOT_FOUND);
        }
        OrganizationCustomerWalletInfo organizationCustomerWalletInfo = organizationCustomerWalletInfoMapper.selectOne(
                Wrappers.<OrganizationCustomerWalletInfo>lambdaQuery()
                        .eq(OrganizationCustomerWalletInfo::getOrganizationNo, createWalletReq.getOrganizationNo())
                        .eq(OrganizationCustomerWalletInfo::getCustomerId, createWalletReq.getCustomerId())
                        .eq(OrganizationCustomerWalletInfo::getStatus, ValidStatusEnum.VALID.getValue())
                        .eq(OrganizationCustomerWalletInfo::getWalletNetwork, createWalletReq.getWalletNetwork())
                        .eq(OrganizationCustomerWalletInfo::getChainNetwork, createWalletReq.getChainNetwork()));
        if (organizationCustomerWalletInfo != null && StringUtils.isNotBlank(organizationCustomerWalletInfo.getWalletAddress())) {
            log.error("[钱包管理-创建钱包]存在有效的钱包,请勿重复创建,机构号:{},客户号:{},钱包通道:{},链网络:{}",
                    createWalletReq.getOrganizationNo(), createWalletReq.getCustomerId(), createWalletReq.getWalletNetwork(), createWalletReq.getChainNetwork());
            // 此处将已存在的钱包地址返回回去
            CreateWalletRes createWalletRes = new CreateWalletRes();
            createWalletRes.setWalletAddress(organizationCustomerWalletInfo.getWalletAddress());
            return Result.fail(CustomerTipConstant.WALLET_ALREADY_EXIST, createWalletRes);
        } else {
            try {
                ApplyWalletAddressReq applyWalletAddressReq = this.assApplyWalletAddressReq(createWalletReq, organizationBasicInfo);
                log.info("[钱包管理-创建钱包]调用钱包提供方创建钱包开始,请求参数:{}", applyWalletAddressReq);
                Result<ApplyWalletAddressRsp> applyWalletAddressRspResult;
                if (mpcWalletMock) {
                    // mock开关
                    ApplyWalletAddressRsp applyWalletAddressRsp = new ApplyWalletAddressRsp();
                    applyWalletAddressRsp.setAddress("mock" + IdWorker.getId());
                    applyWalletAddressRspResult = Result.success(applyWalletAddressRsp);
                } else {
                    applyWalletAddressRspResult = mpcApiFacade.applyWalletAddress(applyWalletAddressReq);
                }
                log.info("[钱包管理-创建钱包]调用钱包提供方创建钱包结束,响应参数:{}", applyWalletAddressRspResult);
                if (Result.isSuccess(applyWalletAddressRspResult)
                        && applyWalletAddressRspResult.getData() != null
                        && StringUtils.isNotBlank(applyWalletAddressRspResult.getData().getAddress())) {
                    log.info("[钱包管理-创建钱包]调用钱包网管创建钱包成功");
                    walletManagementBizService.createWalletSuccessProcess(createWalletReq, applyWalletAddressRspResult.getData().getAddress());
                    CreateWalletRes createWalletRes = new CreateWalletRes();
                    createWalletRes.setWalletAddress(applyWalletAddressRspResult.getData().getAddress());
                    return Result.success(createWalletRes);
                } else {
                    log.error("[钱包管理-创建钱包]调用钱包提供方创建钱包失败");
                    // 保存操作记录表
                    organizationCustomerWalletOperationRecordBizService.insertCreateWalletOperationRecord(
                            createWalletReq, OperationStatusEnum.FAIL, "调用钱包提供方创建钱包返回失败", null);
                    return Result.fail(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } catch (Exception e) {
                log.error("[钱包管理-创建钱包]调用钱包提供方创建钱包失败,异常信息:", e);
                // 保存操作记录表
                organizationCustomerWalletOperationRecordBizService.insertCreateWalletOperationRecord(
                        createWalletReq, OperationStatusEnum.FAIL, "调用钱包提供方创建钱包异常,请查看具体日志", null);
                return Result.fail(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }
    }

    /**
     * mpc钱包充值风控通过处理
     *
     * @param mpcWalletWebhookRecord
     */
    public void mpcWalletRechargeRiskPassProcess(MpcWalletWebhookRecord mpcWalletWebhookRecord, OrganizationCustomerWalletInfo organizationCustomerWalletInfo,
                                                 OrganizationBasicInfo organizationBasicInfo, OrganizationCustomerAccountInfo organizationCustomerAccountInfo) {
        log.info("[MPC钱包Webhook通知事件]风控检验通过,开始进行充值");
        WalletTransactionDetail walletTransactionDetail = this.initWalletTransactionDetail(
                mpcWalletWebhookRecord, organizationCustomerWalletInfo, organizationCustomerAccountInfo);
        // 进行换汇
        this.processingExchangeRate(walletTransactionDetail, organizationBasicInfo);
        // 计算充值承兑费
        this.calculateRechargeAcceptanceFeeAmount(walletTransactionDetail);
        // 处理账户动账
        this.processingAccountTransaction(walletTransactionDetail, organizationBasicInfo, organizationCustomerAccountInfo, mpcWalletWebhookRecord);
        // 保存钱包交易流水
        int row = walletTransactionDetailMapper.insert(walletTransactionDetail);
        log.info("insert {} row WalletTransactionDetail", row);
        log.info("[MPC钱包Webhook通知事件]充值处理完成");
    }

    /**
     * mpc钱包充值风控异常处理
     *
     * @param mpcWalletWebhookRecord
     */
    @Transactional(rollbackFor = Exception.class)
    public void mpcWalletRechargeRiskExceptionProcess(MpcWalletWebhookRecord mpcWalletWebhookRecord, OrganizationCustomerWalletInfo organizationCustomerWalletInfo) {
        log.warn("[MPC钱包Webhook通知事件]风控检验异常,开始进行更新钱包状态及通知UU国际,机构号:{}, 租户id:{}, 请求流水号:{}, 风控建议:{}",
                mpcWalletWebhookRecord.getOrganizationNo(), mpcWalletWebhookRecord.getTenantId(),
                mpcWalletWebhookRecord.getRequestNo(), mpcWalletWebhookRecord.getRiskSuggest());
        organizationCustomerWalletInfo.setStatus(ValidStatusEnum.INVALID.getValue());
        organizationCustomerWalletInfo.setLastModifyTime(LocalDateTime.now());
        int row = organizationCustomerWalletInfoMapper.update(organizationCustomerWalletInfo,
                Wrappers.<OrganizationCustomerWalletInfo>lambdaQuery()
                        .eq(OrganizationCustomerWalletInfo::getId, organizationCustomerWalletInfo.getId())
                        .eq(OrganizationCustomerWalletInfo::getOrganizationNo, organizationCustomerWalletInfo.getOrganizationNo()));
        log.info("update {} row OrganizationCustomerWalletInfo", row);
        organizationCustomerWalletOperationRecordBizService.insertWalletInvalidOperationRecord(
                mpcWalletWebhookRecord, WalletNetworkEnum.KUN_MPC);
        // 通知UU国际
        WalletRechargeRiskExceptionEventVO walletRechargeRiskExceptionEventVO = new WalletRechargeRiskExceptionEventVO();
        walletRechargeRiskExceptionEventVO.setCustomerId(mpcWalletWebhookRecord.getCustomerId());
        walletRechargeRiskExceptionEventVO.setWalletNetwork(WalletNetworkEnum.KUN_MPC.getValue());
        walletRechargeRiskExceptionEventVO.setChainNetwork(mpcWalletWebhookRecord.getChainNetwork());
        walletRechargeRiskExceptionEventVO.setWalletAddress(mpcWalletWebhookRecord.getAddressTo());
        rocketMqService.asyncSend(MqTopicConstant.WALLET_RECHARGE_RISK_EXCEPTION_EVENT_TOPIC, walletRechargeRiskExceptionEventVO);
    }

    /**
     * 钱包创建成功处理
     */
    @Transactional(rollbackFor = Exception.class)
    public void createWalletSuccessProcess(CreateWalletReq createWalletReq, String walletAddress) {
        log.info("[钱包管理-创建钱包]调用钱包提供方成功,开始生成钱包相关数据");
        LocalDateTime now = LocalDateTime.now();
        OrganizationCustomerWalletInfo organizationCustomerWalletInfo = new OrganizationCustomerWalletInfo();
        organizationCustomerWalletInfo.setOrganizationNo(createWalletReq.getOrganizationNo());
        organizationCustomerWalletInfo.setCustomerId(createWalletReq.getCustomerId());
        organizationCustomerWalletInfo.setWalletNetwork(createWalletReq.getWalletNetwork());
        organizationCustomerWalletInfo.setChainNetwork(createWalletReq.getChainNetwork());
        organizationCustomerWalletInfo.setWalletAddress(walletAddress);
        organizationCustomerWalletInfo.setStatus(ValidStatusEnum.VALID.getValue());
        organizationCustomerWalletInfo.setCreateTime(now);
        organizationCustomerWalletInfo.setLastModifyTime(now);
        int row = organizationCustomerWalletInfoMapper.insert(organizationCustomerWalletInfo);
        log.info("insert {} row OrganizationCustomerWalletInfo", row);
        organizationCustomerWalletOperationRecordBizService.insertCreateWalletOperationRecord(
                createWalletReq, OperationStatusEnum.SUCCESS, null, walletAddress);
        log.info("[钱包管理-创建钱包]钱包相关数据生成完成");
    }

    /**
     * 初始化钱包交易明细
     *
     * @param record
     * @param organizationCustomerWalletInfo
     * @return
     */
    private WalletTransactionDetail initWalletTransactionDetail(
            MpcWalletWebhookRecord record, OrganizationCustomerWalletInfo organizationCustomerWalletInfo, OrganizationCustomerAccountInfo organizationCustomerAccountInfo) {
        LocalDateTime now = LocalDateTime.now().withNano(0);
        WalletTransactionDetail walletTransactionDetail = new WalletTransactionDetail();
        walletTransactionDetail.setId("W" + IdWorker.getId());
        walletTransactionDetail.setOrganizationNo(record.getOrganizationNo());
        walletTransactionDetail.setCustomerId(record.getCustomerId());
        walletTransactionDetail.setRequestNo(record.getRequestNo());
        walletTransactionDetail.setWalletNetwork(organizationCustomerWalletInfo.getWalletNetwork());
        walletTransactionDetail.setChainNetwork(organizationCustomerWalletInfo.getChainNetwork());
        walletTransactionDetail.setWalletAddress(organizationCustomerWalletInfo.getWalletAddress());
        walletTransactionDetail.setTransactionDatetime(now);
        walletTransactionDetail.setTransactionType(WalletTransactionTypeEnum.RECHARGE.getValue());
        walletTransactionDetail.setDigitalAmount(record.getAmount());
        walletTransactionDetail.setDigitalCurrencyCode(record.getCurrencyCode());
        // 币种精度现在先写死,数币是6,法币是2
        walletTransactionDetail.setDigitalCurrencyPrecision(6);
        // 法币币种
        walletTransactionDetail.setFiatCurrencyCode(organizationCustomerAccountInfo.getCurrencyCode());
        walletTransactionDetail.setFiatCurrencyPrecision(2);
        // 暂时没有markup,先都设置0
        walletTransactionDetail.setMarkupFeeProportionRate(BigDecimal.ZERO);
        walletTransactionDetail.setMarkupFeeProportionAmount(BigDecimal.ZERO);
        walletTransactionDetail.setMarkupFeeFixedAmount(BigDecimal.ZERO);
        walletTransactionDetail.setMarkupFeeAmount(BigDecimal.ZERO);
        walletTransactionDetail.setCreateTime(now);
        walletTransactionDetail.setLastModifyTime(now);
        return walletTransactionDetail;
    }

    /**
     * 进行汇率换算
     *
     * @param walletTransactionDetail
     */
    private void processingExchangeRate(WalletTransactionDetail walletTransactionDetail, OrganizationBasicInfo organizationBasicInfo) {
        log.info("[MPC钱包Webhook通知事件]开始计算汇率换算");
        // USDC需要查最新汇率,USDT不需要
        BigDecimal fxRate = BigDecimal.ONE;
        if (StringUtils.equals(walletTransactionDetail.getDigitalCurrencyCode(), DigitalCurrencyEnum.USDC.getValue())) {
            // kun现在不支持USDC换法币,此处需要先将USDC换成USDT,在用USDT换法币,因为现在只有法币卡只有美金,USDT:USD直接是1:1,所以此处直接使用USDC换USDT的汇率
            KunAskPriceReq kunAskPriceReq = this.assKunAskPriceReq(walletTransactionDetail, organizationBasicInfo);
            log.info("[MPC钱包Webhook通知事件]调用KUN汇率查询接口开始,请求参数:{}", JSON.toJSONString(kunAskPriceReq));
            Result<KunAskPriceRsp> kunAskPriceRspResult = kCardKunAccountFacade.kunExchangeRate(kunAskPriceReq);
            log.info("[MPC钱包Webhook通知事件]调用KUN汇率查询接口结束,响应参数:{}", JSON.toJSONString(kunAskPriceRspResult));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            if (kunAskPriceRspResult != null && StringUtils.equals(kunAskPriceRspResult.getCode(), String.valueOf(HttpStatus.SC_OK))
                    && kunAskPriceRspResult.getData() != null && kunAskPriceRspResult.getData().getPrice() != null) {
                fxRate = kunAskPriceRspResult.getData().getPrice();
            } else {
                log.error("[MPC钱包Webhook通知事件]调用KUN汇率查询接口失败,响应信息:{}", kunAskPriceRspResult);
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }
        walletTransactionDetail.setFxRate(fxRate);
        // 后面算完手续费之后会减去手续费的金额
        walletTransactionDetail.setFiatAmount(walletTransactionDetail.getDigitalAmount().multiply(fxRate));
        log.info("[MPC钱包Webhook通知事件]汇率换算完成");
    }

    /**
     * 计算充值承兑费
     *
     * @param walletTransactionDetail
     */
    private void calculateRechargeAcceptanceFeeAmount(WalletTransactionDetail walletTransactionDetail) {
        LocalDateTime now = LocalDateTime.now();
        log.info("[MPC钱包Webhook通知事件]开始计算充值承兑费");
        walletTransactionDetail.setRechargeAcceptanceFeeProportionRate(BigDecimal.ZERO);
        walletTransactionDetail.setRechargeAcceptanceFeeProportionAmount(BigDecimal.ZERO);
        walletTransactionDetail.setRechargeAcceptanceFeeFixedAmount(BigDecimal.ZERO);
        CardholderFee cardholderFee = cardholderFeeMapper.selectOne(Wrappers.<CardholderFee>lambdaQuery()
                .eq(CardholderFee::getOrganizationNo, walletTransactionDetail.getOrganizationNo())
                // 充值没有卡,卡产品编号会配置为 *
                .eq(CardholderFee::getCardProductCode, "*")
                .eq(CardholderFee::getStatus, ValidStatusEnum.VALID.getValue())
                .le(CardholderFee::getEffectiveStartTime, now)
                .ge(CardholderFee::getEffectiveEndTime, now));
        if (cardholderFee != null) {
            CardholderFeeDetail cardholderFeeDetail = cardholderFeeDetailMapper.selectOne(
                    Wrappers.<CardholderFeeDetail>lambdaQuery()
                            .eq(CardholderFeeDetail::getFeeId, cardholderFee.getFeeId())
                            .eq(CardholderFeeDetail::getFeeType, CardholderFeeTypeEnum.RECHARGE_ACCEPTANCE_FEE.getValue()));
            if (cardholderFeeDetail != null) {
                BigDecimal proportionRate = cardholderFeeDetail.getActualProportionRate() != null
                        ? cardholderFeeDetail.getActualProportionRate() : BigDecimal.ZERO;
                BigDecimal fixedAmount = cardholderFeeDetail.getActualFixedAmount() != null
                        ? cardholderFeeDetail.getActualFixedAmount() : BigDecimal.ZERO;
                walletTransactionDetail.setRechargeAcceptanceFeeProportionRate(proportionRate);
                walletTransactionDetail.setRechargeAcceptanceFeeProportionMinAmount(cardholderFeeDetail.getActualProportionMinAmount());
                walletTransactionDetail.setRechargeAcceptanceFeeProportionMaxAmount(cardholderFeeDetail.getActualProportionMaxAmount());
                walletTransactionDetail.setRechargeAcceptanceFeeFixedAmount(fixedAmount);
                BigDecimal proportionFeeAmount = walletTransactionDetail.getFiatAmount().multiply(proportionRate);
                walletTransactionDetail.setRechargeAcceptanceFeeProportionAmount(proportionFeeAmount);
                if (cardholderFeeDetail.getActualProportionMinAmount() != null
                        && proportionFeeAmount.compareTo(cardholderFeeDetail.getActualProportionMinAmount()) < 0) {
                    walletTransactionDetail.setRechargeAcceptanceFeeProportionAmount(cardholderFeeDetail.getActualProportionMinAmount());
                } else if (cardholderFeeDetail.getActualProportionMaxAmount() != null
                        && proportionFeeAmount.compareTo(cardholderFeeDetail.getActualProportionMaxAmount()) > 0) {
                    walletTransactionDetail.setRechargeAcceptanceFeeProportionAmount(cardholderFeeDetail.getActualProportionMaxAmount());
                }
            } else {
                log.warn("[MPC钱包Webhook通知事件]未找到手续费配置信息,充值承兑费设置为0");
            }
        } else {
            log.warn("[MPC钱包Webhook通知事件]未找到手续费配置信息,充值承兑费设置为0");
        }
        // 实际手续费金额等于比例+固定值,直接保留两位,向上进位
        walletTransactionDetail.setRechargeAcceptanceFeeAmount(
                walletTransactionDetail.getRechargeAcceptanceFeeProportionAmount()
                        .add(walletTransactionDetail.getRechargeAcceptanceFeeFixedAmount()).setScale(2, RoundingMode.UP));
        // 实际充值法币金额 = 充值法币金额 - 充值承兑手续费
        walletTransactionDetail.setFiatAmount(walletTransactionDetail.getFiatAmount()
                .subtract(walletTransactionDetail.getRechargeAcceptanceFeeAmount()).setScale(2, RoundingMode.DOWN));
        log.info("[MPC钱包Webhook通知事件]充值承兑费计算完成,最终充值承兑手续费为:{}", walletTransactionDetail.getRechargeAcceptanceFeeAmount());
    }

    /**
     * 处理账户动账
     *
     * @param walletTransactionDetail
     */
    private void processingAccountTransaction(WalletTransactionDetail walletTransactionDetail, OrganizationBasicInfo organizationBasicInfo,
                                              OrganizationCustomerAccountInfo organizationCustomerAccountInfo, MpcWalletWebhookRecord mpcWalletWebhookRecord) {
        if (mpcOrganizationBookkeepSwitch) {
            if (mpcWalletWebhookRecord.getOrganizationBookkeepStatus() == null
                    || YesFlagEnum.YES.getNumValue().intValue() != mpcWalletWebhookRecord.getOrganizationBookkeepStatus()) {
                // 此处会有重试的情况,机构记账状态等于1说明已经处理过了
                // 调用kun进行扣账
                KunDebitSubReq kunDebitSubReq = this.assKunDebitSubReq(walletTransactionDetail, organizationBasicInfo);
                log.info("[MPC钱包Webhook通知事件]调用KUN账户扣账接口开始,请求参数:{}", JSON.toJSONString(kunDebitSubReq));
                Result<KunDebitSubRsp> kunDebitSubRspResult = kCardKunAccountFacade.kunDebitSub(kunDebitSubReq);
                log.info("[MPC钱包Webhook通知事件]调用KUN账户扣账接口结束,响应参数:{}", JSON.toJSONString(kunDebitSubRspResult));
                // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
                if (kunDebitSubRspResult != null && StringUtils.equals(kunDebitSubRspResult.getCode(), String.valueOf(HttpStatus.SC_OK))
                        && kunDebitSubRspResult.getData() != null
                        && StringUtils.equals(kunDebitSubRspResult.getData().getStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                    // 有明确成功状态
                    log.info("[MPC钱包Webhook通知事件]调用KUN账户扣账成功");
                    mpcWalletWebhookRecord.setOrganizationBookkeepStatus(YesFlagEnum.YES.getNumValue());
                } else {
                    log.error("[MPC钱包Webhook通知事件]调用KUN账户扣账失败响应信息:{}", JSON.toJSONString(kunDebitSubRspResult));
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            }
        } else {
            log.warn("[MPC钱包Webhook通知事件]机构配置无需校验机构账户金额,跳过KUN账户扣账,直接算未动账");
            mpcWalletWebhookRecord.setOrganizationBookkeepStatus(YesFlagEnum.NO.getNumValue());
        }


        if (mpcWalletWebhookRecord.getCustomerBookkeepStatus() == null
                || YesFlagEnum.YES.getNumValue().intValue() != mpcWalletWebhookRecord.getCustomerBookkeepStatus()) {
            // 此处会有重试的情况,客户记账状态等于1说明已经处理过了
            // 调用账户服务进行上账
            AccountChangeBalanceReq accountChangeBalanceReq = this.assAccountChangeBalanceReq(walletTransactionDetail, organizationCustomerAccountInfo);
            log.info("[MPC钱包Webhook通知事件]调用账户服务动账接口开始,请求参数:{}", JSON.toJSONString(accountChangeBalanceReq));
            Result<AccountChangeBalanceRes> accountChangeBalanceResResult = accountTransactionFacade.changeBalance(accountChangeBalanceReq);
            log.info("[MPC钱包Webhook通知事件]调用账户服务动账接口结束,响应参数:{}", JSON.toJSONString(accountChangeBalanceResResult));
            if (Result.isSuccess(accountChangeBalanceResResult)) {
                // 有明确成功状态
                log.info("[MPC钱包Webhook通知事件]调用账户服务动账成功");
                mpcWalletWebhookRecord.setCustomerBookkeepStatus(YesFlagEnum.YES.getNumValue());
                walletTransactionDetail.setBookkeepNo(accountChangeBalanceResResult.getData().getBookkeepNo());
            } else {
                log.error("[MPC钱包Webhook通知事件]调用账户服务动账接口失败,状态未知,响应信息:{}", JSON.toJSONString(accountChangeBalanceResResult));
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }
    }

    /**
     * 组装申请钱包地址请求参数
     *
     * @param createWalletReq
     * @return
     */
    private ApplyWalletAddressReq assApplyWalletAddressReq(CreateWalletReq createWalletReq, OrganizationBasicInfo organizationBasicInfo) {
        ApplyWalletAddressReq applyWalletAddressReq = new ApplyWalletAddressReq();
        KunApiHeaderVO kunApiHeaderVO = new KunApiHeaderVO();
        kunApiHeaderVO.setToken(organizationBasicInfo.getMpcToken());
        kunApiHeaderVO.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        kunApiHeaderVO.setInvokeId(String.valueOf(IdWorker.getId()));
        kunApiHeaderVO.setAuthCustomerNo(organizationBasicInfo.getOrganizationNo());
        applyWalletAddressReq.setKunApiHeaderVO(kunApiHeaderVO);
        ApplyWalletAddressVO applyWalletAddressVO = new ApplyWalletAddressVO();
        applyWalletAddressVO.setTenantId(organizationBasicInfo.getMpcTenantId());
        applyWalletAddressVO.setNetwork(createWalletReq.getChainNetwork());
        applyWalletAddressVO.setRequestId(createWalletReq.getRequestNo());
        applyWalletAddressReq.setApplyWalletAddressVO(applyWalletAddressVO);
        return applyWalletAddressReq;
    }

    /**
     * 组装kun询价请求参数
     *
     * @param walletTransactionDetail
     * @param organizationBasicInfo
     * @return
     */
    private KunAskPriceReq assKunAskPriceReq(WalletTransactionDetail walletTransactionDetail, OrganizationBasicInfo organizationBasicInfo) {
        KunAskPriceReq kunAskPriceReq = new KunAskPriceReq();
        kunAskPriceReq.setToken(organizationBasicInfo.getMpcToken());
        kunAskPriceReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        kunAskPriceReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        kunAskPriceReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        kunAskPriceReq.setPayAmount(walletTransactionDetail.getDigitalAmount());
        kunAskPriceReq.setSideType(KunSideTypeEnum.SELL.getType());
        // 此处要兑换成的币种直接先写死为USDT
        kunAskPriceReq.setSymbol(walletTransactionDetail.getDigitalCurrencyCode() + "_" + DigitalCurrencyEnum.USDT.getValue());
        return kunAskPriceReq;
    }

    /**
     * 组装kun账户动账请求参数
     *
     * @param walletTransactionDetail
     * @param organizationBasicInfo
     * @return
     */
    private KunDebitSubReq assKunDebitSubReq(WalletTransactionDetail walletTransactionDetail, OrganizationBasicInfo organizationBasicInfo) {
        KunDebitSubReq kunDebitSubReq = new KunDebitSubReq();
        kunDebitSubReq.setToken(organizationBasicInfo.getMpcToken());
        kunDebitSubReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        kunDebitSubReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        kunDebitSubReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        kunDebitSubReq.setRequestNo(walletTransactionDetail.getRequestNo());
        kunDebitSubReq.setCurrency(walletTransactionDetail.getDigitalCurrencyCode());
        kunDebitSubReq.setAmount(walletTransactionDetail.getDigitalAmount());
        kunDebitSubReq.setDirection(KunAndPayXDirectionEnum.TO_GROUP.getDirection());
        kunDebitSubReq.setRemark(KunAndPayXRemarkEnum.RECHARGE.getRemark());
        return kunDebitSubReq;
    }

    /**
     * 组装账户服务动账请求参数
     *
     * @param walletTransactionDetail
     * @param organizationCustomerAccountInfo
     * @return
     */
    private AccountChangeBalanceReq assAccountChangeBalanceReq(WalletTransactionDetail walletTransactionDetail,
                                                               OrganizationCustomerAccountInfo organizationCustomerAccountInfo) {
        AccountChangeBalanceReq accountChangeBalanceReq = new AccountChangeBalanceReq();
        accountChangeBalanceReq.setBusinessSystem(BusinessSystemEnum.KL.getValue());
        accountChangeBalanceReq.setRequestNo(String.valueOf(IdWorker.getId()));
        accountChangeBalanceReq.setBusinessOrganizationNo(walletTransactionDetail.getOrganizationNo());
        accountChangeBalanceReq.setAccountNo(organizationCustomerAccountInfo.getAccountNo());
        accountChangeBalanceReq.setBusinessType(BusinessTypeEnum.RECHARGE.getValue());
        accountChangeBalanceReq.setBusinessAction(BusinessActionEnum.RECHARGE.getValue());
        accountChangeBalanceReq.setAccountingAction(AccountingActionEnum.CREDIT.getValue());
        accountChangeBalanceReq.setBusinessTransactionNo(walletTransactionDetail.getRequestNo());
        accountChangeBalanceReq.setAmount(walletTransactionDetail.getFiatAmount());
        accountChangeBalanceReq.setCurrencyCode(walletTransactionDetail.getFiatCurrencyCode());
        return accountChangeBalanceReq;
    }


    /**
     * 分页查询钱包充值记录
     *
     * @param req
     * @return
     */
    public PageResult<PageQueryWalletRechargeDetailRes> pageQueryWalletRechargeDetail(
            PageQueryWalletRechargeDetailReq req) {
        if (req.getEndDate().isBefore(LocalDate.of(2025, 5, 1))) {
            log.warn("[分页查询钱包充值记录]结束时间不能早于2025-05-01,直接返回空集合");
            return new PageResult<>(Collections.emptyList(), req.getPageNum(), req.getPageSize(), 0);
        }
        LocalDate now = LocalDate.now();
        if (req.getEndDate().isAfter(now)) {
            log.warn("[分页查询钱包充值记录]结束时间不能超过当前,重置结束时间为当前时间");
            req.setEndDate(now);
        }
        long days = ChronoUnit.DAYS.between(req.getStartDate(), req.getEndDate());
        if (days > 365) {
            log.error("[分页查询钱包充值记录]查询时间跨度不能超过365天");
            throw new BusinessException(CommonTipConstant.QUERY_TIME_SPAN_CANNOT_EXCEED_365_DAYS);
        }
        // 查询用户是否存在用户的钱包
        long count = organizationCustomerWalletInfoMapper.selectCount(Wrappers.<OrganizationCustomerWalletInfo>lambdaQuery()
                .eq(OrganizationCustomerWalletInfo::getOrganizationNo, req.getOrganizationNo())
                .eq(OrganizationCustomerWalletInfo::getCustomerId, req.getCustomerId()));
        if (count == 0) {
            log.warn("[分页查询钱包充值记录]用户不存在钱包, 直接返回空集合");
            return new PageResult<>(Collections.emptyList(), req.getPageNum(), req.getPageSize(), 0);
        }
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(req.getPageNum());
        pageParam.setPageSize(req.getPageSize());
        return PageHelperUtil.getPage(pageParam, () -> walletTransactionDetailExtMapper.listWalletRechargeDetailByWhere(
                req.getOrganizationNo(), req.getCustomerId(),
                req.getStartDate().atTime(0, 0, 0),
                req.getEndDate().atTime(23, 59, 59)));
    }
}

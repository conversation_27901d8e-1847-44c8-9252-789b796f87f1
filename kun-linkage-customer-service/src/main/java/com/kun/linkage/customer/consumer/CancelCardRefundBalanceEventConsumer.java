package com.kun.linkage.customer.consumer;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.kun.common.util.lark.LarkAlarmUtil;
import com.kun.common.util.mq.RocketMqService;
import com.kun.linkage.account.facade.api.AccountTransactionFacade;
import com.kun.linkage.account.facade.api.bean.req.AccountChangeBalanceReq;
import com.kun.linkage.account.facade.api.bean.res.AccountChangeBalanceRes;
import com.kun.linkage.account.facade.enums.AccountingActionEnum;
import com.kun.linkage.account.facade.enums.BusinessActionEnum;
import com.kun.linkage.account.facade.enums.BusinessSystemEnum;
import com.kun.linkage.account.facade.enums.BusinessTypeEnum;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.MqConsumerGroupConstant;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.base.enums.OperationStatusEnum;
import com.kun.linkage.common.base.enums.YesFlagEnum;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.external.facade.api.kcard.KCardKunAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.KCardPayXAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXDirectionEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXRemarkEnum;
import com.kun.linkage.common.external.facade.api.kcard.req.KunDebitSubReq;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXDebitSubReq;
import com.kun.linkage.common.external.facade.api.kcard.res.KunDebitSubRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXDebitSubRsp;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import com.kun.linkage.customer.facade.constants.CustomerLockConstant;
import com.kun.linkage.customer.facade.constants.CustomerTipConstant;
import com.kun.linkage.customer.facade.enums.DeductProcessorEnum;
import com.kun.linkage.customer.facade.vo.mq.CancelCardRefundBalanceEventVO;
import com.kun.linkage.customer.service.CardManagementBizService;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 销卡退还余额事件消费者
 */
@Component
@RocketMQMessageListener(consumerGroup = MqConsumerGroupConstant.KL_CANCEL_CARD_REFUND_BALANCE_GROUP,
        topic = MqTopicConstant.CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC, messageModel = MessageModel.CLUSTERING)
public class CancelCardRefundBalanceEventConsumer implements RocketMQListener<CancelCardRefundBalanceEventVO> {
    private static final Logger log = LoggerFactory.getLogger(CancelCardRefundBalanceEventConsumer.class);
    @Resource
    private AccountTransactionFacade accountTransactionFacade;
    @Resource
    private KCardKunAccountFacade kCardKunAccountFacade;
    @Resource
    private KCardPayXAccountFacade kCardPayXAccountFacade;
    @Resource
    private CardManagementBizService cardManagementBizService;
    @Resource
    private RedissonLockUtil redissonLockUtil;
    @Resource
    private LarkAlarmUtil larkAlarmUtil;
    @Resource
    private RocketMqService rocketMqService;

    @Override
    @NewSpan
    public void onMessage(CancelCardRefundBalanceEventVO eventVO) {
        MDC.put("traceId", eventVO.getLogContext().getTraceId());
        log.info("[销卡退还余额事件]接收到事件请求:{}", JSON.toJSONString(eventVO));
        RLock lock = null;
        boolean retryFlag = false;
        String larkAlarmOrganizationAccountRequestNo = "";
        String larkAlarmCustomerAccountRequestNo = "";
        try {
            if (!this.checkParams(eventVO)) {
                log.error("[销卡退还余额事件]请求参数异常,请检查,请求参数:{}", JSON.toJSONString(eventVO));
                return;
            }
            // 使用销卡的请求流水号作为key
            String lockKey = CustomerLockConstant.CANCEL_CARD_REFUND_BALANCE_LOCK_PREFIX + eventVO.getLockRequestNo();
            lock = redissonLockUtil.getLock(lockKey);
            if (lock == null || !lock.tryLock()) {
                log.warn("[销卡退还余额事件]获取锁失败,lockKey:{}", lockKey);
                retryFlag = true;
            } else {
                if (eventVO.getCustomerBookkeepingStatus() == null
                        || Objects.equals(eventVO.getCustomerBookkeepingStatus(), YesFlagEnum.NO.getNumValue())) {
                    log.info("[销卡退还余额事件]开始处理客户账动账");
                    // 如果为空就代表第一次扣或者是前面扣款状态未知,直接使用原来的流水号进行重试,如果明确失败该字段会被设置为空
                    if (StringUtils.isBlank(eventVO.getCustomerAccountRequestNo())) {
                        eventVO.setCustomerAccountRequestNo(String.valueOf(IdWorker.getId()));
                    }
                    larkAlarmCustomerAccountRequestNo = eventVO.getCustomerAccountRequestNo();
                    // 处理客户账动账
                    this.processingCustomerBookkeeping(eventVO);
                } else {
                    log.info("[销卡退还余额事件]客户账已被处理,直接跳过");
                }
                if (eventVO.getOrganizationBookkeepingStatus() == null
                        || Objects.equals(eventVO.getOrganizationBookkeepingStatus(), YesFlagEnum.NO.getNumValue())) {
                    // 如果为空就代表第一次扣或者是前面扣款状态未知,直接使用原来的流水号进行重试,如果明确失败该字段会被设置为空
                    if (StringUtils.isBlank(eventVO.getOrganizationAccountRequestNo())) {
                        eventVO.setOrganizationAccountRequestNo(String.valueOf(IdWorker.getId()));
                    }
                    larkAlarmOrganizationAccountRequestNo = eventVO.getOrganizationAccountRequestNo();
                    // 处理机构账动账
                    this.processingOrganizationBookkeeping(eventVO);
                } else {
                    log.info("[销卡退还余额事件]机构账已被处理,直接跳过");
                }
            }
        } catch (Exception e) {
            log.error("[销卡退还余额事件]处理失败,异常信息:", e);
            retryFlag = true;
        } finally {
            eventVO.setCallCount(eventVO.getCallCount() + 1);
            redissonLockUtil.unlock(lock);
            if (retryFlag && eventVO.getCallCount() < 5) {
                log.warn("[销卡退还余额事件]事件处理失败,10秒后进行重试");
                rocketMqService.delayedSend(MqTopicConstant.CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC, eventVO, 10000, MqTopicConstant.DELAY_LEVEL_10S);
            }
            // 发送告警
            if (retryFlag && eventVO.getCallCount() >= 5) {
                log.warn("[销卡退还余额事件]重试后依旧失败,发送LARK告警");
                if (!Objects.equals(eventVO.getCustomerBookkeepingStatus(), YesFlagEnum.YES.getNumValue())) {
                    // 如果客户账记账失败,则发送LARK告警
                    this.sendLarkAlarm(eventVO, "账户服务", larkAlarmCustomerAccountRequestNo);
                } else {
                    // 客户账记账成功,如果机构账记账失败,则发送LARK告警
                    if (!Objects.equals(eventVO.getOrganizationBookkeepingStatus(), YesFlagEnum.YES.getNumValue())) {
                        this.sendLarkAlarm(eventVO, eventVO.getRefundOrganizationProcessor(), larkAlarmOrganizationAccountRequestNo);
                    }
                }
            }
        }
    }

    /**
     * 发送LARK告警
     *
     * @param eventVO
     */
    private void sendLarkAlarm(CancelCardRefundBalanceEventVO eventVO, String accountProcessor, String requestNo) {
        String msg = String.format("[卡片注销记账异常]机构号:%s, CardID:%s 注销时, 调用 %s 记账失败, 流水号:%s",
                eventVO.getOrganizationNo(), eventVO.getCardId(), accountProcessor, requestNo);
        larkAlarmUtil.sendTextAlarm(msg);
    }

    /**
     * 处理客户账记账
     *
     * @param eventVO
     */
    private void processingCustomerBookkeeping(CancelCardRefundBalanceEventVO eventVO) {
        log.info("[销卡退还余额事件]开始处理客户账动账");
        // 先记客户帐
        AccountChangeBalanceReq accountChangeBalanceReq = new AccountChangeBalanceReq();
        accountChangeBalanceReq.setRequestNo(String.valueOf(IdWorker.getId()));
        accountChangeBalanceReq.setAccountNo(eventVO.getCustomerAccountNo());
        accountChangeBalanceReq.setBusinessType(BusinessTypeEnum.CARD_CANCEL.getValue());
        accountChangeBalanceReq.setBusinessAction(BusinessActionEnum.CARD_CANCEL.getValue());
        accountChangeBalanceReq.setAccountingAction(AccountingActionEnum.DEBIT.getValue());
        accountChangeBalanceReq.setBusinessTransactionNo(eventVO.getCustomerAccountRequestNo());
        accountChangeBalanceReq.setCurrencyCode(eventVO.getCustomerAccountCurrencyCode());
        accountChangeBalanceReq.setAmount(eventVO.getCustomerAccountBalance());
        accountChangeBalanceReq.setBusinessSystem(BusinessSystemEnum.KL.getValue());
        accountChangeBalanceReq.setBusinessOrganizationNo(eventVO.getOrganizationNo());
        Result<AccountChangeBalanceRes> accountChangeBalanceRes = accountTransactionFacade.changeBalance(accountChangeBalanceReq);
        if (Result.isSuccess(accountChangeBalanceRes)) {
            // 有明确成功状态
            log.info("[销卡退还余额事件]调用账户服务动账成功");
            eventVO.setCustomerBookkeepingStatus(YesFlagEnum.YES.getNumValue());
        } else {
            log.error("[MPC钱包Webhook通知事件]调用账户服务动账接口失败,状态未知,响应信息:{}", JSON.toJSONString(accountChangeBalanceRes));
            throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
        }
    }

    /**
     * 处理机构记账
     *
     * @param eventVO
     */
    private void processingOrganizationBookkeeping(CancelCardRefundBalanceEventVO eventVO) {
        log.info("[销卡退还余额事件]开始处理机构账动账");
        if (StringUtils.equals(eventVO.getRefundOrganizationProcessor(), DeductProcessorEnum.KUN.getValue())) {
            // 调用kun动账
            KunDebitSubReq kunDebitSubReq = new KunDebitSubReq();
            kunDebitSubReq.setToken(eventVO.getMpcToken());
            kunDebitSubReq.setGroupProductCode(eventVO.getMpcGroupCode());
            kunDebitSubReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
            kunDebitSubReq.setAccountNo(eventVO.getOrganizationNo());
            kunDebitSubReq.setDirection(KunAndPayXDirectionEnum.TO_USER.getDirection());
            kunDebitSubReq.setRequestNo(eventVO.getOrganizationAccountRequestNo());
            kunDebitSubReq.setCurrency(eventVO.getRefundCurrencyCode());
            kunDebitSubReq.setAmount(eventVO.getRefundOrganizationAmount());
            kunDebitSubReq.setRemark(KunAndPayXRemarkEnum.CANCEL_CARD.getRemark());
            log.info("[销卡退还余额事件]调用KUN退还本金开始,请求参数:{}", JSON.toJSONString(kunDebitSubReq));
            Result<KunDebitSubRsp> result = kCardKunAccountFacade.kunDebitSub(kunDebitSubReq);
            log.info("[销卡退还余额事件]调用KUN退还本金结束,响应参数:{}", JSON.toJSONString(result));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
                if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                    // 明确成功
                    log.info("[销卡退还余额事件]调用KUN账户退还本金成功");
                    eventVO.setOrganizationBookkeepingStatus(YesFlagEnum.YES.getNumValue());
                    // 余额退还成功还需要收取承兑费
                    cardManagementBizService.cancelCardCalculateAcceptanceFee(eventVO);
                } else if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.FAIL.getStatus())) {
                    // 明确失败
                    log.error("[销卡退还余额事件]调用KUN账户退还本金明确失败");
                    // 明确失败需要将request清空,后面生成新的进行调用
                    eventVO.setOrganizationAccountRequestNo(null);
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                } else {
                    log.error("[销卡退还余额事件]调用KUN账户退还本金状态未知");
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } else {
                log.error("[销卡退还余额事件]调用KUN账户退还本金状态未知");
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        } else {
            // 调用payx动账
            PayXDebitSubReq payXDebitSubReq = new PayXDebitSubReq();
            payXDebitSubReq.setToken(eventVO.getMpcToken());
            payXDebitSubReq.setGroupProductCode(eventVO.getMpcGroupCode());
            payXDebitSubReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
            payXDebitSubReq.setAccountNo(eventVO.getOrganizationNo());
            payXDebitSubReq.setDirection(KunAndPayXDirectionEnum.TO_USER.getDirection());
            payXDebitSubReq.setRequestNo(eventVO.getOrganizationAccountRequestNo());
            payXDebitSubReq.setCurrency(eventVO.getRefundCurrencyCode());
            payXDebitSubReq.setAmount(eventVO.getRefundOrganizationAmount());
            payXDebitSubReq.setRemark(KunAndPayXRemarkEnum.CANCEL_CARD.getRemark());
            log.info("[销卡退还余额事件]调用PayX账户动账开始,请求参数:{}", JSON.toJSONString(payXDebitSubReq));
            Result<PayXDebitSubRsp> result = kCardPayXAccountFacade.payXDebitSub(payXDebitSubReq);
            log.info("[销卡退还余额事件]调用PayX账户动账结束,响应参数:{}", JSON.toJSONString(result));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
                if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                    // 明确成功
                    log.info("[销卡退还余额事件]调用PayX账户动账成功");
                    eventVO.setOrganizationBookkeepingStatus(YesFlagEnum.YES.getNumValue());
                } else if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.FAIL.getStatus())) {
                    // 明确失败
                    log.error("[销卡退还余额事件]调用PayX账户动账明确失败");
                    // 明确失败需要将request清空,后面生成新的进行调用
                    eventVO.setOrganizationAccountRequestNo(null);
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                } else {
                    log.error("[销卡退还余额事件]调用PayX账户退还本金状态未知");
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } else {
                log.error("[销卡退还余额事件]调用PayX账户退还本金状态未知");
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }
    }

    /**
     * 校验请求参数
     *
     * @param eventVO
     * @return
     */
    private boolean checkParams(CancelCardRefundBalanceEventVO eventVO) {
        return StringUtils.isBlank(eventVO.getMpcToken())
                || StringUtils.isBlank(eventVO.getMpcGroupCode())
                || StringUtils.isBlank(eventVO.getOrganizationNo())
                || StringUtils.isBlank(eventVO.getCardId())
                || StringUtils.isBlank(eventVO.getCardProductCode())
                || StringUtils.isBlank(eventVO.getCustomerAccountNo())
                || eventVO.getCustomerAccountBalance() == null
                || StringUtils.isBlank(eventVO.getCustomerAccountCurrencyCode())
                || eventVO.getRefundOrganizationAmount() == null
                || StringUtils.isBlank(eventVO.getRefundCurrencyCode());
    }
}

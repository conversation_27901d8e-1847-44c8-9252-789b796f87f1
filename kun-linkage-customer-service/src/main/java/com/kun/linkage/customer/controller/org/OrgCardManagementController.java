package com.kun.linkage.customer.controller.org;

import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.customer.facade.api.bean.req.OrgPageQueryCardRechargeDetailReq;
import com.kun.linkage.customer.facade.api.bean.res.OrgPageQueryCardRechargeDetailRes;
import com.kun.linkage.customer.facade.dto.organization.basic.OrganizationCardQueryDTO;
import com.kun.linkage.customer.facade.vo.organization.OrganizationCardQueryVO;
import com.kun.linkage.customer.service.OrgCardRechargeQueryService;
import com.kun.linkage.customer.service.export.CardRechargeExportService;
import com.kun.linkage.customer.service.organization.OrganizationCardManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 卡管理接口
 * </p>
 */
@Tag(name = "卡管理", description = "商户端卡管理")
@RestController
@RequestMapping("/org/cardManagement")
public class OrgCardManagementController {
    @Resource
    private OrganizationCardManagementService organizationCardManagementService;
    @Resource
    private OrgCardRechargeQueryService orgCardRechargeQueryService;

    @Resource
    private CardRechargeExportService cardRechargeExportService;

    /**
     * 分页查询机构客户账户信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询机构客户卡片信息", summary = "分页查询机构客户卡片信息")
    @PostMapping("/pageList")
    public Result<PageResult<OrganizationCardQueryVO>> pageList(@RequestBody OrganizationCardQueryDTO dto) {
        PageResult<OrganizationCardQueryVO> organizationCustomerCardList = organizationCardManagementService.getOrganizationCustomerCardList(dto);
        return Result.success(organizationCustomerCardList);
    }

    /**
     * 分页查询卡充值记录
     *
     * @param pageQueryCardRechargeDetailReq
     * @return
     */
    @Operation(description = "分页查询卡充值记录", summary = "分页查询卡充值记录")
    @RequestMapping(value = "/pageQueryCardRechargeDetail", method = RequestMethod.POST)
    public Result<PageResult<OrgPageQueryCardRechargeDetailRes>> pageQueryCardRechargeDetail(
            @RequestBody @Validated OrgPageQueryCardRechargeDetailReq pageQueryCardRechargeDetailReq) {
        return Result.success(orgCardRechargeQueryService.pageQueryCardRechargeDetail(pageQueryCardRechargeDetailReq));
    }

    /**
     * 异步导出卡充值记录
     *
     * @param req 查询条件
     * @return 导出任务信息
     */
    @Operation(description = "异步导出卡充值记录", summary = "异步导出卡充值记录")
    @PostMapping("/asyncExportCardRecharge")
    public Result<String> asyncExportCardRecharge(@RequestBody @Validated OrgPageQueryCardRechargeDetailReq req) {
        try {
            // 验证日期范围
            cardRechargeExportService.validateDateRange(req.getStartDate(), req.getEndDate());
            // 创建导出任务
            String fileRecordId = cardRechargeExportService.createExportTask(req);
            return Result.success(fileRecordId);
        } catch (IllegalArgumentException e) {
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            return Result.fail("导出卡充值记录失败：" + e.getMessage());
        }
    }
}

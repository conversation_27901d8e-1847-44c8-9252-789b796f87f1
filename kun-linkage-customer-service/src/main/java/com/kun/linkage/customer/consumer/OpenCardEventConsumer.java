package com.kun.linkage.customer.consumer;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.IpUtils;
import com.kun.common.util.mq.RocketMqService;
import com.kun.linkage.common.base.constants.MqConsumerGroupConstant;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.base.enums.OpenCardStatusEnum;
import com.kun.linkage.common.base.enums.OperationStatusEnum;
import com.kun.linkage.common.db.entity.OrganizationCustomerCardOperationRecord;
import com.kun.linkage.common.db.mapper.OrganizationCustomerCardOperationRecordMapper;
import com.kun.linkage.common.external.facade.api.kcard.KCardCardManagementFacade;
import com.kun.linkage.common.external.facade.api.kcard.constants.KCardApiErrorCode;
import com.kun.linkage.common.external.facade.api.kcard.req.KCardOpenCardQueryReq;
import com.kun.linkage.common.external.facade.api.kcard.res.KCardOpenCardQueryRsp;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import com.kun.linkage.customer.facade.constants.CustomerLockConstant;
import com.kun.linkage.customer.facade.enums.CardOperationTypeEnum;
import com.kun.linkage.customer.facade.vo.mq.OpenCardEventVO;
import com.kun.linkage.customer.service.CardManagementBizService;
import com.kun.linkage.customer.service.OrganizationCustomerCardOperationRecordBizService;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 开卡事件消费者
 *
 */
@Component
@RocketMQMessageListener(consumerGroup = MqConsumerGroupConstant.KL_CUSTOMER_OPEN_CARD_GROUP, topic = MqTopicConstant.OPEN_CARD_EVENT_TOPIC, messageModel = MessageModel.CLUSTERING)
public class OpenCardEventConsumer implements RocketMQListener<OpenCardEventVO> {
    private static final Logger log = LoggerFactory.getLogger(OpenCardEventConsumer.class);
    @Resource
    private CardManagementBizService cardManagementBizService;
    @Resource
    private OrganizationCustomerCardOperationRecordMapper organizationCustomerCardOperationRecordMapper;
    @Resource
    private OrganizationCustomerCardOperationRecordBizService organizationCustomerCardOperationRecordBizService;
    @Resource
    private KCardCardManagementFacade kCardCardManagementFacade;
    @Resource
    private RedissonLockUtil redissonLockUtil;
    @Resource
    private RocketMqService rocketMqService;

    @Override
    @NewSpan
    public void onMessage(OpenCardEventVO openCardEventVO) {
        MDC.put("traceId", openCardEventVO.getLogContext().getTraceId());
        log.info("[开卡事件-开卡状态查询]接收到开卡事件:{}", openCardEventVO);
        // 开卡处理中的记录需要去KCard查询开卡结果
        RLock lock = null;
        boolean retryFlag = false;
        try {
            // 使用机构号+客户号+卡id作为key
            String lockKey = CustomerLockConstant.OPEN_CARD_STATUS_QUERY_LOCK_PREFIX
                    + openCardEventVO.getOrganizationNo() + openCardEventVO.getCustomerId()  + openCardEventVO.getCardId();
            lock = redissonLockUtil.getLock(lockKey);
            if (lock == null || !lock.tryLock()) {
                log.warn("[开卡事件-开卡状态查询]获取锁失败,lockKey:{},5分钟后再次尝试消费", lockKey);
                retryFlag = true;
            } else {
                OrganizationCustomerCardOperationRecord organizationCustomerCardOperationRecord = organizationCustomerCardOperationRecordMapper.selectOne(
                        Wrappers.<OrganizationCustomerCardOperationRecord>lambdaQuery()
                                .eq(OrganizationCustomerCardOperationRecord::getOrganizationNo, openCardEventVO.getOrganizationNo())
                                .eq(OrganizationCustomerCardOperationRecord::getCustomerId, openCardEventVO.getCustomerId())
                                .eq(OrganizationCustomerCardOperationRecord::getOperationType, CardOperationTypeEnum.OPEN_CARD.getType())
                                .eq(OrganizationCustomerCardOperationRecord::getCardId, openCardEventVO.getCardId()));
                if (organizationCustomerCardOperationRecord == null) {
                    log.warn("[开卡事件-开卡状态查询]开卡操作记录不存在,机构号:{},客户号:{},卡id:{}",
                            openCardEventVO.getOrganizationNo(), openCardEventVO.getCustomerId(), openCardEventVO.getCardId());
                    return ;
                }
                if (StringUtils.equals(organizationCustomerCardOperationRecord.getOperationStatus(), OpenCardStatusEnum.SUCCESS.getStatus())
                        || StringUtils.equals(organizationCustomerCardOperationRecord.getOperationStatus(), OpenCardStatusEnum.FAIL.getStatus())) {
                    log.warn("[开卡事件-开卡状态查询]开卡状态已被处理,机构号:{},客户号:{},卡id:{}",
                            openCardEventVO.getOrganizationNo(), openCardEventVO.getCustomerId(), openCardEventVO.getCardId());
                    return ;
                }
                if (LocalDateTime.now().isAfter(organizationCustomerCardOperationRecord.getCreateTime().plusHours(1))) {
                    // 如果当前时间在创建时间一小时之后就直接更新开卡结果为失败,防止外部系统原因导致一直是开卡中,用户会卡死在流程中,此处为兜底逻辑(正常情况开卡是同步返回的肯定不会超1小时)
                    log.warn("[开卡事件-开卡状态查询]开卡发起到现在已超过一小时,直接算失败处理,机构号:{},客户号:{},卡id:{}",
                            openCardEventVO.getOrganizationNo(), openCardEventVO.getCustomerId(), openCardEventVO.getCardId());
                    organizationCustomerCardOperationRecordBizService
                            .updateCardOperationRecordStatus(organizationCustomerCardOperationRecord.getId(), organizationCustomerCardOperationRecord.getOrganizationNo(),
                                    null, null, OperationStatusEnum.FAIL, "开卡发起到现在已超过一小时,直接算失败处理");
                    return ;
                }
                // 调用KCard查询开卡状态
                KCardOpenCardQueryReq kCardOpenCardQueryReq = new KCardOpenCardQueryReq();
                kCardOpenCardQueryReq.setCustomerId(openCardEventVO.getOrganizationNo());
                kCardOpenCardQueryReq.setRequestNo(String.valueOf(IdWorker.getId()));
                String ip = IpUtils.getHostIp();
                kCardOpenCardQueryReq.setIp(ip);
                kCardOpenCardQueryReq.setCardId(openCardEventVO.getCardId());
                log.info("[开卡事件-开卡状态查询]调用KCard开卡状态查询开始,请求参数:{}", JSON.toJSONString(kCardOpenCardQueryReq));
                KCardOpenCardQueryRsp kCardOpenCardQueryRsp = kCardCardManagementFacade.openCardStatusQuery(kCardOpenCardQueryReq);
                log.info("[开卡事件-开卡状态查询]调用KCard开卡状态查询结束,响应参数:{}", JSON.toJSONString(kCardOpenCardQueryRsp));
                if (kCardOpenCardQueryRsp != null
                        && StringUtils.equals(kCardOpenCardQueryRsp.getCode(), KCardApiErrorCode.SUCCESS.getCode())) {
                    log.info("[开卡事件-开卡状态查询]调用KCard开卡状态查询请求成功");
                    // 需要看具体的状态
                    if (StringUtils.equals(kCardOpenCardQueryRsp.getOpenCardStatus(), OpenCardStatusEnum.PENDING.getStatus())) {
                        retryFlag = true;
                    } else if (StringUtils.equals(kCardOpenCardQueryRsp.getOpenCardStatus(), OpenCardStatusEnum.FAIL.getStatus())) {
                        // 开卡失败更新操作记录表为失败
                        organizationCustomerCardOperationRecordBizService
                                .updateCardOperationRecordStatus(organizationCustomerCardOperationRecord.getId(), organizationCustomerCardOperationRecord.getOrganizationNo(),
                                        null, null, OperationStatusEnum.FAIL, "查询KCard开卡状态响应开卡结果为失败,具体原因需查看KCard");
                    } else if (StringUtils.equals(kCardOpenCardQueryRsp.getOpenCardStatus(), OpenCardStatusEnum.SUCCESS.getStatus())) {
                        // 开卡成功,更新操作记录表,新增用户信息,卡信息,账户信息
                        try {
                            cardManagementBizService.openCardSuccessProcess(organizationCustomerCardOperationRecord, kCardOpenCardQueryRsp.getCardNo());
                        } catch (Exception e) {
                            // 开卡成功处理失败(后续会继续进行尝试)
                            log.error("[开卡事件-开卡状态查询]开卡成功后生成开卡数据异常,请检查", e);
                            retryFlag = true;
                        }
                    } else {
                        // 未知开卡状态
                        log.error("[开卡事件-开卡状态查询]调用KCard查询到的开卡状态未知,请检查");
                        retryFlag = true;
                    }
                } else {
                    // 开卡请求失败,返回机构端为开卡中
                    log.error("[开卡事件-开卡状态查询]调用KCard开卡状态查询请求失败");
                    retryFlag = true;
                }
            }

        } catch (Exception e) {
            // 调用KCard异常
            log.error("[卡片管理-开卡状态查询]调用KCard开卡状态查询异常,异常信息:", e);
            retryFlag = true;
        } finally {
            redissonLockUtil.unlock(lock);
            if (retryFlag) {
                log.warn("[开卡事件-开卡状态查询]开卡状态未确定,5分钟后进行重试");
                rocketMqService.delayedSend(MqTopicConstant.OPEN_CARD_EVENT_TOPIC, openCardEventVO, 10000,  MqTopicConstant.DELAY_LEVEL_5M);
            }
        }
    }
}

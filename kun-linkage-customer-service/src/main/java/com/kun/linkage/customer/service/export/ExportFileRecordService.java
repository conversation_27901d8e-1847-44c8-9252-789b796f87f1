package com.kun.linkage.customer.service.export;

import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.KLExportFileRecord;
import com.kun.linkage.common.db.mapper.KLExportFileRecordMapper;
import com.kun.linkage.customer.facade.constant.FeeExportConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 导出文件记录服务
 */
@Slf4j
@Service
public class ExportFileRecordService {

    @Resource
    private KLExportFileRecordMapper klExportFileRecordMapper;

    /**
     * 创建文件记录
     */
    public KLExportFileRecord createFileRecord(String organizationNo, String fileName, String fileType) {
        KLExportFileRecord record = new KLExportFileRecord();
        record.setOrganizationNo(organizationNo);
        record.setFileName(fileName);
        record.setFileType(fileType);
        record.setFileStatus(FeeExportConstant.FileStatus.PROCESSING.getCode());
        record.setCreateTime(DateTimeUtils.getCurrentDateTime());
        record.setUpdateTime(DateTimeUtils.getCurrentDateTime());

        klExportFileRecordMapper.insert(record);
        log.info("创建文件记录成功，文件记录ID: {}, 文件名: {}", record.getFileRecordId(), fileName);
        return record;
    }

    /**
     * 更新文件记录为成功状态
     */
    public void updateFileRecordSuccess(String fileRecordId, String s3Url, long fileSize) {
        KLExportFileRecord record = new KLExportFileRecord();
        record.setFileRecordId(fileRecordId);
        record.setS3Url(s3Url);
        record.setFileSize(fileSize);
        record.setFileStatus(FeeExportConstant.FileStatus.SUCCESS.getCode());
        record.setUpdateTime(DateTimeUtils.getCurrentDateTime());

        klExportFileRecordMapper.updateById(record);
        log.info("更新文件记录为成功状态，文件记录ID: {}, S3 URL: {}", fileRecordId, s3Url);
    }

    /**
     * 更新文件记录为失败状态
     */
    public void updateFileRecordFailed(String fileRecordId, String errorMessage) {
        KLExportFileRecord record = new KLExportFileRecord();
        record.setFileRecordId(fileRecordId);
        record.setFileStatus(FeeExportConstant.FileStatus.FAILED.getCode());
        record.setErrorMessage(errorMessage);
        record.setUpdateTime(DateTimeUtils.getCurrentDateTime());

        klExportFileRecordMapper.updateById(record);
        log.info("更新文件记录为失败状态，文件记录ID: {}, 错误信息: {}", fileRecordId, errorMessage);
    }
}

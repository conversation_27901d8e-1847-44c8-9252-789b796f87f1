package com.kun.linkage.customer.service.organization;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.AccountInfo;
import com.kun.linkage.common.db.entity.OrganizationCustomerCardInfo;
import com.kun.linkage.common.db.mapper.AccountInfoMapper;
import com.kun.linkage.common.db.mapper.OrganizationCustomerCardInfoMapper;
import com.kun.linkage.customer.facade.dto.organization.basic.OrganizationCardQueryDTO;
import com.kun.linkage.customer.facade.vo.organization.OrganizationCardQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrganizationCardManagementService {

    @Resource
    private OrganizationCustomerCardInfoMapper organizationCustomerCardInfoMapper;
    @Resource
    private AccountInfoMapper accountInfoMapper;

    /**
     * 获取商户卡列表
     *
     * @param
     * @return
     */
    public PageResult<OrganizationCardQueryVO> getOrganizationCustomerCardList(OrganizationCardQueryDTO dto) {
        log.info("获取商户卡列表请求参数:{}", dto);
        return PageHelperUtil.getPage(dto, () -> {
            // 1. 查询卡片信息
            LambdaQueryWrapper<OrganizationCustomerCardInfo> wrapper = Wrappers.<OrganizationCustomerCardInfo>lambdaQuery()
                    .eq(StringUtils.isNotBlank(dto.getOrganizationNo()), OrganizationCustomerCardInfo::getOrganizationNo, dto.getOrganizationNo())
                    .eq(StringUtils.isNotBlank(dto.getCardId()), OrganizationCustomerCardInfo::getCardId, dto.getCardId())
                    .eq(StringUtils.isNotBlank(dto.getCustomerId()), OrganizationCustomerCardInfo::getCustomerId, dto.getCustomerId())
                    .eq(StringUtils.isNotBlank(dto.getCardStatus()), OrganizationCustomerCardInfo::getCardStatus, dto.getCardStatus())
                    .orderByDesc(OrganizationCustomerCardInfo::getCreateTime);
            if (dto.getCreateDateFrom() != null) {
                wrapper.ge(OrganizationCustomerCardInfo::getCreateTime, dto.getCreateDateFrom().atStartOfDay());
            }
            if (dto.getCreateDateUntil() != null) {
                wrapper.le(OrganizationCustomerCardInfo::getCreateTime, dto.getCreateDateUntil().atTime(23, 59, 59));
            }
            List<OrganizationCustomerCardInfo> cardList = organizationCustomerCardInfoMapper.selectList(
                    wrapper);


            if (cardList.isEmpty()) {
                return Collections.emptyList();
            }

            // 2. 批量查询账户信息
            Set<String> customerIds = cardList.stream().map(OrganizationCustomerCardInfo::getCustomerId).collect(Collectors.toSet());
            Set<String> currencyCodes = cardList.stream().map(OrganizationCustomerCardInfo::getCurrencyCode).collect(Collectors.toSet());

            List<AccountInfo> accountList = accountInfoMapper.selectList(
                    Wrappers.<AccountInfo>lambdaQuery()
                            .eq(StringUtils.isNotBlank(dto.getOrganizationNo()), AccountInfo::getBusinessOrganizationNo, dto.getOrganizationNo())
                            .in(AccountInfo::getBusinessCustomerId, customerIds)
                            .in(AccountInfo::getCurrencyCode, currencyCodes)
            );

            // 3. 构建账户映射
            Map<String, AccountInfo> accountMap = accountList.stream()
                    .collect(Collectors.toMap(
                            account -> account.getBusinessCustomerId() + account.getCurrencyCode() + account.getBusinessOrganizationNo(),
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));

            return cardList.stream()
                    .map(card -> {
                        OrganizationCardQueryVO vo = new OrganizationCardQueryVO();
                        vo.setId(String.valueOf(card.getId()));
                        vo.setOpenTime(card.getCreateTime());
                        vo.setOrganizationNo(card.getOrganizationNo());
                        vo.setCustomerId(card.getCustomerId());
                        vo.setCardId(card.getCardId());
                        vo.setCardNo(card.getMaskedCardNo());
                        vo.setCurrency(card.getCurrencyCode());
                        vo.setCardStatus(card.getCardStatus());
                        vo.setCardActiveStatus(card.getCardActiveStatus());
                        vo.setCardholderMobile("+" + card.getMobilePhoneArea() + " " + card.getMobilePhone());
                        vo.setCardholderEmail(card.getEmail());
                        vo.setCardholderName(card.getCardholderFirstName() + " " + card.getCardholderLastName());

                        String accountKey = card.getCustomerId() + card.getCurrencyCode() + card.getOrganizationNo();
                        AccountInfo account = accountMap.get(accountKey);
                        if (account != null) {
                            vo.setAvailableAmount(account.getAvailableAmount());
                        }
                        return vo;
                    })
                    .collect(Collectors.toList());
        });
    }
}

package com.kun.linkage.customer.ext.mapper;


import com.kun.linkage.customer.facade.vo.report.OrganizationFeeMonthReportVO;

import java.util.List;

/**
 * <p>
 * 机构费用明细表 Mapper 扩展接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
public interface OrganizationFeeDetailExtMapper {
    /**
     * 查询机构费用月报表数据
     *
     * @param tableIndex
     * @return
     */
    List<OrganizationFeeMonthReportVO> queryOrganizationFeeMonthReportData(String tableIndex);
}

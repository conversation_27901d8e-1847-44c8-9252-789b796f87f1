package com.kun.linkage.customer.ext.mapper;

import com.kun.linkage.customer.facade.vo.MessageSendRecordGroupCountVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 信息发送记录 Mapper 扩展接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface MessageSendRecordExtMapper {
    /**
     * 根据条件查询钱包充值记录
     *
     * @param startDatetime
     * @param endDatetime
     * @return
     */
    List<MessageSendRecordGroupCountVO> groupCountSMSRecordByWhere(
            @Param("startDatetime") LocalDateTime startDatetime, @Param("endDatetime") LocalDateTime endDatetime);
}

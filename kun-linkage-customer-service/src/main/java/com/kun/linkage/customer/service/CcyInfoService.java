package com.kun.linkage.customer.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.kun.linkage.common.base.enums.YesFlagEnum;
import com.kun.linkage.common.db.entity.CurrencyInfo;
import com.kun.linkage.common.db.mapper.CurrencyInfoMapper;
import com.kun.linkage.customer.constant.CustomerServiceApplicationConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * title: <br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * @date 2025/7/4 17:12
 */
@Service
public class CcyInfoService {

    @Resource
    private CurrencyInfoMapper currencyInfoMapper;

    /**
     * title: <br>
     * @description: 根据币种查询币种信息
     * Copyright: Copyright (c)2014<br>
     * Company: 易宝支付(YeePay)<br>
     *
     * <AUTHOR>
     * @version 1.0.0
     * @since 2025/7/4 17:28
     */
    @Cacheable(value = CustomerServiceApplicationConstant.APPLICATION_NAME,
            key = "'CcyInfoCachePrefix:' + #currencyCode", unless = "#result == null")
    public CurrencyInfo getCardCurrencyInfo(String currencyCode) {
        QueryWrapper<CurrencyInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CurrencyInfo::getCcyCode, currencyCode).eq(CurrencyInfo::getStatus, YesFlagEnum.YES.getNumValue());
        return currencyInfoMapper.selectOne(queryWrapper);
    }
}

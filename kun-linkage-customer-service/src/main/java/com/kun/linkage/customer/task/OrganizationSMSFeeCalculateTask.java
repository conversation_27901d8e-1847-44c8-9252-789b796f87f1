package com.kun.linkage.customer.task;

import com.alibaba.fastjson.JSON;
import com.kun.common.util.lark.LarkAlarmUtil;
import com.kun.common.util.log.trace.LogContext;
import com.kun.linkage.customer.service.OrganizationSMSFeeCalculateBizService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

/**
 * 机构SMS费用计算任务
 */
@Slf4j
@Component
public class OrganizationSMSFeeCalculateTask {
    @Resource
    private OrganizationSMSFeeCalculateBizService organizationSMSFeeCalculateBizService;
    @Resource
    private LarkAlarmUtil larkAlarmUtil;

    @NewSpan
    @XxlJob("organizationSMSFeeCalculateTask")
    public boolean organizationSMSFeeCalculateTask() {
        XxlJobHelper.log("[机构SMS费用计算任务]开始执行");
        YearMonth yearMonth = null;
        try {
            LogContext.fromContext(LogContext.getContext());
            String jobParam = XxlJobHelper.getJobParam();
            if (StringUtils.isNotBlank(jobParam)) {
                XxlJobHelper.log("[机构SMS费用计算任务]计算的月份为XXL-JOB中传入的月份");
                yearMonth = YearMonth.parse(JSON.parseObject(jobParam).getString("yearMonth"), DateTimeFormatter.ofPattern("yyyyMM"));
            } else {
                XxlJobHelper.log("[机构SMS费用计算任务]计算的月份为上个月");
                yearMonth = YearMonth.now().minusMonths(1);
            }
            XxlJobHelper.log("[机构SMS费用计算任务]计算的月份:{}", yearMonth);
            organizationSMSFeeCalculateBizService.calculateSMSFee(yearMonth);
            return XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            XxlJobHelper.log(e.getMessage());
            XxlJobHelper.log(e);
            this.sendLarkAlarm(yearMonth);
            return XxlJobHelper.handleFail(e.getMessage());
        } finally {
            XxlJobHelper.log("[机构SMS费用计算任务]执行结束", LogContext.getContext().getTraceId());
            LogContext.destroy();
        }
    }

    /**
     * 发送LARK告警
     * @param yearMonth
     */
    private void sendLarkAlarm(YearMonth yearMonth) {
        log.warn("[机构SMS费用计算任务]开始发送lark告警,数据日期:{}", yearMonth);
        String msg = String.format("[机构SMS费用计算失败] 数据日期:%s, 计算日期:%s", yearMonth, LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE));
        larkAlarmUtil.sendTextAlarm(msg);
    }
}

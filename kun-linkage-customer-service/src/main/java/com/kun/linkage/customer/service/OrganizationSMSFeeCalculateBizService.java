package com.kun.linkage.customer.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.linkage.common.base.enums.DigitalCurrencyEnum;
import com.kun.linkage.common.db.entity.OrganizationBasicInfo;
import com.kun.linkage.common.db.entity.OrganizationFeeConfig;
import com.kun.linkage.common.db.entity.OrganizationFeeDetail;
import com.kun.linkage.common.db.entity.OrganizationFeeTemplateDetail;
import com.kun.linkage.common.db.mapper.OrganizationBasicInfoMapper;
import com.kun.linkage.common.db.mapper.OrganizationFeeDetailMapper;
import com.kun.linkage.common.db.mapper.OrganizationFeeTemplateDetailMapper;
import com.kun.linkage.customer.ext.mapper.MessageSendRecordExtMapper;
import com.kun.linkage.customer.facade.enums.*;
import com.kun.linkage.customer.facade.vo.MessageSendRecordGroupCountVO;
import com.kun.linkage.customer.service.organization.OrganizationFeeConfigBizService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class OrganizationSMSFeeCalculateBizService {
    private static final Logger log = LoggerFactory.getLogger(OrganizationSMSFeeCalculateBizService.class);
    @Resource
    private MessageSendRecordExtMapper messageSendRecordExtMapper;
    @Resource
    private OrganizationFeeConfigBizService organizationFeeConfigBizService;
    @Resource
    private OrganizationBasicInfoMapper organizationBasicInfoMapper;
    @Resource
    private OrganizationFeeTemplateDetailMapper organizationFeeTemplateDetailMapper;
    @Resource
    private OrganizationFeeDetailMapper organizationFeeDetailMapper;
    @Resource
    private ExchangeRateBizService exchangeRateBizService;

    /**
     * 计算SMS费用
     *
     * @param yearMonth
     * @return
     */
    public void calculateSMSFee(YearMonth yearMonth) {
        log.info("[机构SMS费用计算]开始计算机构SMS费用,数据日期:{}", yearMonth);
        // 执行月份第一天的0时0分0秒
        LocalDateTime startDateTime = yearMonth.atDay(1).atStartOfDay();
        // 执行月份下个月第一天的0时0分0秒
        LocalDateTime endDateTime = yearMonth.plusMonths(1).atDay(1).atStartOfDay();
        // 删除已生成的数据,防止重跑
        organizationFeeDetailMapper.delete(Wrappers.lambdaQuery(OrganizationFeeDetail.class)
                .eq(OrganizationFeeDetail::getFeeType, OrganizationFeeTypeEnum.SMS_FEE.getValue())
                .ge(OrganizationFeeDetail::getTransactionDatetime, startDateTime)
                .lt(OrganizationFeeDetail::getTransactionDatetime, endDateTime));
        // 区间大于等于开始时间 小于结束时间
        List<MessageSendRecordGroupCountVO> groupCountVOList = messageSendRecordExtMapper.groupCountSMSRecordByWhere(startDateTime, endDateTime);
        if (groupCountVOList == null || groupCountVOList.isEmpty()) {
            log.info("[机构SMS费用计算]没有待处理数据,数据日期:{}", yearMonth);
            return;
        }
        // 汇率缓存,key为换汇的币兑,value为汇率
        Map<String, BigDecimal> fxRateCache = new HashMap<>();
        for (MessageSendRecordGroupCountVO groupCountVO : groupCountVOList) {
            log.info("[机构SMS费用计算]开始处理机构SMS费用,数据日期:{},机构号:{}", yearMonth, groupCountVO.getOrganizationNo());
            OrganizationBasicInfo organizationBasicInfo = organizationBasicInfoMapper.selectOne(
                    Wrappers.<OrganizationBasicInfo>lambdaQuery().eq(OrganizationBasicInfo::getOrganizationNo, groupCountVO.getOrganizationNo()));
            if (organizationBasicInfo == null) {
                log.warn("[机构SMS费用计算]机构信息不存在,直接跳过,机构号:{}", groupCountVO.getOrganizationNo());
                continue;
            }
            this.calculateAndSaveSMSFee(organizationBasicInfo, fxRateCache, groupCountVO.getNum(), yearMonth);
        }
    }

    /**
     * 计算并保存SMS手续费
     *
     * @param organizationBasicInfo
     */
    public void calculateAndSaveSMSFee(OrganizationBasicInfo organizationBasicInfo, Map<String, BigDecimal> fxRateCache, long num, YearMonth yearMonth) {
        try {
            log.info("[机构SMS费用计算]开始计算手续费,机构号:{}", organizationBasicInfo.getOrganizationNo());
            String organizationNo = organizationBasicInfo.getOrganizationNo();
            OrganizationFeeTypeEnum feeTypeEnum = OrganizationFeeTypeEnum.SMS_FEE;
            BigDecimal deductFeeAmount = BigDecimal.ZERO;
            // 获取费率配置
            OrganizationFeeConfig organizationFeeConfig = organizationFeeConfigBizService.getValidOrganizationFeeConfigByWhere(organizationNo, null);
            if (organizationFeeConfig == null) {
                log.warn("[机构SMS费用计算]未找到机构费率配置信息,手续费按0处理,机构号:{}", organizationNo);
            } else {
                LambdaQueryWrapper<OrganizationFeeTemplateDetail> wrapper = Wrappers.<OrganizationFeeTemplateDetail>lambdaQuery()
                        .eq(OrganizationFeeTemplateDetail::getTemplateNo, organizationFeeConfig.getTemplateNo())
                        .eq(OrganizationFeeTemplateDetail::getFeeType, feeTypeEnum.getValue())
                        .eq(OrganizationFeeTemplateDetail::getCollectionMethod, OrganizationFeeCollectionMethodEnum.MONTHLY_SETTLEMENT.getValue())
                        .eq(OrganizationFeeTemplateDetail::getBillingDimension, OrganizationFeeBillingDimensionEnum.SINGLE_AMOUNT.getValue());
                OrganizationFeeTemplateDetail organizationFeeTemplateDetail = organizationFeeTemplateDetailMapper.selectOne(wrapper);
                if (organizationFeeTemplateDetail == null) {
                    log.warn("[机构SMS费用计算]未找到机构费率配置明细,手续费按0处理,机构号:{},手续费收取方式:{},手续费计费维度:{}", organizationNo,
                            OrganizationFeeCollectionMethodEnum.MONTHLY_SETTLEMENT.getValue(), OrganizationFeeBillingDimensionEnum.SINGLE_AMOUNT.getValue());
                } else {
                    BigDecimal fxRate;
                    // 获取汇率信息
                    if (fxRateCache.containsKey(organizationFeeTemplateDetail.getCurrencyCode() + "-" + organizationBasicInfo.getPoolCurrencyCode())) {
                        fxRate = fxRateCache.get(organizationFeeTemplateDetail.getCurrencyCode() + "-" + organizationBasicInfo.getPoolCurrencyCode());
                    } else {
                        fxRate = exchangeRateBizService.getCurrencyExchangeRate(organizationFeeTemplateDetail.getCurrencyCode(), organizationBasicInfo.getPoolCurrencyCode(), BigDecimal.ZERO, organizationBasicInfo);
                        fxRateCache.put(organizationFeeTemplateDetail.getCurrencyCode() + "-" + organizationBasicInfo.getPoolCurrencyCode(), fxRate);
                    }
                    // SMS一定是单笔金额、月结、收取固定值
                    BigDecimal proportionRate = organizationFeeTemplateDetail.getProportionRate() != null ? organizationFeeTemplateDetail.getProportionRate() : BigDecimal.ZERO;
                    BigDecimal fixedAmount = organizationFeeTemplateDetail.getFixedAmount() != null ? organizationFeeTemplateDetail.getFixedAmount() : BigDecimal.ZERO;
                    Integer poolCurrencyPrecision = OrganizationPoolCurrencyCodeEnum.getPrecisionByValue(organizationBasicInfo.getPoolCurrencyCode());
                    BigDecimal feeAmount = fixedAmount.multiply(new BigDecimal(num));
                    deductFeeAmount = feeAmount.multiply(fxRate).setScale(poolCurrencyPrecision, RoundingMode.UP);
                    if (deductFeeAmount.compareTo(BigDecimal.ZERO) > 0) {
                        log.info("[机构SMS费用计算]保存手续费记录");
                        // 保存费用明细记录
                        LocalDateTime now = LocalDateTime.now().withNano(0);
                        OrganizationFeeDetail organizationFeeDetail = new OrganizationFeeDetail();
                        organizationFeeDetail.setOrganizationNo(organizationNo);
                        organizationFeeDetail.setCardProductCode(null);
                        organizationFeeDetail.setRelatedTransactionId(null);
                        organizationFeeDetail.setCalculateDatetime(now);
                        // 此处交易日期使用上个月月底(分表键)
                        organizationFeeDetail.setTransactionDatetime(yearMonth.atEndOfMonth().atTime(23, 59, 59));
                        organizationFeeDetail.setFeeType(organizationFeeTemplateDetail.getFeeType());
                        organizationFeeDetail.setFeeCollectionMethod(organizationFeeTemplateDetail.getCollectionMethod());
                        organizationFeeDetail.setTransactionAmount(BigDecimal.ZERO);
                        organizationFeeDetail.setTransactionCurrencyCode(organizationFeeTemplateDetail.getCurrencyCode());
                        organizationFeeDetail.setTransactionCurrencyPrecision(DigitalCurrencyEnum.contains(organizationFeeTemplateDetail.getCurrencyCode()) ? 6 : 2);
                        organizationFeeDetail.setFeeAmount(feeAmount);
                        organizationFeeDetail.setSnapshotBillingDimension(organizationFeeTemplateDetail.getBillingDimension());
                        organizationFeeDetail.setSnapshotMinAmount(organizationFeeTemplateDetail.getMinAmount());
                        organizationFeeDetail.setSnapshotMaxAmount(organizationFeeTemplateDetail.getMaxAmount());
                        organizationFeeDetail.setSnapshotProportionRate(proportionRate);
                        organizationFeeDetail.setSnapshotProportionMinAmount(organizationFeeTemplateDetail.getProportionMinAmount());
                        organizationFeeDetail.setSnapshotProportionMaxAmount(organizationFeeTemplateDetail.getProportionMaxAmount());
                        organizationFeeDetail.setSnapshotFixedAmount(fixedAmount);
                        organizationFeeDetail.setFxRate(fxRate);
                        // 月结都为未收
                        organizationFeeDetail.setFeeCollectionStatus(OrganizationFeeCollectionStatusEnum.NOT_COLLECTED.getValue());
                        if (DigitalCurrencyEnum.contains(organizationBasicInfo.getPoolCurrencyCode())) {
                            // 扣款币种是数币
                            organizationFeeDetail.setDeductProcessor(DeductProcessorEnum.KUN.getValue());
                        } else {
                            // 扣款币种是法币
                            organizationFeeDetail.setDeductProcessor(DeductProcessorEnum.PAYX.getValue());
                        }
                        organizationFeeDetail.setDeductFeeAmount(deductFeeAmount);
                        organizationFeeDetail.setDeductCurrencyCode(organizationBasicInfo.getPoolCurrencyCode());
                        organizationFeeDetail.setDeductCurrencyPrecision(poolCurrencyPrecision);
                        organizationFeeDetail.setCallCount(0);
                        organizationFeeDetail.setCreateTime(now);
                        organizationFeeDetail.setLastModifyTime(now);
                        organizationFeeDetailMapper.insert(organizationFeeDetail);
                        log.info("[机构SMS费用计算]生成费用明细记录成功,记录id:{}", organizationFeeDetail.getId());
                    } else {
                        log.info("[机构SMS费用计算]费用为0,直接跳过");
                    }
                }
                log.info("[机构SMS费用计算]手续费计算完成,机构号:{}, 最终手续费为:{}, 币种:{}",
                        organizationBasicInfo.getOrganizationNo(), deductFeeAmount, organizationBasicInfo.getPoolCurrencyCode());
            }
        } catch (Exception e) {
            log.error("[机构SMS费用计算]手续费计算异常", e);
        }
    }
}

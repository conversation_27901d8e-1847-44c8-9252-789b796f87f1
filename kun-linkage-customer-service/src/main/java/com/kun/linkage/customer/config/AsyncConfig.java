package com.kun.linkage.customer.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.PreDestroy;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 异步调用配置类
 * 
 * 提供专门的外部API异步调用线程池配置
 * 主要特性：
 * 1. 独立的线程池，避免影响其他异步任务
 * 2. 合理的线程池参数配置
 * 3. 优雅的拒绝策略
 * 4. 线程池监控和清理
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncConfig implements DisposableBean {

    // 保存线程池引用，用于安全销毁
    private ThreadPoolTaskExecutor externalApiAsyncExecutor;

    /**
     * 外部API异步调用线程池
     * 
     * @return 线程池执行器
     */
    @Bean("externalApiAsyncExecutor")
    public Executor externalApiAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数：根据CPU核心数配置
        int corePoolSize = Math.max(2, Runtime.getRuntime().availableProcessors());
        executor.setCorePoolSize(corePoolSize);
        
        // 最大线程数：核心线程数的2倍，但不超过20
        int maxPoolSize = Math.min(corePoolSize * 2, 20);
        executor.setMaxPoolSize(maxPoolSize);
        
        // 队列容量：根据业务需求配置，避免队列过大导致内存问题
        executor.setQueueCapacity(100);
        
        // 线程名前缀：便于监控和调试
        executor.setThreadNamePrefix("ExternalAPI-");
        
        // 线程空闲时间：超过核心线程数的线程在空闲时间后会被回收
        executor.setKeepAliveSeconds(60);
        
        // 拒绝策略：调用者运行策略，避免任务丢失
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间：最多等待30秒
        executor.setAwaitTerminationSeconds(30);
        
        // 允许核心线程超时：允许核心线程在空闲时被回收
        executor.setAllowCoreThreadTimeOut(true);
        
        // 初始化线程池
        executor.initialize();
        
        // 保存线程池引用，用于安全销毁
        this.externalApiAsyncExecutor = executor;
        
        log.info("外部API异步调用线程池初始化完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}", 
                corePoolSize, maxPoolSize, executor.getQueueCapacity());
        
        return executor;
    }

    /**
     * 获取线程池状态信息
     * 
     * @return 线程池状态描述
     */
    public String getThreadPoolStatus() {
        if (externalApiAsyncExecutor == null) {
            return "线程池未初始化";
        }
        
        ThreadPoolExecutor threadPoolExecutor = externalApiAsyncExecutor.getThreadPoolExecutor();
        if (threadPoolExecutor == null) {
            return "线程池执行器为空";
        }
        
        return String.format(
            "线程池状态 - 核心线程数: %d, 最大线程数: %d, 当前线程数: %d, 活跃线程数: %d, " +
            "队列大小: %d, 已完成任务数: %d, 总任务数: %d",
            threadPoolExecutor.getCorePoolSize(),
            threadPoolExecutor.getMaximumPoolSize(),
            threadPoolExecutor.getPoolSize(),
            threadPoolExecutor.getActiveCount(),
            threadPoolExecutor.getQueue().size(),
            threadPoolExecutor.getCompletedTaskCount(),
            threadPoolExecutor.getTaskCount()
        );
    }

    /**
     * 检查线程池是否健康
     * 
     * @return true表示健康，false表示异常
     */
    public boolean isThreadPoolHealthy() {
        if (externalApiAsyncExecutor == null) {
            return false;
        }
        
        ThreadPoolExecutor threadPoolExecutor = externalApiAsyncExecutor.getThreadPoolExecutor();
        if (threadPoolExecutor == null) {
            return false;
        }
        
        // 检查线程池是否已关闭
        if (threadPoolExecutor.isShutdown() || threadPoolExecutor.isTerminated()) {
            return false;
        }
        
        // 检查队列是否过满（超过80%容量）
        int queueCapacity = externalApiAsyncExecutor.getQueueCapacity();
        int currentQueueSize = threadPoolExecutor.getQueue().size();
        if (currentQueueSize > queueCapacity * 0.8) {
            log.warn("线程池队列使用率过高: {}/{} ({}%)", 
                    currentQueueSize, queueCapacity, 
                    (currentQueueSize * 100 / queueCapacity));
            return false;
        }
        
        return true;
    }

    /**
     * 销毁线程池
     * 
     * 实现DisposableBean接口，确保在Spring容器关闭时正确销毁线程池
     */
    @Override
    public void destroy() throws Exception {
        if (externalApiAsyncExecutor != null) {
            log.info("开始销毁外部API异步调用线程池...");
            
            try {
                // 获取底层的ThreadPoolExecutor
                ThreadPoolExecutor threadPoolExecutor = externalApiAsyncExecutor.getThreadPoolExecutor();
                
                if (threadPoolExecutor != null && !threadPoolExecutor.isShutdown()) {
                    // 记录当前状态
                    log.info("销毁前线程池状态: {}", getThreadPoolStatus());
                    
                    // 优雅关闭：不再接受新任务，但会执行已提交的任务
                    threadPoolExecutor.shutdown();
                    
                    // 等待任务完成，最多等待30秒
                    boolean terminated = threadPoolExecutor.awaitTermination(30, TimeUnit.SECONDS);
                    
                    if (!terminated) {
                        log.warn("线程池在30秒内未能完全关闭，强制关闭...");
                        // 强制关闭：中断正在执行的任务
                        threadPoolExecutor.shutdownNow();
                        
                        // 再等待10秒
                        terminated = threadPoolExecutor.awaitTermination(10, TimeUnit.SECONDS);
                        
                        if (!terminated) {
                            log.error("线程池强制关闭后仍未完全终止");
                        } else {
                            log.info("线程池强制关闭成功");
                        }
                    } else {
                        log.info("线程池优雅关闭成功");
                    }
                } else {
                    log.info("线程池已经关闭，无需重复销毁");
                }
                
            } catch (InterruptedException e) {
                log.error("销毁外部API异步调用线程池时被中断", e);
                Thread.currentThread().interrupt();
                // 强制关闭
                externalApiAsyncExecutor.getThreadPoolExecutor().shutdownNow();
            } catch (Exception e) {
                log.error("销毁外部API异步调用线程池时发生异常", e);
                // 强制关闭
                try {
                    externalApiAsyncExecutor.getThreadPoolExecutor().shutdownNow();
                } catch (Exception ex) {
                    log.error("强制关闭外部API异步调用线程池失败", ex);
                }
            }
        } else {
            log.warn("外部API异步调用线程池未初始化，无需销毁");
        }
    }

    /**
     * 预销毁方法（备用方案）
     * 
     * 如果DisposableBean接口失效，此方法作为备用销毁方案
     */
    @PreDestroy
    public void preDestroy() {
        try {
            destroy();
        } catch (Exception e) {
            log.error("预销毁外部API异步调用线程池失败", e);
        }
    }
}

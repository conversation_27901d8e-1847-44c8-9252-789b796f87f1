package com.kun.linkage.customer.service.kyc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kun.linkage.common.db.entity.CustomerKycLevel3Info;
import com.kun.linkage.common.db.entity.CustomerKycLevel3Record;
import com.kun.linkage.common.db.mapper.CustomerKycLevel3InfoMapper;
import com.kun.linkage.common.db.mapper.CustomerKycLevel3RecordMapper;
import com.kun.linkage.customer.facade.api.bean.req.kyc.CustomerKycLevel3Req;
import com.kun.linkage.customer.service.FileUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class CustomerKycLevel3Service {

    @Resource
    private CustomerKycLevel3RecordMapper customerKycLevel3RecordMapper;
    @Resource
    private FileUploadService fileUploadService;
    @Resource
    private CustomerKycLevel3InfoMapper customerKycLevel3InfoMapper;

    /**
     * 新增用户三级审核
     * @param customerKycLevel3
     * @param caseNo
     * @param organizationNo
     * @param customerId
     */
    public Integer saveCustomerKycLevel3Record(CustomerKycLevel3Req customerKycLevel3, String caseNo,
                                               String organizationNo, String customerId,Date date) {
        //资产证明图片数据处理
        List<String> assetProofUploadIds = customerKycLevel3.getAssetProofUploadIds();
        String updateAssetProofUploadIds = null;
        String assetProof = null;
        if(null != assetProofUploadIds && !assetProofUploadIds.isEmpty()){
            // 如果列表大小大于3，则只取前3个元素
            if (assetProofUploadIds.size() > 3) {
                assetProofUploadIds = assetProofUploadIds.subList(0, 3);
            }

            updateAssetProofUploadIds = String.join(",", assetProofUploadIds);

            List<String> AssetProofUploadList = fileUploadService.selectStoragePathByFileId(customerId, organizationNo, assetProofUploadIds);
            if(null != AssetProofUploadList && !AssetProofUploadList.isEmpty()){
                assetProof = String.join(",", AssetProofUploadList);
            }
        }

        //资产证明文件
        List<String> assetProofDocumentUploadIds = customerKycLevel3.getAssetProofDocumentUploadIds();
        String updateAssetProofDocumentUploadIds = null;
        String assetProofDocument = null;
        if(null != assetProofDocumentUploadIds && !assetProofDocumentUploadIds.isEmpty()){

            // 如果列表大小大于3，则只取前3个元素
            if (assetProofDocumentUploadIds.size() > 3) {
                assetProofDocumentUploadIds = assetProofDocumentUploadIds.subList(0, 3);
            }

            updateAssetProofUploadIds = String.join(",", assetProofDocumentUploadIds);
            List<String> assetProofDocumentList = fileUploadService.selectStoragePathByFileId(customerId, organizationNo, assetProofDocumentUploadIds);
            if(null != assetProofDocumentList && !assetProofDocumentList.isEmpty()){
                assetProofDocument = String.join(",", assetProofDocumentList);
            }
        }

        //数币资金来源证明文件
        List<String> cryptocurrencyFundingSourceDocumentUploadIds = customerKycLevel3.getCryptocurrencyFundingSourceDocumentUploadIds();
        String updateCryptocurrencyFundingSourceDocumentUploadIds = null;
        String cryptocurrencyFundingSourceDocument = null;
        if(null != cryptocurrencyFundingSourceDocumentUploadIds && !cryptocurrencyFundingSourceDocumentUploadIds.isEmpty()){

            // 如果列表大小大于3，则只取前3个元素
            if (cryptocurrencyFundingSourceDocumentUploadIds.size() > 3) {
                cryptocurrencyFundingSourceDocumentUploadIds = cryptocurrencyFundingSourceDocumentUploadIds.subList(0, 3);
            }

            updateCryptocurrencyFundingSourceDocumentUploadIds = String.join(",", cryptocurrencyFundingSourceDocumentUploadIds);
            List<String> sourceDocumentList = fileUploadService.selectStoragePathByFileId(customerId, organizationNo, assetProofDocumentUploadIds);
            if(null != sourceDocumentList && !sourceDocumentList.isEmpty()){
                cryptocurrencyFundingSourceDocument = String.join(",", sourceDocumentList);
            }
        }

        //数币资金来源;枚举集合 处理
        List<String> cryptocurrencyFundingSourceList = customerKycLevel3.getCryptocurrencyFundingSourceList();
        String  cryptocurrencyFundingSource = null;
        if(null != cryptocurrencyFundingSourceList && !cryptocurrencyFundingSourceList.isEmpty()){
            cryptocurrencyFundingSource = String.join(",", cryptocurrencyFundingSourceList);
        }

        CustomerKycLevel3Record customerKycLevel3Record = new CustomerKycLevel3Record();
        customerKycLevel3Record.setCustomerId(customerId);
        customerKycLevel3Record.setOrganizationNo(organizationNo);
        customerKycLevel3Record.setCaseNo(caseNo);
        customerKycLevel3Record.setEmploymentStatus(customerKycLevel3.getEmploymentStatus());
        customerKycLevel3Record.setCompanyName(customerKycLevel3.getCompanyName());
        customerKycLevel3Record.setIndustry(customerKycLevel3.getIndustry());
        customerKycLevel3Record.setPosition(customerKycLevel3.getPosition());
        customerKycLevel3Record.setYearsOfExperience(customerKycLevel3.getYearsOfExperience());
        customerKycLevel3Record.setAnnualIncome(customerKycLevel3.getAnnualIncome());
        customerKycLevel3Record.setAssetProofUploadIds(updateAssetProofUploadIds);
        customerKycLevel3Record.setAssetProof(assetProof);
        customerKycLevel3Record.setAssetProofOtherNotes(customerKycLevel3.getAssetProofOtherNotes());
        customerKycLevel3Record.setAssetProofDocumentUploadIds(updateAssetProofDocumentUploadIds);
        customerKycLevel3Record.setAssetProofDocument(assetProofDocument);
        customerKycLevel3Record.setCryptocurrencyFundingSource(cryptocurrencyFundingSource);
        customerKycLevel3Record.setCryptocurrencyFundingSourceOtherNotes(customerKycLevel3.getCryptocurrencyFundingSourceOtherNotes());
        customerKycLevel3Record.setCryptocurrencyFundingSourceDocumentUploadIds(updateCryptocurrencyFundingSourceDocumentUploadIds);
        customerKycLevel3Record.setCryptocurrencyFundingSourceDocument(cryptocurrencyFundingSourceDocument);
        customerKycLevel3Record.setCreateTime(date);
        customerKycLevel3Record.setUpdateTime(date);

       return customerKycLevel3RecordMapper.insert(customerKycLevel3Record);
    }

    /**
     * 新增或者更新用户三级级信息
     * @param caseNo 案件号
     * @param organizationNo 机构号
     * @param customerId 客户号
     */
    public Boolean saveOrUpdateCustomerKycLevel3Info(String caseNo, String organizationNo, String customerId) {
        CustomerKycLevel3Record customerKycLevel3Record = selectKycLevel3ByCase(caseNo);
        if(null == customerKycLevel3Record){
            log.info("新增或者更新KYC三级级信息失败;根据案件号没有查询到三级审核信息;caseNo:{}",caseNo);
            return false;
        }

        CustomerKycLevel3Info customerKycLevel3Info = this.selectCustomerKycLevel3InfoByOrgNoAndCustomerId(organizationNo, customerId);
        if(null == customerKycLevel3Info){
            //新增
            customerKycLevel3Info = new CustomerKycLevel3Info();
            BeanUtils.copyProperties(customerKycLevel3Record, customerKycLevel3Info);
            customerKycLevel3Info.setSubmissionTime(customerKycLevel3Info.getSubmissionTime());
            return customerKycLevel3InfoMapper.insert(customerKycLevel3Info) > 0;
        }else {
            //更新数据
            CustomerKycLevel3Info updateEntity = new CustomerKycLevel3Info();
            BeanUtils.copyProperties(customerKycLevel3Info,updateEntity);
            updateEntity.setKycLevel3Id(customerKycLevel3Info.getKycLevel3Id());
            updateEntity.setSubmissionTime(customerKycLevel3Info.getSubmissionTime());
            updateEntity.setUpdateTime(new Date());
            return customerKycLevel3InfoMapper.updateById(updateEntity) > 0;
        }
    }

    /**
     * 根据机构号和客户号
     * @param organizationNo 机构号
     * @param customerId 客户号
     * @return
     */
    public CustomerKycLevel3Info selectCustomerKycLevel3InfoByOrgNoAndCustomerId(String organizationNo,String customerId){
        LambdaQueryWrapper<CustomerKycLevel3Info> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerKycLevel3Info::getOrganizationNo, organizationNo)
                .eq(CustomerKycLevel3Info::getCustomerId, customerId);
        return customerKycLevel3InfoMapper.selectOne(queryWrapper);
    }
    
    
    /**
     * 根据案件号查询3级信息
     * @param caseNo 案件号
     */
    public CustomerKycLevel3Record selectKycLevel3ByCase(String caseNo) {
        LambdaQueryWrapper<CustomerKycLevel3Record> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerKycLevel3Record::getCaseNo, caseNo);
        return customerKycLevel3RecordMapper.selectOne(queryWrapper);
    }

}

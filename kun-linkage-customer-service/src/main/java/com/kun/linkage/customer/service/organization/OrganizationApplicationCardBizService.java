package com.kun.linkage.customer.service.organization;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.linkage.boss.support.vo.VccBossUserVO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.dto.ReviewDTO;
import com.kun.linkage.common.base.enums.OperationTypeEnum;
import com.kun.linkage.common.base.enums.ReviewStatusEnum;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.OrganizationApplicationCard;
import com.kun.linkage.common.db.entity.OrganizationApplicationCardReviewRecord;
import com.kun.linkage.common.db.entity.OrganizationBasicInfo;
import com.kun.linkage.common.db.mapper.OrganizationApplicationCardMapper;
import com.kun.linkage.common.db.mapper.OrganizationApplicationCardReviewRecordMapper;
import com.kun.linkage.common.db.mapper.OrganizationBasicInfoMapper;
import com.kun.linkage.customer.ext.mapper.OrganizationApplicationCardExtMapper;
import com.kun.linkage.customer.facade.dto.organization.applicationcard.OrganizationApplicationCardAddSubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.applicationcard.OrganizationApplicationCardModifySubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.applicationcard.OrganizationApplicationCardPageQueryDTO;
import com.kun.linkage.customer.facade.dto.organization.applicationcard.OrganizationApplicationCardReviewRecordPageQueryDTO;
import com.kun.linkage.customer.facade.vo.organization.OrganizationApplicationCardReviewRecordVO;
import com.kun.linkage.customer.facade.vo.organization.OrganizationApplicationCardVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 机构卡产品信息服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
@Service
public class OrganizationApplicationCardBizService {
    private static final Logger log = LoggerFactory.getLogger(OrganizationApplicationCardBizService.class);
    @Resource
    private OrganizationBasicInfoMapper organizationBasicInfoMapper;
    @Resource
    private OrganizationApplicationCardMapper organizationApplicationCardMapper;
    @Resource
    private OrganizationApplicationCardReviewRecordMapper organizationApplicationCardReviewRecordMapper;
    @Resource
    private OrganizationApplicationCardExtMapper organizationApplicationCardExtMapper;

    /**
     * 分页查询机构卡产品信息
     *
     * @param dto
     * @return
     */
    public PageResult<OrganizationApplicationCardVO> pageList(OrganizationApplicationCardPageQueryDTO dto) {
        return PageHelperUtil.getPage(dto, () -> organizationApplicationCardExtMapper.listOrganizationApplicationCardByWhere(dto));
    }

    /**
     * 根据机构号获取其所属的卡产品信息
     *
     * @param organizationNo
     * @return
     */
    public List<OrganizationApplicationCardVO> getValidListByOrganizationNo(String organizationNo) {
        List<OrganizationApplicationCard> organizationApplicationCardList =
                organizationApplicationCardMapper.selectList(Wrappers.<OrganizationApplicationCard>lambdaQuery()
                        .eq(OrganizationApplicationCard::getOrganizationNo, organizationNo)
                        .eq(OrganizationApplicationCard::getStatus, ValidStatusEnum.VALID.getValue()));
        List<OrganizationApplicationCardVO> organizationApplicationCardVOList = new ArrayList<>();
        if (organizationApplicationCardList != null && !organizationApplicationCardList.isEmpty()) {
            organizationApplicationCardList.forEach(organizationApplicationCard -> {
                OrganizationApplicationCardVO organizationApplicationCardVO = new OrganizationApplicationCardVO();
                BeanUtil.copyProperties(organizationApplicationCard, organizationApplicationCardVO);
                organizationApplicationCardVOList.add(organizationApplicationCardVO);
            });
        }
        return organizationApplicationCardVOList;
    }

    /**
     * 提交新增机构卡产品信息
     *
     * @param dto
     * @return
     */
    public Result<Void> addSubmit(OrganizationApplicationCardAddSubmitDTO dto, VccBossUserVO bossOperator) {
        // 校验机构是否存在
        Long num = organizationBasicInfoMapper.selectCount(Wrappers.<OrganizationBasicInfo>lambdaQuery()
                .eq(OrganizationBasicInfo::getOrganizationNo, dto.getOrganizationNo()));
        if (num == null || num <= 0) {
            log.error("机构信息不存在, 机构号:{}", dto.getOrganizationNo());
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }
        // 校验机构号+卡产品是否已存在
        num = organizationApplicationCardMapper.selectCount(Wrappers.<OrganizationApplicationCard>lambdaQuery()
                .eq(OrganizationApplicationCard::getOrganizationNo, dto.getOrganizationNo())
                .eq(OrganizationApplicationCard::getCardProductCode, dto.getCardProductCode()));
        if (num != null && num > 0) {
            log.error("机构卡产品信息已存在, 机构号:{}, 卡产品编号:{}", dto.getOrganizationNo(), dto.getCardProductCode());
            return Result.fail(CommonTipConstant.DATA_ALREADY_EXIST);
        }
        // 校验机构号+卡产品是否存在待审核数据
        num = organizationApplicationCardReviewRecordMapper.selectCount(Wrappers.<OrganizationApplicationCardReviewRecord>lambdaQuery()
                .eq(OrganizationApplicationCardReviewRecord::getOrganizationNo, dto.getOrganizationNo())
                .eq(OrganizationApplicationCardReviewRecord::getCardProductCode, dto.getCardProductCode())
                .eq(OrganizationApplicationCardReviewRecord::getReviewStatus, ReviewStatusEnum.PENDING.getValue()));
        if (num != null && num > 0) {
            log.error("已存在待审核的数据, 机构号:{}, 卡产品编号:{}", dto.getOrganizationNo(), dto.getCardProductCode());
            return Result.fail(CommonTipConstant.ALREADY_EXIST_PENDING_DATA);
        }

        OrganizationApplicationCardReviewRecord organizationApplicationCardReviewRecord = new OrganizationApplicationCardReviewRecord();
        BeanUtil.copyProperties(dto, organizationApplicationCardReviewRecord);
        organizationApplicationCardReviewRecord.setOperatorType(OperationTypeEnum.ADD.getValue());
        organizationApplicationCardReviewRecord.setReviewStatus(ReviewStatusEnum.PENDING.getValue());
        organizationApplicationCardReviewRecord.setSubmitTime(LocalDateTime.now());
        organizationApplicationCardReviewRecord.setSubmitUserId(String.valueOf(bossOperator.getId()));
        organizationApplicationCardReviewRecord.setSubmitUserName(bossOperator.getUsername());
        // 新增到审核记录中,审核通过才会到正式表
        organizationApplicationCardReviewRecordMapper.insert(organizationApplicationCardReviewRecord);
        return Result.success();
    }

    /**
     * 提交修改机构卡产品信息
     *
     * @param dto
     * @return
     */
    public Result<Void> modifySubmit(OrganizationApplicationCardModifySubmitDTO dto, VccBossUserVO bossOperator) {
        // 校验待修改的数据是否存在
        OrganizationApplicationCard organizationApplicationCard = organizationApplicationCardMapper.selectById(dto.getOrganizationApplicationCardId());
        if (organizationApplicationCard == null) {
            log.error("机构卡产品信息不存在, 机构卡产品信息id:{}", dto.getOrganizationApplicationCardId());
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }
        if (!StringUtils.equals(organizationApplicationCard.getCardProductCode(), dto.getCardProductCode())) {
            // 如果修改后的卡产品和修改前的不一样，则需要校验校验机构号+卡产品是否已存在
            Long num = organizationApplicationCardMapper.selectCount(Wrappers.<OrganizationApplicationCard>lambdaQuery()
                    .eq(OrganizationApplicationCard::getOrganizationNo, organizationApplicationCard.getOrganizationNo())
                    .eq(OrganizationApplicationCard::getCardProductCode, dto.getCardProductCode()));
            if (num != null && num > 0) {
                log.error("机构卡产品信息已存在, 机构号:{}, 卡产品编号:{}", organizationApplicationCard.getOrganizationNo(), dto.getCardProductCode());
                return Result.fail(CommonTipConstant.DATA_ALREADY_EXIST);
            }
        }
        // 校验机构号+卡产品是否存在待审核数据
        Long num = organizationApplicationCardReviewRecordMapper.selectCount(Wrappers.<OrganizationApplicationCardReviewRecord>lambdaQuery()
                .eq(OrganizationApplicationCardReviewRecord::getOrganizationNo, organizationApplicationCard.getOrganizationNo())
                .eq(OrganizationApplicationCardReviewRecord::getCardProductCode, dto.getCardProductCode())
                .eq(OrganizationApplicationCardReviewRecord::getReviewStatus, ReviewStatusEnum.PENDING.getValue()));
        if (num != null && num > 0) {
            log.error("已存在待审核的数据, 机构号:{}, 卡产品编号:{}", organizationApplicationCard.getOrganizationNo(), dto.getCardProductCode());
            return Result.fail(CommonTipConstant.ALREADY_EXIST_PENDING_DATA);
        }

        OrganizationApplicationCardReviewRecord organizationApplicationCardReviewRecord = new OrganizationApplicationCardReviewRecord();
        BeanUtil.copyProperties(dto, organizationApplicationCardReviewRecord);
        organizationApplicationCardReviewRecord.setOrganizationNo(organizationApplicationCard.getOrganizationNo());
        organizationApplicationCardReviewRecord.setOperatorType(OperationTypeEnum.MODIFY.getValue());
        organizationApplicationCardReviewRecord.setReviewStatus(ReviewStatusEnum.PENDING.getValue());
        organizationApplicationCardReviewRecord.setSubmitTime(LocalDateTime.now());
        organizationApplicationCardReviewRecord.setSubmitUserId(String.valueOf(bossOperator.getId()));
        organizationApplicationCardReviewRecord.setSubmitUserName(bossOperator.getUsername());
        // 新增到审核记录中,审核通过才会到正式表
        organizationApplicationCardReviewRecordMapper.insert(organizationApplicationCardReviewRecord);
        return Result.success();
    }

    /**
     * 审核机构卡产品信息
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> review(ReviewDTO dto, VccBossUserVO bossOperator) {
        OrganizationApplicationCardReviewRecord organizationApplicationCardReviewRecord =
                organizationApplicationCardReviewRecordMapper.selectOne(
                        Wrappers.<OrganizationApplicationCardReviewRecord>lambdaQuery()
                                .eq(OrganizationApplicationCardReviewRecord::getReviewId, dto.getReviewId())
                                .eq(OrganizationApplicationCardReviewRecord::getReviewStatus, ReviewStatusEnum.PENDING.getValue()));
        if (organizationApplicationCardReviewRecord == null) {
            log.error("机构卡产品审核记录不存在, reviewId:{}", dto.getReviewId());
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }
        LocalDateTime now = LocalDateTime.now();
        OrganizationApplicationCardReviewRecord modifyRecord = new OrganizationApplicationCardReviewRecord();
        modifyRecord.setReviewId(Long.parseLong(dto.getReviewId()));
        modifyRecord.setReviewStatus(dto.getReviewStatus());
        if (StringUtils.isNotBlank(dto.getReviewReason())) {
            modifyRecord.setReviewReason(dto.getReviewReason());
        }
        modifyRecord.setReviewTime(now);
        modifyRecord.setReviewUserId(String.valueOf(bossOperator.getId()));
        modifyRecord.setReviewUserName(bossOperator.getUsername());
        if (StringUtils.equals(dto.getReviewStatus(), ReviewStatusEnum.REJECT.getValue())) {
            organizationApplicationCardReviewRecordMapper.updateById(modifyRecord);
        } else if (StringUtils.equals(dto.getReviewStatus(), ReviewStatusEnum.PASS.getValue())) {
            if (StringUtils.equals(OperationTypeEnum.ADD.getValue(), organizationApplicationCardReviewRecord.getOperatorType())) {
                // 新增
                OrganizationApplicationCard organizationApplicationCard = new OrganizationApplicationCard();
                BeanUtil.copyProperties(organizationApplicationCardReviewRecord, organizationApplicationCard);
                organizationApplicationCard.setCreateTime(now);
                organizationApplicationCard.setCreateUserId(String.valueOf(bossOperator.getId()));
                organizationApplicationCard.setCreateUserName(bossOperator.getUsername());
                organizationApplicationCard.setLastModifyTime(now);
                organizationApplicationCard.setLastModifyUserId(String.valueOf(bossOperator.getId()));
                organizationApplicationCard.setLastModifyUserName(bossOperator.getUsername());
                organizationApplicationCardMapper.insert(organizationApplicationCard);
            } else {
                // 修改
                OrganizationApplicationCard organizationApplicationCard = new OrganizationApplicationCard();
                BeanUtil.copyProperties(organizationApplicationCardReviewRecord, organizationApplicationCard);
                organizationApplicationCard.setId(organizationApplicationCardReviewRecord.getOrganizationApplicationCardId());
                organizationApplicationCard.setLastModifyTime(now);
                organizationApplicationCard.setLastModifyUserId(String.valueOf(bossOperator.getId()));
                organizationApplicationCard.setLastModifyUserName(bossOperator.getUsername());
                organizationApplicationCardMapper.updateById(organizationApplicationCard);
            }
            organizationApplicationCardReviewRecordMapper.updateById(modifyRecord);
        } else {
            log.error("非法审核状态, 审核状态:{}", dto.getReviewStatus());
            return Result.fail(CommonTipConstant.ILLEGAL_REQUEST);
        }

        return Result.success();
    }


    /**
     * 分页查询机构卡产品审核信息
     *
     * @param dto
     * @return
     */
    public PageResult<OrganizationApplicationCardReviewRecordVO> reviewPageList(OrganizationApplicationCardReviewRecordPageQueryDTO dto) {
        return PageHelperUtil.getPage(dto, () -> organizationApplicationCardExtMapper.listOrganizationApplicationCardReviewRecordByWhere(dto));
    }
}

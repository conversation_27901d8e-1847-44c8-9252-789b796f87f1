package com.kun.linkage.customer.service.organization;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.linkage.boss.support.vo.VccBossUserVO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.dto.ReviewDTO;
import com.kun.linkage.common.base.enums.OperationTypeEnum;
import com.kun.linkage.common.base.enums.ReviewStatusEnum;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.base.utils.SensitiveInfoUtil;
import com.kun.linkage.common.db.entity.OrganizationBasicInfo;
import com.kun.linkage.common.db.entity.OrganizationBasicInfoReviewRecord;
import com.kun.linkage.common.db.mapper.OrganizationBasicInfoMapper;
import com.kun.linkage.common.db.mapper.OrganizationBasicInfoReviewRecordMapper;
import com.kun.linkage.common.db.vo.KLKycPushVO;
import com.kun.linkage.customer.ext.mapper.OrganizationBasicInfoExtMapper;
import com.kun.linkage.customer.facade.dto.organization.basic.OrganizationBasicInfoAddSubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.basic.OrganizationBasicInfoModifySubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.basic.OrganizationBasicInfoPageQueryDTO;
import com.kun.linkage.customer.facade.dto.organization.basic.OrganizationBasicInfoReviewRecordPageQueryDTO;
import com.kun.linkage.customer.facade.enums.OrganizationBusinessTypeEnum;
import com.kun.linkage.customer.facade.vo.organization.OrganizationBasicInfoReviewRecordVO;
import com.kun.linkage.customer.facade.vo.organization.OrganizationBasicInfoVO;
import com.kun.linkage.customer.service.TechnicalParamsBizService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <p>
 * 机构基本信息服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
@Service
public class OrganizationBasicInfoBizService {
    private static final Logger log = LoggerFactory.getLogger(OrganizationBasicInfoBizService.class);
    @Resource
    private OrganizationBasicInfoMapper organizationBasicInfoMapper;
    @Resource
    private OrganizationBasicInfoReviewRecordMapper organizationBasicInfoReviewRecordMapper;
    @Resource
    private OrganizationBasicInfoExtMapper organizationBasicInfoExtMapper;
    @Resource
    private TechnicalParamsBizService technicalParamsBizService;

    /**
     * 分页查询机构基本信息
     *
     * @param dto
     * @return
     */
    public PageResult<OrganizationBasicInfoVO> pageList(OrganizationBasicInfoPageQueryDTO dto) {
        return PageHelperUtil.getPage(dto, () -> organizationBasicInfoExtMapper.listOrganizationBasicInfoByWhere(dto));
    }

    /**
     * 查询所有机构信息
     *
     * @return
     */
    public List<OrganizationBasicInfoVO> allValidList() {
        OrganizationBasicInfoPageQueryDTO organizationBasicInfoPageQueryDTO = new OrganizationBasicInfoPageQueryDTO();
        organizationBasicInfoPageQueryDTO.setStatus(ValidStatusEnum.VALID.getValue());
        return organizationBasicInfoExtMapper.listOrganizationBasicInfoByWhere(organizationBasicInfoPageQueryDTO);
    }

    /**
     * 提交新增机构信息
     *
     * @param dto
     * @return
     */
    public Result<Void> addSubmit(OrganizationBasicInfoAddSubmitDTO dto, VccBossUserVO bossOperator) {
        OrganizationBasicInfoReviewRecord organizationBasicInfoReviewRecord = new OrganizationBasicInfoReviewRecord();
        BeanUtil.copyProperties(dto, organizationBasicInfoReviewRecord);
        organizationBasicInfoReviewRecord.setOperatorType(OperationTypeEnum.ADD.getValue());
        organizationBasicInfoReviewRecord.setReviewStatus(ReviewStatusEnum.PENDING.getValue());
        organizationBasicInfoReviewRecord.setSubmitTime(LocalDateTime.now());
        organizationBasicInfoReviewRecord.setSubmitUserId(String.valueOf(bossOperator.getId()));
        organizationBasicInfoReviewRecord.setSubmitUserName(bossOperator.getUsername());
        // 新增到审核记录中,审核通过才会到正式表
        organizationBasicInfoReviewRecordMapper.insert(organizationBasicInfoReviewRecord);
        return Result.success();
    }

    /**
     * 提交修改机构信息
     *
     * @param dto
     * @return
     */
    public Result<Void> modifySubmit(OrganizationBasicInfoModifySubmitDTO dto, VccBossUserVO bossOperator) {
        if (StringUtils.isBlank(dto.getKunMid()) && StringUtils.isBlank(dto.getPayxMid())) {
            log.error(",kunMid和payxMid不能同时为空");
            return Result.fail(CommonTipConstant.REQUEST_PARAM_ERROR);
        }
        // 校验数据是否存在
        OrganizationBasicInfo organizationBasicInfo = organizationBasicInfoMapper.selectById(dto.getOrganizationId());
        if (organizationBasicInfo == null) {
            log.error("机构信息不存在, 机构信息id:{}", dto.getOrganizationId());
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }
        // 校验同一个机构id是否存在待审核数据
        Long num = organizationBasicInfoReviewRecordMapper.selectCount(
                Wrappers.<OrganizationBasicInfoReviewRecord>lambdaQuery()
                        .eq(OrganizationBasicInfoReviewRecord::getOrganizationId, dto.getOrganizationId())
                        .eq(OrganizationBasicInfoReviewRecord::getReviewStatus, ReviewStatusEnum.PENDING.getValue()));
        if (num != null && num > 0) {
            log.error("已存在待审核的数据, 机构信息id:{}", dto.getOrganizationId());
            return Result.fail(CommonTipConstant.ALREADY_EXIST_PENDING_DATA);
        }
        OrganizationBasicInfoReviewRecord organizationBasicInfoReviewRecord = new OrganizationBasicInfoReviewRecord();
        BeanUtil.copyProperties(dto, organizationBasicInfoReviewRecord);
        organizationBasicInfoReviewRecord.setOrganizationNo(organizationBasicInfo.getOrganizationNo());
        organizationBasicInfoReviewRecord.setBusinessType(organizationBasicInfo.getBusinessType());
        organizationBasicInfoReviewRecord.setCountryCode(organizationBasicInfo.getCountryCode());
        organizationBasicInfoReviewRecord.setOperatorType(OperationTypeEnum.MODIFY.getValue());
        organizationBasicInfoReviewRecord.setReviewStatus(ReviewStatusEnum.PENDING.getValue());
        organizationBasicInfoReviewRecord.setSubmitTime(LocalDateTime.now());
        organizationBasicInfoReviewRecord.setSubmitUserId(String.valueOf(bossOperator.getId()));
        organizationBasicInfoReviewRecord.setSubmitUserName(bossOperator.getUsername());
        // 新增到审核记录中,审核通过才会到正式表
        organizationBasicInfoReviewRecordMapper.insert(organizationBasicInfoReviewRecord);
        return Result.success();
    }

    /**
     * 审核机构信息
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> review(ReviewDTO dto, VccBossUserVO bossOperator) {
        OrganizationBasicInfoReviewRecord organizationBasicInfoReviewRecord =
                organizationBasicInfoReviewRecordMapper.selectOne(
                        Wrappers.<OrganizationBasicInfoReviewRecord>lambdaQuery()
                                .eq(OrganizationBasicInfoReviewRecord::getReviewId, dto.getReviewId())
                                .eq(OrganizationBasicInfoReviewRecord::getReviewStatus, ReviewStatusEnum.PENDING.getValue()));
        if (organizationBasicInfoReviewRecord == null) {
            log.error("机构审核记录不存在, 审核记录Id:{}", dto.getReviewId());
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }
        LocalDateTime now = LocalDateTime.now();
        OrganizationBasicInfoReviewRecord modifyRecord = new OrganizationBasicInfoReviewRecord();
        modifyRecord.setReviewId(Long.parseLong(dto.getReviewId()));
        modifyRecord.setReviewStatus(dto.getReviewStatus());
        if (StringUtils.isNotBlank(dto.getReviewReason())) {
            modifyRecord.setReviewReason(dto.getReviewReason());
        }
        modifyRecord.setReviewTime(now);
        modifyRecord.setReviewUserId(String.valueOf(bossOperator.getId()));
        modifyRecord.setReviewUserName(bossOperator.getUsername());
        if (StringUtils.equals(dto.getReviewStatus(), ReviewStatusEnum.REJECT.getValue())) {
            organizationBasicInfoReviewRecordMapper.updateById(modifyRecord);
        } else if (StringUtils.equals(dto.getReviewStatus(), ReviewStatusEnum.PASS.getValue())) {
            if (StringUtils.equals(OperationTypeEnum.MODIFY.getValue(), organizationBasicInfoReviewRecord.getOperatorType())) {
                // 修改
                OrganizationBasicInfo organizationBasicInfo = new OrganizationBasicInfo();
                BeanUtil.copyProperties(organizationBasicInfoReviewRecord, organizationBasicInfo);
                organizationBasicInfo.setId(organizationBasicInfoReviewRecord.getOrganizationId());
                organizationBasicInfo.setLastModifyTime(now);
                organizationBasicInfo.setLastModifyUserId(String.valueOf(bossOperator.getId()));
                organizationBasicInfo.setLastModifyUserName(bossOperator.getUsername());
                organizationBasicInfoMapper.updateById(organizationBasicInfo);
            } else {
                log.error("非法操作状态, 操作状态:{}", organizationBasicInfoReviewRecord.getOperatorType());
                return Result.fail(CommonTipConstant.ILLEGAL_REQUEST);
            }
            organizationBasicInfoReviewRecordMapper.updateById(modifyRecord);
        } else {
            log.error("非法审核状态, 审核状态:{}", dto.getReviewStatus());
            return Result.fail(CommonTipConstant.ILLEGAL_REQUEST);
        }

        return Result.success();
    }


    /**
     * 分页查询机构审核信息
     *
     * @param dto
     * @return
     */
    public PageResult<OrganizationBasicInfoReviewRecordVO> reviewPageList(OrganizationBasicInfoReviewRecordPageQueryDTO dto) {
        return PageHelperUtil.getPage(dto, () -> organizationBasicInfoExtMapper.listOrganizationBasicInfoReviewRecordByWhere(dto));

    }

    public void syncCustomerInfo(List<KLKycPushVO> klKycPushVOS) {
        log.info("开始同步商户信息:{}", klKycPushVOS);
        // 查询已经同步过的数据
        List<OrganizationBasicInfo> organizationBasicInfos = organizationBasicInfoMapper.selectList(new LambdaQueryWrapper<>());
        // 如果机构号已经存在,则代表已同步过，则跳过
        List<KLKycPushVO> syncKlKycPushVOS = klKycPushVOS.stream()
                .filter(klKycPushVO -> organizationBasicInfos.stream()
                        .noneMatch(organizationBasicInfo ->
                                StringUtils.equals(organizationBasicInfo.getOrganizationNo(), klKycPushVO.getKunCustomerNo())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(syncKlKycPushVOS)) {
            log.info("没有需要同步的数据");
            return;
        }

        LocalDateTime dateTime = LocalDateTime.now();
        log.info("需要同步的数据:{}", syncKlKycPushVOS);
        syncKlKycPushVOS.forEach(klKycPushVO -> {
            try {
                OrganizationBasicInfo organizationBasicInfo = new OrganizationBasicInfo();
                organizationBasicInfo.setOrganizationNo(klKycPushVO.getKunCustomerNo());
                organizationBasicInfo.setOrganizationName(klKycPushVO.getCustomerName());
                organizationBasicInfo.setBusinessType(OrganizationBusinessTypeEnum.U_CARD.getValue());// 默认为U卡
                organizationBasicInfo.setCountryCode(klKycPushVO.getRegisterRegion());
                organizationBasicInfo.setStatus(ValidStatusEnum.VALID.getValue());// 默认有效
                organizationBasicInfo.setKey(UUID.randomUUID().toString().replace("-", ""));// 生成唯一主键
                // 生成敏感信息加密秘钥
                organizationBasicInfo.setSensitiveKey(SensitiveInfoUtil.generateBase64Key());
                organizationBasicInfo.setKunMid(klKycPushVO.getKunCustomerNo());
                organizationBasicInfo.setPayxMid(klKycPushVO.getAcsCustomerNo());
                organizationBasicInfo.setMode(null);
                organizationBasicInfo.setCheckOrganizationAccountFlag(1);
                organizationBasicInfo.setCheckCustomerAccountFlag(null);
                organizationBasicInfo.setThirdPartyAuthorizationFlag(null);
                organizationBasicInfo.setCreateTime(dateTime);
                organizationBasicInfo.setCreateUserId(klKycPushVO.getCustomerNo());
                organizationBasicInfo.setCreateUserName(klKycPushVO.getCustomerName());
                organizationBasicInfo.setLastModifyTime(dateTime);
                organizationBasicInfo.setLastModifyUserId(klKycPushVO.getCustomerNo());
                organizationBasicInfo.setLastModifyUserName(klKycPushVO.getCustomerName());
                log.info("新增商户信息:{}", organizationBasicInfo);
                organizationBasicInfoMapper.insert(organizationBasicInfo);
                log.info("新增商户信息成功");

                // 为新商户创建技术参数配置
                try {
                    technicalParamsBizService.createTechnicalParamsForNewOrganization(organizationBasicInfo.getOrganizationNo());
                    log.info("为新商户创建技术参数配置成功，机构号：{}", organizationBasicInfo.getOrganizationNo());
                } catch (Exception techParamsException) {
                    log.error("为新商户创建技术参数配置失败，机构号：{}，错误：{}",
                            organizationBasicInfo.getOrganizationNo(), techParamsException.getMessage(), techParamsException);
                }
            } catch (Exception e) {
                log.error("新增商户信息失败,商户信息:{}", klKycPushVO, e);
            }
        });

        log.info("同步商户信息结束");
    }

    /**
     * 根据机构号查询机构信息
     * @param organizationNo 机构号
     * @return
     */
    public OrganizationBasicInfo selectOrganizationBasicInfoByOrganization(String organizationNo) {
       return organizationBasicInfoMapper.selectOne(Wrappers.<OrganizationBasicInfo>lambdaQuery()
               .eq(OrganizationBasicInfo::getOrganizationNo, organizationNo)
               .eq(OrganizationBasicInfo::getStatus, ValidStatusEnum.VALID.getValue()));
    }

}

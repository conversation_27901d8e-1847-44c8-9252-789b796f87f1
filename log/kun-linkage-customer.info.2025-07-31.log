2025-07-31 15:55:45.371 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-31 15:55:45.442 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-31 15:55:46.079 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-31 15:55:46.079 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-31 15:55:47.859 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-07-31 15:55:47.907 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-31 15:55:49.123 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 15:55:49.128 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 15:55:49.175 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 27 ms. Found 0 Redis repository interfaces.
2025-07-31 15:55:49.409 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-31 15:55:49.684 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=193bc5b0-4a94-3329-b4a1-1233bbf07d9e
2025-07-31 15:55:49.799 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 15:55:49.800 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 15:55:49.800 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$533/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 15:55:49.801 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 15:55:49.804 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 15:55:49.808 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 15:55:50.408 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$5c020144] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 15:55:51.295 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-31 15:55:51.296 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3372 ms
2025-07-31 15:56:01.729 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-31 15:56:06.655 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 15:56:06.754 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 15:56:06.766 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 15:56:07.623 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-31 15:56:07.623 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-31 15:56:07.623 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-31 15:56:14.077 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 15:56:14.112 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 15:56:14.760 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-31 15:56:16.099 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-31 15:56:19.230 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-31 15:56:27.349 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 15:56:27.935 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-31 15:56:28.466 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-31 15:56:30.259 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-31 15:56:30.310 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 15:56:30.310 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-31 15:56:30.321 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-31 15:56:30.323 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 15:56:30.323 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 15:56:30.324 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-31 15:56:30.324 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@b230f87
2025-07-31 15:56:34.324 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-07-31 15:56:35.236 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3cf09930[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$621cfbf5#mpcWalletEventRetryNotifyTask]
2025-07-31 15:56:35.236 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3bd8032e[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$ec6ad785#organizationFeeMonthlyReportTask]
2025-07-31 15:56:35.237 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@39613474[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$b367ee77#organizationSMSFeeCalculateTask]
2025-07-31 15:56:35.237 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@660d78b7[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$3fba82b0#syncCustomerInfoTask]
2025-07-31 15:56:35.255 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 15:56:35.274 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 15:56:40.665 [Thread-140] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-31 15:56:46.911 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 15:56:46.912 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-31 15:56:58.068 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 15:56:58.068 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-31 15:57:09.221 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 15:57:09.223 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-31 15:57:20.376 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 15:57:20.377 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-31 15:57:28.563 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 15:57:28.564 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-31 15:57:28.703 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-31 15:57:28.740 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-31 15:57:28.756 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-31 15:57:28.844 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-31 15:57:28.940 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-31 15:57:28.978 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-31 15:57:28.978 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-31 15:57:33.330 [XNIO-1 task-1] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 15:57:33.330 [XNIO-1 task-1] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-31 15:57:33.333 [XNIO-1 task-1] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 3 ms
2025-07-31 15:57:40.465 [XNIO-1 task-5] INFO  [ ddfaf694f5dcac3a , ddfaf694f5dcac3a ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 1231 ms
2025-07-31 15:57:44.401 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 15:57:50.421 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 15:57:56.432 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 15:58:04.814 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 15:58:10.825 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 15:58:10.860 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.destroy:847 - Shutting down Quartz Scheduler
2025-07-31 15:58:10.860 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:666 - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 15:58:10.861 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 15:58:10.861 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:740 - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 15:58:10.871 [Thread-140] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:91 - >>>>>>>>>>> xxl-job remoting server stop.
2025-07-31 15:58:11.484 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:87 - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='kun-linkage-customer-executor', registryValue='http://192.168.0.105:16661/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-31 15:58:11.485 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:105 - >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-31 15:58:11.485 [main] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.stop:117 - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-31 15:58:11.486 [xxl-job, executor JobLogFileCleanThread] INFO  [  ,  ] com.xxl.job.core.thread.JobLogFileCleanThread.run:99 - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-07-31 15:58:11.486 [xxl-job, executor TriggerCallbackThread] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:98 - >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-31 15:58:11.487 [Thread-129] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:128 - >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-31 15:58:17.507 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-31 15:58:17.508 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:9876] result: true
2025-07-31 15:58:23.521 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-31 15:58:23.522 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:9876] result: true
2025-07-31 15:58:23.670 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-07-31 15:58:23.706 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-07-31 15:58:23.708 [main] INFO  [  ,  ] io.undertow.stop:259 - stopping server: Undertow - 2.2.28.Final
2025-07-31 15:58:23.719 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-31 15:58:23.746 [main] INFO  [  ,  ] o.s.b.a.l.ConditionEvaluationReportLoggingListener.logMessage:136 - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-31 16:01:00.340 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-31 16:01:00.411 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-31 16:01:01.023 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-31 16:01:02.205 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 16:01:02.208 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 16:01:02.246 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-07-31 16:01:02.441 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-31 16:01:02.701 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=22be0dab-3101-389b-b974-65d573d49875
2025-07-31 16:01:02.834 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:01:02.835 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:01:02.836 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$504/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:01:02.836 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:01:02.839 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:01:02.844 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:01:03.392 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$9aab46cc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:01:04.248 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-31 16:01:04.249 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3214 ms
2025-07-31 16:01:09.672 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-31 16:01:14.059 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:01:14.156 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:01:14.170 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:01:14.964 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-31 16:01:14.966 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-31 16:01:14.967 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-31 16:01:21.552 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:01:21.592 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:01:22.407 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-31 16:01:23.635 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-31 16:01:27.395 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-31 16:01:34.917 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:01:35.699 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-31 16:01:36.331 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-31 16:01:38.729 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-31 16:01:38.805 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 16:01:38.806 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-31 16:01:38.819 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-31 16:01:38.823 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 16:01:38.824 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 16:01:38.824 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-31 16:01:38.824 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@52b2f795
2025-07-31 16:01:42.120 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 18 endpoint(s) beneath base path '/actuator'
2025-07-31 16:01:43.004 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4608a871[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$f413e59#mpcWalletEventRetryNotifyTask]
2025-07-31 16:01:43.004 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2248583e[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$998f19e9#organizationFeeMonthlyReportTask]
2025-07-31 16:01:43.005 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1dfd27fe[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$608c30db#organizationSMSFeeCalculateTask]
2025-07-31 16:01:43.005 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3b6f1227[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$ecdec514#syncCustomerInfoTask]
2025-07-31 16:01:43.017 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:01:43.036 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:01:48.412 [Thread-111] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-31 16:01:54.978 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:01:54.978 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-31 16:02:06.142 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:02:06.143 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-31 16:02:17.300 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:02:17.301 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-31 16:02:28.473 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:02:28.474 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-31 16:02:36.853 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:02:36.854 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-31 16:02:36.951 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-31 16:02:36.989 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-31 16:02:37.004 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-31 16:02:37.082 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-31 16:02:37.177 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-31 16:02:37.193 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-31 16:02:37.196 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 16:02:37.236 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 97.243 seconds (JVM running for 103.832)
2025-07-31 16:02:37.539 [RMI TCP Connection(13)-192.168.0.105] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 16:02:37.539 [RMI TCP Connection(13)-192.168.0.105] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-31 16:02:37.547 [RMI TCP Connection(13)-192.168.0.105] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 8 ms
2025-07-31 16:03:37.636 [XNIO-1 task-1] INFO  [ c044c3c0ce975c6e , c044c3c0ce975c6e ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 1058 ms
2025-07-31 16:03:43.570 [XNIO-1 task-1] INFO  [ 362f1ac517889477 , 362f1ac517889477 ] c.k.l.c.c.webhook.TechnicalParamsController.update:79 - 更新技术参数配置，参数：TechnicalParamsUpdateDTO(id=4, organizationNo=12090276, webhookEnabled=0, webhookUrlKyc=https://www.baidu.com, webhookUrlThirdPartyAuth=https://www.baidu.com, webhookUrlAuthResult=https://www.baidu.com, webhookUrl3dsOtp=https://www.google.com, status=VALID, createTime=2025-07-29T12:03:13, createUserId=, createUserName=, lastModifyTime=2025-07-29T12:03:13, lastModifyUserId=, lastModifyUserName=)
2025-07-31 16:03:43.598 [XNIO-1 task-1] INFO  [ 362f1ac517889477 , 362f1ac517889477 ] c.k.l.customer.service.TechnicalParamsBizService.updateTechnicalParams:106 - 更新技术参数配置，参数：TechnicalParamsUpdateDTO(id=4, organizationNo=12090276, webhookEnabled=0, webhookUrlKyc=https://www.baidu.com, webhookUrlThirdPartyAuth=https://www.baidu.com, webhookUrlAuthResult=https://www.baidu.com, webhookUrl3dsOtp=https://www.google.com, status=VALID, createTime=2025-07-29T12:03:13, createUserId=, createUserName=, lastModifyTime=2025-07-29T12:03:13, lastModifyUserId=, lastModifyUserName=)
2025-07-31 16:06:36.611 [XNIO-1 task-1] INFO  [ 362f1ac517889477 , 362f1ac517889477 ] ShardingSphere-SQL.log:74 - Logic SQL: UPDATE kl_technical_params  SET organization_no=?,

webhook_enabled=?,
webhook_url_kyc=?,
webhook_url_third_party_auth=?,
webhook_url_auth_result=?,
webhook_url_3ds_otp=?,
status=?,
create_time=?,
create_user_id=?,
create_user_name=?,
last_modify_time=?,
last_modify_user_id=?,
last_modify_user_name=?  WHERE id=?
2025-07-31 16:06:36.648 [XNIO-1 task-1] INFO  [ 362f1ac517889477 , 362f1ac517889477 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLUpdateStatement(orderBy=Optional.empty, limit=Optional.empty)
2025-07-31 16:06:36.682 [XNIO-1 task-1] INFO  [ 362f1ac517889477 , 362f1ac517889477 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: UPDATE kl_technical_params  SET organization_no=?,

webhook_enabled=?,
webhook_url_kyc=?,
webhook_url_third_party_auth=?,
webhook_url_auth_result=?,
webhook_url_3ds_otp=?,
status=?,
create_time=?,
create_user_id=?,
create_user_name=?,
last_modify_time=?,
last_modify_user_id=?,
last_modify_user_name=?  WHERE id=? ::: [12090276, 0, https://www.baidu.com, https://www.baidu.com, https://www.baidu.com, https://www.google.com, VALID, 2025-07-29T12:03:13, , , 2025-07-31T16:03:43.605, , , 4]
2025-07-31 16:19:12.435 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 16:19:12.703 [XNIO-1 task-1] INFO  [ 99e1195d578f4ca0 , 99e1195d578f4ca0 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT COUNT( * ) FROM kl_organization_customer_card_info 
 
 WHERE (organization_no = ? AND customer_id = ? AND card_active_status = ?)
2025-07-31 16:19:12.704 [XNIO-1 task-1] INFO  [ 99e1195d578f4ca0 , 99e1195d578f4ca0 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-31 16:19:12.704 [XNIO-1 task-1] INFO  [ 99e1195d578f4ca0 , 99e1195d578f4ca0 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT COUNT( * ) FROM kl_organization_customer_card_info_4 
 
 WHERE (organization_no = ? AND customer_id = ? AND card_active_status = ?) ::: [12090276, 23, ACTIVATED]
2025-07-31 16:19:13.307 [XNIO-1 task-1] INFO  [ 99e1195d578f4ca0 , 99e1195d578f4ca0 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_card_recharge_detail WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ? AND recharge_status = ?)
2025-07-31 16:19:13.307 [XNIO-1 task-1] INFO  [ 99e1195d578f4ca0 , 99e1195d578f4ca0 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-31 16:19:13.307 [XNIO-1 task-1] INFO  [ 99e1195d578f4ca0 , 99e1195d578f4ca0 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_card_recharge_detail_2025Q3 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ? AND recharge_status = ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-31T23:59:59, SUCCESS]
2025-07-31 16:19:21.689 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-31 16:19:21.759 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-31 16:19:22.564 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-31 16:19:23.736 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 16:19:23.741 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 16:19:23.781 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-07-31 16:19:23.978 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-31 16:19:24.221 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=22be0dab-3101-389b-b974-65d573d49875
2025-07-31 16:19:24.333 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:19:24.335 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:19:24.335 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$504/349757746] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:19:24.336 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:19:24.339 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:19:24.344 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:19:24.889 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$ff91457c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:19:25.568 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-31 16:19:25.568 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 2992 ms
2025-07-31 16:19:31.152 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-31 16:19:35.937 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:19:36.052 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:19:36.066 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:19:36.992 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-31 16:19:36.994 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-31 16:19:36.995 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-31 16:19:43.265 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:19:43.292 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:19:43.884 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-31 16:19:53.022 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-31 16:19:55.943 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-31 16:19:58.009 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:19:58.534 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-31 16:19:58.980 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-31 16:20:00.487 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-31 16:20:00.532 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 16:20:00.532 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-31 16:20:00.542 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-31 16:20:00.544 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 16:20:00.544 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 16:20:00.544 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-31 16:20:00.544 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@626a783b
2025-07-31 16:20:03.479 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 18 endpoint(s) beneath base path '/actuator'
2025-07-31 16:20:04.172 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6c916b7e[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$ff716a13#mpcWalletEventRetryNotifyTask]
2025-07-31 16:20:04.173 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4631ecf5[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$89bf45a3#organizationFeeMonthlyReportTask]
2025-07-31 16:20:04.173 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2892b704[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$50bc5c95#organizationSMSFeeCalculateTask]
2025-07-31 16:20:04.174 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6b2d01e2[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$dd0ef0ce#syncCustomerInfoTask]
2025-07-31 16:20:04.186 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:20:04.205 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:20:09.559 [Thread-110] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-31 16:20:15.842 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:20:15.843 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-31 16:20:27.033 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:20:27.035 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-31 16:20:38.199 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:20:38.200 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-31 16:20:49.360 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:20:49.361 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-31 16:21:00.522 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:21:00.523 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-31 16:21:00.645 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-31 16:21:00.678 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-31 16:21:00.694 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-31 16:21:00.782 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-31 16:21:00.871 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-31 16:21:00.875 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-31 16:21:00.876 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 16:21:00.911 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 99.594 seconds (JVM running for 106.219)
2025-07-31 16:21:01.448 [RMI TCP Connection(3)-192.168.0.105] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 16:21:01.448 [RMI TCP Connection(3)-192.168.0.105] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-31 16:21:01.456 [RMI TCP Connection(3)-192.168.0.105] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 7 ms
2025-07-31 16:21:14.441 [XNIO-1 task-1] INFO  [ 87bb1afb87970971 , 87bb1afb87970971 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT COUNT( * ) FROM kl_organization_customer_card_info 
 
 WHERE (organization_no = ? AND customer_id = ? AND card_active_status = ?)
2025-07-31 16:21:14.442 [XNIO-1 task-1] INFO  [ 87bb1afb87970971 , 87bb1afb87970971 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-31 16:21:14.442 [XNIO-1 task-1] INFO  [ 87bb1afb87970971 , 87bb1afb87970971 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT COUNT( * ) FROM kl_organization_customer_card_info_4 
 
 WHERE (organization_no = ? AND customer_id = ? AND card_active_status = ?) ::: [12090276, 23, ACTIVATED]
2025-07-31 16:22:31.877 [XNIO-1 task-1] INFO  [ 87bb1afb87970971 , 87bb1afb87970971 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_card_recharge_detail WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ? AND recharge_status = ?)
2025-07-31 16:22:31.878 [XNIO-1 task-1] INFO  [ 87bb1afb87970971 , 87bb1afb87970971 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-31 16:22:31.878 [XNIO-1 task-1] INFO  [ 87bb1afb87970971 , 87bb1afb87970971 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_card_recharge_detail_2025Q3 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ? AND recharge_status = ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-31T23:59:59, SUCCESS]
2025-07-31 16:22:32.089 [XNIO-1 task-1] INFO  [ 87bb1afb87970971 , 87bb1afb87970971 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,business_type,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ? AND recharge_status = ?) ORDER BY recharge_datetime DESC
 LIMIT ? 
2025-07-31 16:22:32.089 [XNIO-1 task-1] INFO  [ 87bb1afb87970971 , 87bb1afb87970971 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@5b87d7ee], lock=Optional.empty, window=Optional.empty)
2025-07-31 16:22:32.089 [XNIO-1 task-1] INFO  [ 87bb1afb87970971 , 87bb1afb87970971 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,business_type,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail_2025Q3 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ? AND recharge_status = ?) ORDER BY recharge_datetime DESC
 LIMIT ?  ::: [12090276, 23, 2025-07-01T00:00, 2025-07-31T23:59:59, SUCCESS, 10]
2025-07-31 16:22:35.351 [XNIO-1 task-1] INFO  [ 480388b235aa1ec2 , 480388b235aa1ec2 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT COUNT( * ) FROM kl_organization_customer_card_info 
 
 WHERE (organization_no = ? AND customer_id = ? AND card_active_status = ?)
2025-07-31 16:22:35.351 [XNIO-1 task-1] INFO  [ 480388b235aa1ec2 , 480388b235aa1ec2 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-31 16:22:35.351 [XNIO-1 task-1] INFO  [ 480388b235aa1ec2 , 480388b235aa1ec2 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT COUNT( * ) FROM kl_organization_customer_card_info_4 
 
 WHERE (organization_no = ? AND customer_id = ? AND card_active_status = ?) ::: [12090276, 23, ACTIVATED]
2025-07-31 16:22:38.122 [XNIO-1 task-1] INFO  [ 480388b235aa1ec2 , 480388b235aa1ec2 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_card_recharge_detail WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ? AND recharge_status = ?)
2025-07-31 16:22:38.126 [XNIO-1 task-1] INFO  [ 480388b235aa1ec2 , 480388b235aa1ec2 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-31 16:22:38.126 [XNIO-1 task-1] INFO  [ 480388b235aa1ec2 , 480388b235aa1ec2 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_card_recharge_detail_2025Q3 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ? AND recharge_status = ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-31T23:59:59, SUCCESS]
2025-07-31 16:22:38.200 [XNIO-1 task-1] INFO  [ 480388b235aa1ec2 , 480388b235aa1ec2 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,business_type,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ? AND recharge_status = ?) ORDER BY recharge_datetime DESC
 LIMIT ? 
2025-07-31 16:22:38.200 [XNIO-1 task-1] INFO  [ 480388b235aa1ec2 , 480388b235aa1ec2 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@5b87d7ee], lock=Optional.empty, window=Optional.empty)
2025-07-31 16:22:38.201 [XNIO-1 task-1] INFO  [ 480388b235aa1ec2 , 480388b235aa1ec2 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,business_type,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail_2025Q3 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ? AND recharge_status = ?) ORDER BY recharge_datetime DESC
 LIMIT ?  ::: [12090276, 23, 2025-07-01T00:00, 2025-07-31T23:59:59, SUCCESS, 10]
2025-07-31 16:37:43.003 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 16:38:45.427 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-31 16:38:45.498 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-31 16:38:46.065 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-31 16:38:47.227 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 16:38:47.231 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 16:38:47.267 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-07-31 16:38:47.468 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-31 16:38:47.707 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=22be0dab-3101-389b-b974-65d573d49875
2025-07-31 16:38:47.810 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:38:47.811 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:38:47.812 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$504/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:38:47.812 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:38:47.815 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:38:47.819 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:38:48.363 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$98d0a6fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:38:49.049 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-31 16:38:49.049 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 2973 ms
2025-07-31 16:38:54.513 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-31 16:38:59.541 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:38:59.633 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:38:59.645 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:39:00.434 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-31 16:39:00.436 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-31 16:39:00.436 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-31 16:39:06.702 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:39:06.731 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:39:07.339 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-31 16:39:08.609 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-31 16:39:11.533 [redisson-netty-2-19] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-31 16:39:18.489 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:39:19.127 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-31 16:39:19.621 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-31 16:39:21.196 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-31 16:39:21.260 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 16:39:21.261 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-31 16:39:21.272 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-31 16:39:21.274 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 16:39:21.275 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 16:39:21.275 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-31 16:39:21.275 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5563630d
2025-07-31 16:39:24.294 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 18 endpoint(s) beneath base path '/actuator'
2025-07-31 16:39:25.000 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@40d2a644[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$85f0f1ad#mpcWalletEventRetryNotifyTask]
2025-07-31 16:39:25.000 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@56fe2233[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$103ecd3d#organizationFeeMonthlyReportTask]
2025-07-31 16:39:25.001 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6e879a78[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$d73be42f#organizationSMSFeeCalculateTask]
2025-07-31 16:39:25.001 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@882364e[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$638e7868#syncCustomerInfoTask]
2025-07-31 16:39:25.013 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:39:25.031 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:39:30.391 [Thread-106] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-31 16:39:36.714 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:39:36.714 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-31 16:39:47.882 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:39:47.883 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-31 16:39:59.052 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:39:59.053 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-31 16:40:10.227 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:40:10.228 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-31 16:40:18.390 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:40:18.391 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-31 16:40:18.531 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-31 16:40:18.564 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-31 16:40:18.580 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-31 16:40:18.654 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-31 16:40:18.742 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-31 16:40:18.745 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-31 16:40:18.746 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 16:40:18.782 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 93.681 seconds (JVM running for 100.092)
2025-07-31 16:40:19.131 [RMI TCP Connection(3)-192.168.0.105] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 16:40:19.131 [RMI TCP Connection(3)-192.168.0.105] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-31 16:40:19.140 [RMI TCP Connection(3)-192.168.0.105] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 9 ms
2025-07-31 16:40:44.144 [XNIO-1 task-1] INFO  [ 414960c7a4719195 , 414960c7a4719195 ] c.k.l.c.c.webhook.TechnicalParamsController.update:79 - 更新技术参数配置，参数：TechnicalParamsUpdateDTO(id=4, organizationNo=12001944, webhookEnabled=0, webhookUrlKyc=https://www.baidu.com, webhookUrlThirdPartyAuth=https://www.baidu.com, webhookUrlAuthResult=https://www.baidu.com, webhookUrl3dsOtp=https://www.google.com, status=VALID, createTime=2025-07-29T12:03:13, createUserId=, createUserName=, lastModifyTime=2025-07-31T15:53:38, lastModifyUserId=25052653600, lastModifyUserName=KateTest6)
2025-07-31 16:40:44.174 [XNIO-1 task-1] INFO  [ 414960c7a4719195 , 414960c7a4719195 ] c.k.l.customer.service.TechnicalParamsBizService.updateTechnicalParams:106 - 更新技术参数配置，参数：TechnicalParamsUpdateDTO(id=4, organizationNo=12001944, webhookEnabled=0, webhookUrlKyc=https://www.baidu.com, webhookUrlThirdPartyAuth=https://www.baidu.com, webhookUrlAuthResult=https://www.baidu.com, webhookUrl3dsOtp=https://www.google.com, status=VALID, createTime=2025-07-29T12:03:13, createUserId=, createUserName=, lastModifyTime=2025-07-31T15:53:38, lastModifyUserId=25052653600, lastModifyUserName=KateTest6)
2025-07-31 16:40:44.498 [XNIO-1 task-1] INFO  [ 414960c7a4719195 , 414960c7a4719195 ] ShardingSphere-SQL.log:74 - Logic SQL: UPDATE kl_technical_params  SET organization_no=?,

webhook_enabled=?,
webhook_url_kyc=?,
webhook_url_third_party_auth=?,
webhook_url_auth_result=?,
webhook_url_3ds_otp=?,
status=?,
create_time=?,
create_user_id=?,
create_user_name=?,
last_modify_time=?,
last_modify_user_id=?,
last_modify_user_name=?  WHERE id=?
2025-07-31 16:40:44.498 [XNIO-1 task-1] INFO  [ 414960c7a4719195 , 414960c7a4719195 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLUpdateStatement(orderBy=Optional.empty, limit=Optional.empty)
2025-07-31 16:40:44.498 [XNIO-1 task-1] INFO  [ 414960c7a4719195 , 414960c7a4719195 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: UPDATE kl_technical_params  SET organization_no=?,

webhook_enabled=?,
webhook_url_kyc=?,
webhook_url_third_party_auth=?,
webhook_url_auth_result=?,
webhook_url_3ds_otp=?,
status=?,
create_time=?,
create_user_id=?,
create_user_name=?,
last_modify_time=?,
last_modify_user_id=?,
last_modify_user_name=?  WHERE id=? ::: [12001944, 0, https://www.baidu.com, https://www.baidu.com, https://www.baidu.com, https://www.google.com, VALID, 2025-07-29T12:03:13, , , 2025-07-31T16:40:44.183, 25052653600, KateTest6, 4]
2025-07-31 16:40:52.225 [XNIO-1 task-1] INFO  [ 414960c7a4719195 , 414960c7a4719195 ] c.k.l.customer.service.TechnicalParamsBizService.updateTechnicalParams:115 - 技术参数配置更新成功，机构号：12001944
2025-07-31 16:42:03.853 [XNIO-1 task-1] INFO  [ 3be67152cde25c79 , 3be67152cde25c79 ] c.k.l.c.c.webhook.TechnicalParamsController.update:79 - 更新技术参数配置，参数：TechnicalParamsUpdateDTO(id=4, organizationNo=12001944, webhookEnabled=0, webhookUrlKyc=https://www.baidu.com, webhookUrlThirdPartyAuth=https://www.baidu.com, webhookUrlAuthResult=https://www.baidu.com, webhookUrl3dsOtp=https://www.google.com, status=VALID, createTime=2025-07-29T12:03:13, createUserId=, createUserName=, lastModifyTime=2025-07-31T15:53:38, lastModifyUserId=25052653600, lastModifyUserName=KateTest6)
2025-07-31 16:42:03.855 [XNIO-1 task-1] INFO  [ 3be67152cde25c79 , 3be67152cde25c79 ] c.k.l.customer.service.TechnicalParamsBizService.updateTechnicalParams:106 - 更新技术参数配置，参数：TechnicalParamsUpdateDTO(id=4, organizationNo=12001944, webhookEnabled=0, webhookUrlKyc=https://www.baidu.com, webhookUrlThirdPartyAuth=https://www.baidu.com, webhookUrlAuthResult=https://www.baidu.com, webhookUrl3dsOtp=https://www.google.com, status=VALID, createTime=2025-07-29T12:03:13, createUserId=, createUserName=, lastModifyTime=2025-07-31T15:53:38, lastModifyUserId=25052653600, lastModifyUserName=KateTest6)
2025-07-31 16:42:03.868 [XNIO-1 task-1] INFO  [ 3be67152cde25c79 , 3be67152cde25c79 ] ShardingSphere-SQL.log:74 - Logic SQL: UPDATE kl_technical_params  SET organization_no=?,

webhook_enabled=?,
webhook_url_kyc=?,
webhook_url_third_party_auth=?,
webhook_url_auth_result=?,
webhook_url_3ds_otp=?,
status=?,
create_time=?,
create_user_id=?,
create_user_name=?,
last_modify_time=?,
last_modify_user_id=?,
last_modify_user_name=?  WHERE id=?
2025-07-31 16:42:03.868 [XNIO-1 task-1] INFO  [ 3be67152cde25c79 , 3be67152cde25c79 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLUpdateStatement(orderBy=Optional.empty, limit=Optional.empty)
2025-07-31 16:42:03.868 [XNIO-1 task-1] INFO  [ 3be67152cde25c79 , 3be67152cde25c79 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: UPDATE kl_technical_params  SET organization_no=?,

webhook_enabled=?,
webhook_url_kyc=?,
webhook_url_third_party_auth=?,
webhook_url_auth_result=?,
webhook_url_3ds_otp=?,
status=?,
create_time=?,
create_user_id=?,
create_user_name=?,
last_modify_time=?,
last_modify_user_id=?,
last_modify_user_name=?  WHERE id=? ::: [12001944, 0, https://www.baidu.com, https://www.baidu.com, https://www.baidu.com, https://www.google.com, VALID, 2025-07-29T12:03:13, , , 2025-07-31T16:42:03.855, 25052653600, KateTest6, 4]
2025-07-31 16:42:39.805 [XNIO-1 task-1] INFO  [ 3be67152cde25c79 , 3be67152cde25c79 ] c.k.l.customer.service.TechnicalParamsBizService.updateTechnicalParams:115 - 技术参数配置更新成功，机构号：12001944
2025-07-31 16:42:49.507 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 16:46:05.004 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-31 16:46:05.076 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-31 16:46:05.637 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-31 16:46:06.754 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 16:46:06.758 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 16:46:06.793 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-07-31 16:46:06.987 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-31 16:46:07.229 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=22be0dab-3101-389b-b974-65d573d49875
2025-07-31 16:46:07.332 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:46:07.333 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:46:07.333 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$504/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:46:07.334 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:46:07.336 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:46:07.340 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:46:07.865 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$98d0a6fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 16:46:08.541 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-31 16:46:08.541 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 2892 ms
2025-07-31 16:46:14.023 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-31 16:46:18.837 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:46:18.925 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:46:18.937 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:46:19.789 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-31 16:46:19.792 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-31 16:46:19.793 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-31 16:46:26.058 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:46:26.089 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:46:26.716 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-31 16:46:27.837 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-31 16:46:31.895 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-31 16:46:39.006 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:46:39.560 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-31 16:46:40.022 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-31 16:46:41.632 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-31 16:46:41.678 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 16:46:41.678 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-31 16:46:41.687 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-31 16:46:41.690 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 16:46:41.690 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 16:46:41.690 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-31 16:46:41.690 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7d82eeb9
2025-07-31 16:46:45.184 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 18 endpoint(s) beneath base path '/actuator'
2025-07-31 16:46:45.988 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@c005d62[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$13369149#mpcWalletEventRetryNotifyTask]
2025-07-31 16:46:45.988 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@451f5e53[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$9d846cd9#organizationFeeMonthlyReportTask]
2025-07-31 16:46:45.989 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7808fb63[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$648183cb#organizationSMSFeeCalculateTask]
2025-07-31 16:46:45.989 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7b29d791[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$f0d41804#syncCustomerInfoTask]
2025-07-31 16:46:46.003 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:46:46.021 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 16:46:51.405 [Thread-106] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-31 16:46:57.727 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:46:57.728 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-31 16:47:08.906 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:47:08.908 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-31 16:47:20.071 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:47:20.072 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-31 16:47:31.234 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:47:31.234 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-31 16:47:39.392 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-31 16:47:39.394 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-31 16:47:39.573 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-31 16:47:39.618 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-31 16:47:39.637 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-31 16:47:39.725 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-31 16:47:39.820 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-31 16:47:39.824 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-31 16:47:39.824 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 16:47:39.866 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 95.169 seconds (JVM running for 101.415)
2025-07-31 16:47:40.259 [RMI TCP Connection(6)-192.168.0.105] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 16:47:40.259 [RMI TCP Connection(6)-192.168.0.105] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-31 16:47:40.280 [RMI TCP Connection(6)-192.168.0.105] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 20 ms
2025-07-31 16:47:41.157 [XNIO-1 task-1] INFO  [ 531ded9a4809fdf3 , 531ded9a4809fdf3 ] c.k.l.c.c.webhook.TechnicalParamsController.update:79 - 更新技术参数配置，参数：TechnicalParamsUpdateDTO(id=4, aesKey=1234123412341234, organizationNo=12001944, webhookEnabled=0, webhookUrlKyc=https://www.baidu.com, webhookUrlThirdPartyAuth=https://www.baidu.com, webhookUrlAuthResult=https://www.baidu.com, webhookUrl3dsOtp=https://www.google.com, status=VALID, createTime=2025-07-29T12:03:13, createUserId=, createUserName=, lastModifyTime=2025-07-31T15:53:38, lastModifyUserId=25052653600, lastModifyUserName=KateTest6)
2025-07-31 16:47:41.192 [XNIO-1 task-1] INFO  [ 531ded9a4809fdf3 , 531ded9a4809fdf3 ] c.k.l.customer.service.TechnicalParamsBizService.updateTechnicalParams:106 - 更新技术参数配置，参数：TechnicalParamsUpdateDTO(id=4, aesKey=1234123412341234, organizationNo=12001944, webhookEnabled=0, webhookUrlKyc=https://www.baidu.com, webhookUrlThirdPartyAuth=https://www.baidu.com, webhookUrlAuthResult=https://www.baidu.com, webhookUrl3dsOtp=https://www.google.com, status=VALID, createTime=2025-07-29T12:03:13, createUserId=, createUserName=, lastModifyTime=2025-07-31T15:53:38, lastModifyUserId=25052653600, lastModifyUserName=KateTest6)
2025-07-31 16:47:41.503 [XNIO-1 task-1] INFO  [ 531ded9a4809fdf3 , 531ded9a4809fdf3 ] ShardingSphere-SQL.log:74 - Logic SQL: UPDATE kl_technical_params  SET organization_no=?,
aes_key=?,
webhook_enabled=?,
webhook_url_kyc=?,
webhook_url_third_party_auth=?,
webhook_url_auth_result=?,
webhook_url_3ds_otp=?,
status=?,
create_time=?,
create_user_id=?,
create_user_name=?,
last_modify_time=?,
last_modify_user_id=?,
last_modify_user_name=?  WHERE id=?
2025-07-31 16:47:41.503 [XNIO-1 task-1] INFO  [ 531ded9a4809fdf3 , 531ded9a4809fdf3 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLUpdateStatement(orderBy=Optional.empty, limit=Optional.empty)
2025-07-31 16:47:41.503 [XNIO-1 task-1] INFO  [ 531ded9a4809fdf3 , 531ded9a4809fdf3 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: UPDATE kl_technical_params  SET organization_no=?,
aes_key=?,
webhook_enabled=?,
webhook_url_kyc=?,
webhook_url_third_party_auth=?,
webhook_url_auth_result=?,
webhook_url_3ds_otp=?,
status=?,
create_time=?,
create_user_id=?,
create_user_name=?,
last_modify_time=?,
last_modify_user_id=?,
last_modify_user_name=?  WHERE id=? ::: [12001944, 1234123412341234, 0, https://www.baidu.com, https://www.baidu.com, https://www.baidu.com, https://www.google.com, VALID, 2025-07-29T12:03:13, , , 2025-07-31T16:47:41.200, 25052653600, KateTest6, 4]
2025-07-31 16:47:46.352 [XNIO-1 task-1] INFO  [ 531ded9a4809fdf3 , 531ded9a4809fdf3 ] c.k.l.customer.service.TechnicalParamsBizService.updateTechnicalParams:115 - 技术参数配置更新成功，机构号：12001944
2025-07-31 16:49:04.566 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.

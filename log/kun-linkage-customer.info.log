2025-08-04 17:06:30.734 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-04 17:06:30.800 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-04 17:06:31.416 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 17:06:31.416 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 17:06:33.153 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-08-04 17:06:33.202 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-04 17:06:34.458 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 17:06:34.463 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 17:06:34.503 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-08-04 17:06:34.710 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-04 17:06:35.022 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=e71913b0-8fc4-3ff7-bf63-17db799ab456
2025-08-04 17:06:35.148 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:06:35.149 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:06:35.150 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:06:35.150 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:06:35.153 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:06:35.157 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:06:35.752 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$7d5aa4e0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:06:36.628 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-04 17:06:36.628 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3406 ms
2025-08-04 17:06:47.696 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-04 17:06:56.550 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:06:56.650 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:06:56.665 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:06:57.519 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-04 17:06:57.519 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:06:57.519 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-04 17:07:04.017 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:07:04.054 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:07:04.666 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-04 17:07:06.763 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 17:07:09.993 [redisson-netty-2-19] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 17:07:17.142 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:07:17.746 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-04 17:07:17.790 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-08-04 17:07:18.208 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-04 17:07:19.872 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-04 17:07:19.921 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-04 17:07:19.921 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-04 17:07:19.931 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-04 17:07:19.934 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-04 17:07:19.934 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-04 17:07:19.934 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-04 17:07:19.935 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7bca3747
2025-08-04 17:07:23.142 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-04 17:07:23.925 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@33c4fc94[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$96160e44#mpcWalletEventRetryNotifyTask]
2025-08-04 17:07:23.925 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7ad9ed91[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$2063e9d4#organizationFeeMonthlyReportTask]
2025-08-04 17:07:23.926 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@60362b7[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$e76100c6#organizationSMSFeeCalculateTask]
2025-08-04 17:07:23.926 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@18809db8[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$73b394ff#syncCustomerInfoTask]
2025-08-04 17:07:23.940 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:07:23.959 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:07:29.349 [Thread-146] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-08-04 17:07:35.601 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:07:35.602 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-04 17:07:46.796 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:07:46.799 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-04 17:07:57.986 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:07:57.987 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-04 17:08:09.167 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:08:09.169 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-08-04 17:08:17.354 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:08:17.354 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-08-04 17:08:17.509 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-04 17:08:17.548 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-04 17:08:17.571 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-04 17:08:17.659 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-04 17:08:17.765 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-08-04 17:08:17.809 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 17:08:17.809 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 17:08:18.272 [XNIO-1 task-1] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 17:08:18.272 [XNIO-1 task-1] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-04 17:08:18.275 [XNIO-1 task-1] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 3 ms
2025-08-04 17:08:18.721 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-08-04 17:08:18.740 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-04 17:08:18.740 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-04 17:08:18.858 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 108.465 seconds (JVM running for 114.851)
2025-08-04 17:08:19.006 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-08-04 17:08:19.007 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-08-04 17:08:19.008 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-08-04 17:08:25.583 [XNIO-1 task-1] INFO  [ 03a843a5138f47e5 , 03a843a5138f47e5 ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 1124 ms
2025-08-04 17:09:29.454 [XNIO-1 task-1] INFO  [ baf71ae8db02fb2f , baf71ae8db02fb2f ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_fee_detail WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
2025-08-04 17:09:29.455 [XNIO-1 task-1] INFO  [ baf71ae8db02fb2f , baf71ae8db02fb2f ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:09:29.455 [XNIO-1 task-1] INFO  [ baf71ae8db02fb2f , baf71ae8db02fb2f ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_fee_detail_202505 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) UNION ALL SELECT count(0) FROM kl_organization_fee_detail_202506 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) UNION ALL SELECT count(0) FROM kl_organization_fee_detail_202507 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) UNION ALL SELECT count(0) FROM kl_organization_fee_detail_202508 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ::: [null, 2025-01-01T00:00, 2025-08-04T23:59:59, null, 2025-01-01T00:00, 2025-08-04T23:59:59, null, 2025-01-01T00:00, 2025-08-04T23:59:59, null, 2025-01-01T00:00, 2025-08-04T23:59:59]
2025-08-04 17:10:23.938 [XNIO-1 task-1] INFO  [ dbdfee223ff2bd6f , dbdfee223ff2bd6f ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_fee_detail WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
2025-08-04 17:10:23.939 [XNIO-1 task-1] INFO  [ dbdfee223ff2bd6f , dbdfee223ff2bd6f ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:10:23.939 [XNIO-1 task-1] INFO  [ dbdfee223ff2bd6f , dbdfee223ff2bd6f ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_fee_detail_202507 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ::: [null, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 17:11:28.292 [XNIO-1 task-1] INFO  [ 48992684b71da077 , 48992684b71da077 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_fee_detail WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
2025-08-04 17:11:28.293 [XNIO-1 task-1] INFO  [ 48992684b71da077 , 48992684b71da077 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:11:28.293 [XNIO-1 task-1] INFO  [ 48992684b71da077 , 48992684b71da077 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_fee_detail_202507 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ::: [null, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 17:11:54.018 [XNIO-1 task-1] INFO  [ d4bfa173b9375707 , d4bfa173b9375707 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_fee_detail WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
2025-08-04 17:11:54.020 [XNIO-1 task-1] INFO  [ d4bfa173b9375707 , d4bfa173b9375707 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:11:54.020 [XNIO-1 task-1] INFO  [ d4bfa173b9375707 , d4bfa173b9375707 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_fee_detail_202507 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ::: [12090276, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 17:11:54.189 [XNIO-1 task-1] INFO  [ d4bfa173b9375707 , d4bfa173b9375707 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,card_product_code,calculate_datetime,transaction_datetime,related_transaction_id,fee_type,fee_collection_method,transaction_amount,transaction_currency_code,transaction_currency_precision,fee_amount,fx_rate,deduct_processor,deduct_currency_code,deduct_currency_precision,deduct_fee_amount,deduct_request_no,remark,fee_collection_status,snapshot_billing_dimension,snapshot_min_amount,snapshot_max_amount,snapshot_proportion_rate,snapshot_proportion_min_amount,snapshot_proportion_max_amount,snapshot_fixed_amount,call_count,create_time,last_modify_time  FROM kl_organization_fee_detail 
 
 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ORDER BY create_time DESC
 LIMIT ? 
2025-08-04 17:11:54.189 [XNIO-1 task-1] INFO  [ d4bfa173b9375707 , d4bfa173b9375707 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@325fb8a4], lock=Optional.empty, window=Optional.empty)
2025-08-04 17:11:54.189 [XNIO-1 task-1] INFO  [ d4bfa173b9375707 , d4bfa173b9375707 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,card_product_code,calculate_datetime,transaction_datetime,related_transaction_id,fee_type,fee_collection_method,transaction_amount,transaction_currency_code,transaction_currency_precision,fee_amount,fx_rate,deduct_processor,deduct_currency_code,deduct_currency_precision,deduct_fee_amount,deduct_request_no,remark,fee_collection_status,snapshot_billing_dimension,snapshot_min_amount,snapshot_max_amount,snapshot_proportion_rate,snapshot_proportion_min_amount,snapshot_proportion_max_amount,snapshot_fixed_amount,call_count,create_time,last_modify_time  FROM kl_organization_fee_detail_202507 
 
 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ORDER BY create_time DESC
 LIMIT ?  ::: [12090276, 2025-07-01T00:00, 2025-07-31T23:59:59, 100]
2025-08-04 17:12:10.555 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - Logic SQL: INSERT INTO kl_export_file_record  ( file_record_id,
organization_no,
file_name,
file_type,


file_status,

create_time,
update_time )  VALUES  ( ?,
?,
?,
?,


?,

?,
? )
2025-08-04 17:12:10.555 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLInsertStatement(setAssignment=Optional.empty, onDuplicateKeyColumns=Optional.empty)
2025-08-04 17:12:10.555 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: INSERT INTO kl_export_file_record  ( file_record_id,
organization_no,
file_name,
file_type,


file_status,

create_time,
update_time )  VALUES  (?, ?, ?, ?, ?, ?, ?) ::: [1952296492996259841, 12090276, Settlement_12090276_20250804171210.csv, ORGANIZATION_FEE_EXPORT, PROCESSING, 2025-08-04 17:12:10.0, 2025-08-04 17:12:10.0]
2025-08-04 17:12:10.744 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] c.k.l.c.service.export.ExportFileRecordService.createFileRecord:40 - 创建文件记录成功，文件记录ID: 1952296492996259841, 文件名: Settlement_12090276_20250804171210.csv
2025-08-04 17:12:10.745 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] c.k.l.c.s.e.OrganizationFeeDetailExportService.asyncExportData:80 - 开始异步导出手续费明细数据，文件记录ID: 1952296492996259841
2025-08-04 17:12:10.753 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,card_product_code,calculate_datetime,transaction_datetime,related_transaction_id,fee_type,fee_collection_method,transaction_amount,transaction_currency_code,transaction_currency_precision,fee_amount,fx_rate,deduct_processor,deduct_currency_code,deduct_currency_precision,deduct_fee_amount,deduct_request_no,remark,fee_collection_status,snapshot_billing_dimension,snapshot_min_amount,snapshot_max_amount,snapshot_proportion_rate,snapshot_proportion_min_amount,snapshot_proportion_max_amount,snapshot_fixed_amount,call_count,create_time,last_modify_time  FROM kl_organization_fee_detail 
 
 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ORDER BY create_time DESC
2025-08-04 17:12:10.753 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:12:10.753 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,card_product_code,calculate_datetime,transaction_datetime,related_transaction_id,fee_type,fee_collection_method,transaction_amount,transaction_currency_code,transaction_currency_precision,fee_amount,fx_rate,deduct_processor,deduct_currency_code,deduct_currency_precision,deduct_fee_amount,deduct_request_no,remark,fee_collection_status,snapshot_billing_dimension,snapshot_min_amount,snapshot_max_amount,snapshot_proportion_rate,snapshot_proportion_min_amount,snapshot_proportion_max_amount,snapshot_fixed_amount,call_count,create_time,last_modify_time  FROM kl_organization_fee_detail_202507 
 
 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ORDER BY create_time DESC ::: [12090276, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 17:12:10.864 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] c.k.l.c.s.e.OrganizationFeeDetailExportService.asyncExportData:84 - 查询到 16 条手续费明细数据
2025-08-04 17:12:10.909 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] c.k.l.c.s.e.OrganizationFeeDetailExportService.asyncExportData:88 - CSV文件生成完成，文件大小: 2522 bytes
2025-08-04 17:12:13.767 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] c.k.l.c.s.e.OrganizationFeeDetailExportService.asyncExportData:92 - 文件上传S3成功，URL: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/customer/fee-reports/organization/202508/Settlement_12090276_20250804171210.csv
2025-08-04 17:12:13.792 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - Logic SQL: UPDATE kl_export_file_record  SET file_size=?,
s3_url=?,
file_status=?,


update_time=?  WHERE file_record_id=?
2025-08-04 17:12:13.792 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLUpdateStatement(orderBy=Optional.empty, limit=Optional.empty)
2025-08-04 17:12:13.793 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: UPDATE kl_export_file_record  SET file_size=?,
s3_url=?,
file_status=?,


update_time=?  WHERE file_record_id=? ::: [2522, https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/customer/fee-reports/organization/202508/Settlement_12090276_20250804171210.csv, SUCCESS, 2025-08-04 17:12:13.0, 1952296492996259841]
2025-08-04 17:12:13.954 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] c.k.l.c.service.export.ExportFileRecordService.updateFileRecordSuccess:56 - 更新文件记录为成功状态，文件记录ID: 1952296492996259841, S3 URL: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/customer/fee-reports/organization/202508/Settlement_12090276_20250804171210.csv
2025-08-04 17:12:13.954 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] c.k.l.c.s.e.OrganizationFeeDetailExportService.asyncExportData:96 - 手续费明细导出任务完成，文件记录ID: 1952296492996259841
2025-08-04 17:12:13.955 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] c.k.l.c.s.e.OrganizationFeeDetailExportService.asyncExportData:101 - 临时文件删除成功: /var/folders/s_/n4rjfkbj7s13rzhsr4yxs34c0000gp/T/fee_export_5711474392184781733.csv
2025-08-04 17:16:54.885 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-04 17:45:49.805 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-04 17:45:49.870 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-04 17:45:50.500 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 17:45:50.500 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 17:45:52.242 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-08-04 17:45:52.287 [com.alibaba.nacos.client.remote.worker] INFO  [  ,  ] com.alibaba.nacos.common.remote.client.printIfInfoEnabled:60 - [f17dcc0b-7f96-4e13-ab28-bf953c633658_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-08-04 17:45:52.294 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-04 17:45:53.536 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 17:45:53.540 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 17:45:53.588 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-08-04 17:45:53.796 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-04 17:45:54.124 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=1f84fbf2-8682-3e7c-a339-07a412e035e5
2025-08-04 17:45:54.246 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:45:54.247 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:45:54.248 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:45:54.248 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:45:54.251 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:45:54.255 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:45:54.860 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$7d5aa4e0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:45:55.731 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-04 17:45:55.731 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3418 ms
2025-08-04 17:46:06.220 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-04 17:46:11.845 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:46:11.944 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:46:11.960 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:46:12.895 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-04 17:46:12.895 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:46:12.895 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-04 17:46:19.366 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:46:19.400 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:46:20.026 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-04 17:46:21.300 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 17:46:24.195 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 17:46:31.193 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:46:31.789 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-04 17:46:31.834 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-08-04 17:46:32.271 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-04 17:46:33.940 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-04 17:46:33.988 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-04 17:46:33.989 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-04 17:46:33.999 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-04 17:46:34.001 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-04 17:46:34.002 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-04 17:46:34.002 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-04 17:46:34.002 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3918a9f9
2025-08-04 17:46:37.225 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-04 17:46:37.992 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@688fa02f[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$86266986#mpcWalletEventRetryNotifyTask]
2025-08-04 17:46:37.992 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@379445f[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$10744516#organizationFeeMonthlyReportTask]
2025-08-04 17:46:37.993 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1f7e6c3a[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$d7715c08#organizationSMSFeeCalculateTask]
2025-08-04 17:46:37.993 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@55024328[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$63c3f041#syncCustomerInfoTask]
2025-08-04 17:46:38.007 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:46:38.025 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:46:43.435 [Thread-136] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-08-04 17:46:49.677 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:46:49.679 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-04 17:47:00.844 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:47:00.846 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-04 17:47:12.041 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:47:12.042 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-04 17:47:23.212 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:47:23.213 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-08-04 17:47:31.380 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:47:31.381 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-08-04 17:47:31.547 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-04 17:47:31.591 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-04 17:47:31.611 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-04 17:47:31.704 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-04 17:47:31.803 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-08-04 17:47:31.845 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 17:47:31.845 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 17:47:32.467 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-08-04 17:47:32.472 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-04 17:47:32.473 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-04 17:47:32.511 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 103.039 seconds (JVM running for 109.354)
2025-08-04 17:47:32.544 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-08-04 17:47:32.544 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-08-04 17:47:32.544 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-08-04 17:47:32.738 [XNIO-1 task-1] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 17:47:32.738 [XNIO-1 task-1] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-04 17:47:32.741 [XNIO-1 task-1] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 2 ms
2025-08-04 17:47:39.800 [XNIO-1 task-19] INFO  [ 54b841a5dc3feb7f , 54b841a5dc3feb7f ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 1062 ms
2025-08-04 17:48:21.599 [XNIO-1 task-19] INFO  [ b274c24de62b1bd7 , b274c24de62b1bd7 ] c.k.l.c.s.o.OrganizationCardManagementService.getOrganizationCustomerCardList:41 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='12090276', createDateFrom=2025-07-01, createDateUntil=2025-07-31, cardId='null', customerId='null', cardStatus='null'}
2025-08-04 17:48:22.605 [XNIO-1 task-19] INFO  [ b274c24de62b1bd7 , b274c24de62b1bd7 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info WHERE (organization_no = ? AND create_time >= ? AND create_time <= ?)
2025-08-04 17:48:22.605 [XNIO-1 task-19] INFO  [ b274c24de62b1bd7 , b274c24de62b1bd7 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:48:22.605 [XNIO-1 task-19] INFO  [ b274c24de62b1bd7 , b274c24de62b1bd7 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_4 WHERE (organization_no = ? AND create_time >= ? AND create_time <= ?) ::: [12090276, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 17:48:37.842 [XNIO-1 task-19] INFO  [ 120fbabfbde421c1 , 120fbabfbde421c1 ] c.k.l.c.s.o.OrganizationCardManagementService.getOrganizationCustomerCardList:41 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='null', createDateFrom=2025-07-01, createDateUntil=2025-07-31, cardId='null', customerId='null', cardStatus='null'}
2025-08-04 17:48:37.887 [XNIO-1 task-19] INFO  [ 120fbabfbde421c1 , 120fbabfbde421c1 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info WHERE (create_time >= ? AND create_time <= ?)
2025-08-04 17:48:37.888 [XNIO-1 task-19] INFO  [ 120fbabfbde421c1 , 120fbabfbde421c1 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:48:37.888 [XNIO-1 task-19] INFO  [ 120fbabfbde421c1 , 120fbabfbde421c1 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_1 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_2 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_3 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_4 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_5 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_6 WHERE (create_time >= ? AND create_time <= ?) UNION ALL SELECT count(0) FROM kl_organization_customer_card_info_7 WHERE (create_time >= ? AND create_time <= ?) ::: [2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 17:50:40.499 [XNIO-1 task-19] INFO  [ f3f3be128a540c10 , f3f3be128a540c10 ] c.k.l.c.s.o.OrganizationCardManagementService.getOrganizationCustomerCardList:41 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='11009375', createDateFrom=2025-07-01, createDateUntil=2025-07-31, cardId='null', customerId='1234234', cardStatus='null'}
2025-08-04 17:50:40.531 [XNIO-1 task-19] INFO  [ f3f3be128a540c10 , f3f3be128a540c10 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info WHERE (organization_no = ? AND customer_id = ? AND create_time >= ? AND create_time <= ?)
2025-08-04 17:50:40.531 [XNIO-1 task-19] INFO  [ f3f3be128a540c10 , f3f3be128a540c10 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:50:40.531 [XNIO-1 task-19] INFO  [ f3f3be128a540c10 , f3f3be128a540c10 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_7 WHERE (organization_no = ? AND customer_id = ? AND create_time >= ? AND create_time <= ?) ::: [11009375, 1234234, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 17:50:55.941 [XNIO-1 task-19] INFO  [ 13a3ed336c165d4f , 13a3ed336c165d4f ] c.k.l.c.s.o.OrganizationCardManagementService.getOrganizationCustomerCardList:41 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='12090276', createDateFrom=2025-07-01, createDateUntil=2025-07-31, cardId='null', customerId='23', cardStatus='null'}
2025-08-04 17:50:55.966 [XNIO-1 task-19] INFO  [ 13a3ed336c165d4f , 13a3ed336c165d4f ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info WHERE (organization_no = ? AND customer_id = ? AND create_time >= ? AND create_time <= ?)
2025-08-04 17:50:55.966 [XNIO-1 task-19] INFO  [ 13a3ed336c165d4f , 13a3ed336c165d4f ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:50:55.966 [XNIO-1 task-19] INFO  [ 13a3ed336c165d4f , 13a3ed336c165d4f ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_4 WHERE (organization_no = ? AND customer_id = ? AND create_time >= ? AND create_time <= ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 17:53:08.718 [XNIO-1 task-19] INFO  [ 2f273ee95f63a3c8 , 2f273ee95f63a3c8 ] c.k.l.c.s.o.OrganizationCardManagementService.getOrganizationCustomerCardList:41 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='12090276', createDateFrom=2025-07-01, createDateUntil=2025-07-31, cardId='null', customerId='23', cardStatus='null'}
2025-08-04 17:53:08.731 [XNIO-1 task-19] INFO  [ 2f273ee95f63a3c8 , 2f273ee95f63a3c8 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info WHERE (organization_no = ? AND customer_id = ? AND create_time >= ? AND create_time <= ?)
2025-08-04 17:53:08.734 [XNIO-1 task-19] INFO  [ 2f273ee95f63a3c8 , 2f273ee95f63a3c8 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:53:08.734 [XNIO-1 task-19] INFO  [ 2f273ee95f63a3c8 , 2f273ee95f63a3c8 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_4 WHERE (organization_no = ? AND customer_id = ? AND create_time >= ? AND create_time <= ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 17:57:18.820 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-04 17:57:27.262 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-04 17:57:27.328 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-04 17:57:27.931 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 17:57:27.932 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 17:57:29.654 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-08-04 17:57:29.702 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-04 17:57:30.891 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 17:57:30.895 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 17:57:30.935 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-08-04 17:57:31.142 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-04 17:57:31.469 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=1f84fbf2-8682-3e7c-a339-07a412e035e5
2025-08-04 17:57:31.596 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:57:31.597 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:57:31.597 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:57:31.598 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:57:31.601 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:57:31.605 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:57:32.202 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$7d5aa4e0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:57:33.096 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-04 17:57:33.097 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3377 ms
2025-08-04 17:58:29.045 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-04 17:58:29.108 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-04 17:58:29.711 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 17:58:29.712 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 17:58:31.440 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-08-04 17:58:31.490 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-04 17:58:32.690 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 17:58:32.695 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 17:58:32.733 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-08-04 17:58:32.937 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-04 17:58:33.252 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=1f84fbf2-8682-3e7c-a339-07a412e035e5
2025-08-04 17:58:33.371 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:58:33.372 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:58:33.372 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/564569236] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:58:33.373 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:58:33.376 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:58:33.380 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:58:33.979 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$e3dbf60f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:58:34.822 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-04 17:58:34.823 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3313 ms
2025-08-04 17:58:45.449 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-04 17:58:51.221 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:58:51.319 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:58:51.333 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:58:52.180 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-04 17:58:52.181 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:58:52.181 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-04 17:58:58.630 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:58:58.665 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:58:59.279 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-04 17:59:00.435 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 17:59:02.661 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 17:59:09.647 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:59:10.438 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-04 17:59:10.483 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-08-04 17:59:10.912 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-04 17:59:12.620 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-04 17:59:12.673 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-04 17:59:12.673 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-04 17:59:12.684 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-04 17:59:12.687 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-04 17:59:12.687 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-04 17:59:12.687 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-04 17:59:12.687 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7d972029
2025-08-04 17:59:15.918 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-04 17:59:16.814 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@61dc8a64[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$d3e57b3c#mpcWalletEventRetryNotifyTask]
2025-08-04 17:59:16.814 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1e2f67e9[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$5e3356cc#organizationFeeMonthlyReportTask]
2025-08-04 17:59:16.814 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@16c230c[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$25306dbe#organizationSMSFeeCalculateTask]
2025-08-04 17:59:16.815 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7d151d89[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$b18301f7#syncCustomerInfoTask]
2025-08-04 17:59:16.829 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:59:16.849 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:59:22.228 [Thread-135] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-08-04 17:59:28.482 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:59:28.484 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-04 17:59:39.654 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:59:39.655 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-04 17:59:50.844 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:59:50.844 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-04 18:00:02.022 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:00:02.023 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-08-04 18:00:10.210 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:00:10.210 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-08-04 18:00:10.347 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-04 18:00:10.383 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-04 18:00:10.400 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-04 18:00:10.491 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-04 18:00:10.585 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-08-04 18:00:10.624 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 18:00:10.624 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 18:00:11.179 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-08-04 18:00:11.182 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-04 18:00:11.182 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-04 18:00:11.224 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 102.492 seconds (JVM running for 108.931)
2025-08-04 18:00:11.256 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-08-04 18:00:11.257 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-08-04 18:00:11.257 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-08-04 18:00:11.599 [RMI TCP Connection(4)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 18:00:11.599 [RMI TCP Connection(4)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-04 18:00:11.615 [RMI TCP Connection(4)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 15 ms
2025-08-04 18:00:35.371 [XNIO-1 task-2] INFO  [ b6aab1de1e193659 , b6aab1de1e193659 ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 1100 ms
2025-08-04 18:00:54.009 [XNIO-1 task-2] INFO  [ 189b0200fdc02143 , 189b0200fdc02143 ] c.k.l.c.s.o.OrganizationCardManagementService.getOrganizationCustomerCardList:41 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='12090276', createDateFrom=2025-07-01, createDateUntil=2025-07-31, cardId='null', customerId='23', cardStatus='null'}
2025-08-04 18:00:55.101 [XNIO-1 task-2] INFO  [ 189b0200fdc02143 , 189b0200fdc02143 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info WHERE (organization_no = ? AND customer_id = ? AND create_time >= ? AND create_time <= ?)
2025-08-04 18:00:55.101 [XNIO-1 task-2] INFO  [ 189b0200fdc02143 , 189b0200fdc02143 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 18:00:55.102 [XNIO-1 task-2] INFO  [ 189b0200fdc02143 , 189b0200fdc02143 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_4 WHERE (organization_no = ? AND customer_id = ? AND create_time >= ? AND create_time <= ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 18:01:16.110 [XNIO-1 task-2] INFO  [ 5673e29c0a1dc0c2 , 5673e29c0a1dc0c2 ] c.k.l.c.s.o.OrganizationCardManagementService.getOrganizationCustomerCardList:41 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='12090276', createDateFrom=2025-07-01, createDateUntil=2025-07-31, cardId='null', customerId='23', cardStatus='null'}
2025-08-04 18:01:16.133 [XNIO-1 task-2] INFO  [ 5673e29c0a1dc0c2 , 5673e29c0a1dc0c2 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info WHERE (organization_no = ? AND customer_id = ? AND create_time >= ? AND create_time <= ?)
2025-08-04 18:01:16.134 [XNIO-1 task-2] INFO  [ 5673e29c0a1dc0c2 , 5673e29c0a1dc0c2 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 18:01:16.134 [XNIO-1 task-2] INFO  [ 5673e29c0a1dc0c2 , 5673e29c0a1dc0c2 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_4 WHERE (organization_no = ? AND customer_id = ? AND create_time >= ? AND create_time <= ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 18:01:16.320 [XNIO-1 task-2] INFO  [ 8b436e27781c6dc3 , 8b436e27781c6dc3 ] c.k.l.c.s.o.OrganizationCardManagementService.getOrganizationCustomerCardList:41 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='12090276', createDateFrom=2025-07-01, createDateUntil=2025-07-31, cardId='null', customerId='23', cardStatus='null'}
2025-08-04 18:01:16.333 [XNIO-1 task-2] INFO  [ 8b436e27781c6dc3 , 8b436e27781c6dc3 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info WHERE (organization_no = ? AND customer_id = ? AND create_time >= ? AND create_time <= ?)
2025-08-04 18:01:16.334 [XNIO-1 task-2] INFO  [ 8b436e27781c6dc3 , 8b436e27781c6dc3 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 18:01:16.334 [XNIO-1 task-2] INFO  [ 8b436e27781c6dc3 , 8b436e27781c6dc3 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_4 WHERE (organization_no = ? AND customer_id = ? AND create_time >= ? AND create_time <= ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 18:01:30.789 [XNIO-1 task-2] INFO  [ f9290fb8c7cae10c , f9290fb8c7cae10c ] c.k.l.c.s.o.OrganizationCardManagementService.getOrganizationCustomerCardList:41 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='12090276', createDateFrom=2025-07-01, createDateUntil=2025-07-31, cardId='null', customerId='23', cardStatus='null'}
2025-08-04 18:01:30.809 [XNIO-1 task-2] INFO  [ f9290fb8c7cae10c , f9290fb8c7cae10c ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info WHERE (organization_no = ? AND customer_id = ? AND create_time >= ? AND create_time <= ?)
2025-08-04 18:01:30.810 [XNIO-1 task-2] INFO  [ f9290fb8c7cae10c , f9290fb8c7cae10c ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 18:01:30.811 [XNIO-1 task-2] INFO  [ f9290fb8c7cae10c , f9290fb8c7cae10c ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_4 WHERE (organization_no = ? AND customer_id = ? AND create_time >= ? AND create_time <= ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 18:01:54.225 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-04 18:02:09.270 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-04 18:02:09.335 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-04 18:02:09.956 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 18:02:09.956 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 18:02:11.699 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-08-04 18:02:11.748 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-04 18:02:12.926 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 18:02:12.930 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 18:02:12.972 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-08-04 18:02:13.196 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-04 18:02:13.529 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=1f84fbf2-8682-3e7c-a339-07a412e035e5
2025-08-04 18:02:13.656 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 18:02:13.657 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 18:02:13.658 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 18:02:13.659 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 18:02:13.661 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 18:02:13.666 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 18:02:14.325 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$7d5aa4e0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 18:02:15.247 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-04 18:02:15.248 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3480 ms
2025-08-04 18:02:25.988 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-04 18:02:30.846 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 18:02:30.947 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 18:02:30.962 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 18:02:31.832 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-04 18:02:31.832 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 18:02:31.832 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-04 18:02:38.302 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 18:02:38.336 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 18:02:38.977 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-04 18:02:40.215 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 18:02:44.596 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 18:02:51.588 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 18:02:52.225 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-04 18:02:52.270 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-08-04 18:02:52.688 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-04 18:02:54.339 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-04 18:02:54.388 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-04 18:02:54.389 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-04 18:02:54.400 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-04 18:02:54.402 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-04 18:02:54.402 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-04 18:02:54.403 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-04 18:02:54.403 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7ef60643
2025-08-04 18:02:57.599 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-04 18:02:58.352 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@98be09f[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$ffda90#mpcWalletEventRetryNotifyTask]
2025-08-04 18:02:58.352 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@65f85228[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$8b4db620#organizationFeeMonthlyReportTask]
2025-08-04 18:02:58.353 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@222456bc[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$524acd12#organizationSMSFeeCalculateTask]
2025-08-04 18:02:58.353 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2acec770[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$de9d614b#syncCustomerInfoTask]
2025-08-04 18:02:58.367 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 18:02:58.386 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 18:03:03.776 [Thread-137] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-08-04 18:03:10.018 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:03:10.019 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-04 18:03:21.186 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:03:21.186 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-04 18:03:32.348 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:03:32.350 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-04 18:03:43.505 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:03:43.506 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-08-04 18:03:51.664 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:03:51.665 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-08-04 18:03:51.783 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-04 18:03:51.819 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-04 18:03:51.836 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-04 18:03:51.922 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-04 18:03:52.015 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8081 (http)
2025-08-04 18:03:52.053 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 18:03:52.053 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 18:03:52.591 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8081 register finished
2025-08-04 18:03:52.596 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-04 18:03:52.596 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-04 18:03:52.633 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 103.678 seconds (JVM running for 109.932)
2025-08-04 18:03:52.665 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-08-04 18:03:52.665 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-08-04 18:03:52.666 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-08-04 18:03:53.110 [RMI TCP Connection(1)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 18:03:53.110 [RMI TCP Connection(1)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-04 18:03:53.117 [RMI TCP Connection(1)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 7 ms
2025-08-04 18:04:03.525 [XNIO-1 task-3] INFO  [ 5ac9229ddfa6923c , 5ac9229ddfa6923c ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 1060 ms
2025-08-04 18:05:17.611 [XNIO-1 task-3] INFO  [ e365e6b28c2c108b , e365e6b28c2c108b ] c.k.l.c.s.o.OrganizationCardManagementService.getOrganizationCustomerCardList:41 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='12090276', createDateFrom=2025-07-01, createDateUntil=2025-07-31, cardId='null', customerId='23', cardStatus='null'}
2025-08-04 18:05:18.618 [XNIO-1 task-3] INFO  [ e365e6b28c2c108b , e365e6b28c2c108b ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info WHERE (organization_no = ? AND customer_id = ? AND create_time >= ? AND create_time <= ?)
2025-08-04 18:05:18.618 [XNIO-1 task-3] INFO  [ e365e6b28c2c108b , e365e6b28c2c108b ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 18:05:18.618 [XNIO-1 task-3] INFO  [ e365e6b28c2c108b , e365e6b28c2c108b ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_4 WHERE (organization_no = ? AND customer_id = ? AND create_time >= ? AND create_time <= ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 18:06:06.587 [XNIO-1 task-3] INFO  [ 962f3816320a30fa , 962f3816320a30fa ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_card_recharge_detail WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ?)
2025-08-04 18:06:06.587 [XNIO-1 task-3] INFO  [ 962f3816320a30fa , 962f3816320a30fa ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 18:06:06.587 [XNIO-1 task-3] INFO  [ 962f3816320a30fa , 962f3816320a30fa ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_card_recharge_detail_2025Q3 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 18:06:06.714 [XNIO-1 task-3] INFO  [ 962f3816320a30fa , 962f3816320a30fa ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,business_type,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ?) ORDER BY recharge_datetime DESC
 LIMIT ? 
2025-08-04 18:06:06.715 [XNIO-1 task-3] INFO  [ 962f3816320a30fa , 962f3816320a30fa ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@56bc88e1], lock=Optional.empty, window=Optional.empty)
2025-08-04 18:06:06.715 [XNIO-1 task-3] INFO  [ 962f3816320a30fa , 962f3816320a30fa ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,business_type,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail_2025Q3 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ?) ORDER BY recharge_datetime DESC
 LIMIT ?  ::: [12090276, 23, 2025-07-01T00:00, 2025-07-31T23:59:59, 100]
2025-08-04 18:06:23.742 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] ShardingSphere-SQL.log:74 - Logic SQL: INSERT INTO kl_export_file_record  ( file_record_id,
organization_no,
file_name,
file_type,


file_status,

create_time,
update_time )  VALUES  ( ?,
?,
?,
?,


?,

?,
? )
2025-08-04 18:06:23.742 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLInsertStatement(setAssignment=Optional.empty, onDuplicateKeyColumns=Optional.empty)
2025-08-04 18:06:23.742 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: INSERT INTO kl_export_file_record  ( file_record_id,
organization_no,
file_name,
file_type,


file_status,

create_time,
update_time )  VALUES  (?, ?, ?, ?, ?, ?, ?) ::: [1952310137851478018, 12090276, Topup_12090276_20250804180623.csv, CARD_RECHARGE_EXPORT, PROCESSING, 2025-08-04 18:06:23.0, 2025-08-04 18:06:23.0]
2025-08-04 18:06:23.927 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] c.k.l.c.service.export.ExportFileRecordService.createFileRecord:35 - 创建文件记录成功，文件记录ID: 1952310137851478018, 文件名: Topup_12090276_20250804180623.csv
2025-08-04 18:06:23.928 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] c.k.l.c.service.export.CardRechargeExportService.asyncExportData:84 - 开始异步导出卡充值记录数据，文件记录ID: 1952310137851478018
2025-08-04 18:06:23.934 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,business_type,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ?) ORDER BY recharge_datetime DESC
2025-08-04 18:06:23.934 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 18:06:23.934 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,business_type,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail_2025Q3 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime >= ? AND recharge_datetime <= ?) ORDER BY recharge_datetime DESC ::: [12090276, 23, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 18:06:24.047 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] c.k.l.c.service.export.CardRechargeExportService.asyncExportData:88 - 查询到 10 条卡充值记录数据
2025-08-04 18:06:24.093 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] c.k.l.c.service.export.CardRechargeExportService.asyncExportData:92 - CSV文件生成完成，文件大小: 1839 bytes
2025-08-04 18:06:26.998 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] c.k.l.c.service.export.CardRechargeExportService.asyncExportData:96 - 文件上传S3成功，URL: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/customer/cardRecharge/202508/Topup_12090276_20250804180623.csv
2025-08-04 18:06:27.024 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] ShardingSphere-SQL.log:74 - Logic SQL: UPDATE kl_export_file_record  SET file_size=?,
s3_url=?,
file_status=?,


update_time=?  WHERE file_record_id=?
2025-08-04 18:06:27.024 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLUpdateStatement(orderBy=Optional.empty, limit=Optional.empty)
2025-08-04 18:06:27.025 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: UPDATE kl_export_file_record  SET file_size=?,
s3_url=?,
file_status=?,


update_time=?  WHERE file_record_id=? ::: [1839, https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/customer/cardRecharge/202508/Topup_12090276_20250804180623.csv, SUCCESS, 2025-08-04 18:06:26.0, 1952310137851478018]
2025-08-04 18:06:27.179 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] c.k.l.c.service.export.ExportFileRecordService.updateFileRecordSuccess:51 - 更新文件记录为成功状态，文件记录ID: 1952310137851478018, S3 URL: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/customer/cardRecharge/202508/Topup_12090276_20250804180623.csv
2025-08-04 18:06:27.180 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] c.k.l.c.service.export.CardRechargeExportService.asyncExportData:100 - 卡充值记录导出任务完成，文件记录ID: 1952310137851478018
2025-08-04 18:06:27.180 [XNIO-1 task-3] INFO  [ 3502703012612954 , 3502703012612954 ] c.k.l.c.service.export.CardRechargeExportService.asyncExportData:105 - 临时文件删除成功: /var/folders/s_/n4rjfkbj7s13rzhsr4yxs34c0000gp/T/recharge_export_4939641242686315620.csv
2025-08-04 18:06:36.516 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-04 18:07:09.184 [SpringApplicationShutdownHook] INFO  [  ,  ] io.undertow.stop:259 - stopping server: Undertow - 2.2.28.Final
2025-08-04 18:07:09.198 [SpringApplicationShutdownHook] INFO  [  ,  ] io.undertow.servlet.log:389 - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04 18:07:09.202 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:07:09.203 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:07:09.203 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:07:09.203 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:07:09.203 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:07:09.226 [SpringApplicationShutdownHook] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.destroy:847 - Shutting down Quartz Scheduler
2025-08-04 18:07:09.226 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:666 - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-04 18:07:09.227 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-04 18:07:09.227 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:740 - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-04 18:07:09.231 [SpringApplicationShutdownHook] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.deregister:95 - De-registering from Nacos Server now...
2025-08-04 18:07:09.268 [SpringApplicationShutdownHook] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.deregister:115 - De-registration finished.
2025-08-04 18:07:09.281 [Thread-137] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:91 - >>>>>>>>>>> xxl-job remoting server stop.
2025-08-04 18:07:09.352 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:87 - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='kun-linkage-customer-executor', registryValue='http://172.19.151.145:16661/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-08-04 18:07:09.353 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:105 - >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-08-04 18:07:09.353 [SpringApplicationShutdownHook] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.stop:117 - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-08-04 18:07:09.353 [xxl-job, executor JobLogFileCleanThread] INFO  [  ,  ] com.xxl.job.core.thread.JobLogFileCleanThread.run:99 - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-08-04 18:07:09.353 [xxl-job, executor TriggerCallbackThread] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:98 - >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-08-04 18:07:09.354 [Thread-127] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:128 - >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-08-04 18:07:15.379 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-04 18:07:15.380 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-08-04 18:07:21.392 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-08-04 18:07:21.394 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-08-04 18:07:21.545 [SpringApplicationShutdownHook] INFO  [  ,  ] com.kun.linkage.customer.config.AsyncConfig.destroy:152 - 开始销毁外部API异步调用线程池...
2025-08-04 18:07:21.545 [SpringApplicationShutdownHook] INFO  [  ,  ] com.kun.linkage.customer.config.AsyncConfig.destroy:185 - 线程池已经关闭，无需重复销毁
2025-08-04 18:07:21.545 [SpringApplicationShutdownHook] INFO  [  ,  ] com.kun.linkage.customer.config.AsyncConfig.destroy:152 - 开始销毁外部API异步调用线程池...
2025-08-04 18:07:21.545 [SpringApplicationShutdownHook] INFO  [  ,  ] com.kun.linkage.customer.config.AsyncConfig.destroy:185 - 线程池已经关闭，无需重复销毁
2025-08-04 18:07:21.546 [SpringApplicationShutdownHook] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-08-04 18:07:21.554 [SpringApplicationShutdownHook] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-08-04 18:07:29.132 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-04 18:07:29.202 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-04 18:07:29.809 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 18:07:29.810 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 18:07:31.544 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-08-04 18:07:31.596 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-04 18:07:32.786 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 18:07:32.790 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 18:07:32.850 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-08-04 18:07:33.084 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-04 18:07:33.405 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=1f84fbf2-8682-3e7c-a339-07a412e035e5
2025-08-04 18:07:33.527 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 18:07:33.528 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 18:07:33.528 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 18:07:33.529 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 18:07:33.532 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 18:07:33.536 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 18:07:34.134 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$22071bdc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 18:07:35.002 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-04 18:07:35.003 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3389 ms
2025-08-04 18:07:45.443 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-04 18:07:50.967 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 18:07:51.065 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 18:07:51.081 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 18:07:51.935 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-04 18:07:51.935 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 18:07:51.935 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-04 18:07:58.398 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 18:07:58.433 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 18:07:59.069 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-04 18:08:00.403 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 18:08:02.925 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 18:08:09.990 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 18:08:10.681 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-04 18:08:10.724 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-08-04 18:08:11.141 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-04 18:08:12.869 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-04 18:08:12.922 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-04 18:08:12.922 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-04 18:08:12.934 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-04 18:08:12.936 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-04 18:08:12.936 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-04 18:08:12.937 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-04 18:08:12.937 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@66752456
2025-08-04 18:08:16.492 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-04 18:08:17.321 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@48667fd3[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$560f7940#mpcWalletEventRetryNotifyTask]
2025-08-04 18:08:17.322 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@42acca1a[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$e05d54d0#organizationFeeMonthlyReportTask]
2025-08-04 18:08:17.322 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6ac3ceda[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$a75a6bc2#organizationSMSFeeCalculateTask]
2025-08-04 18:08:17.323 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4a26a54b[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$33acfffb#syncCustomerInfoTask]
2025-08-04 18:08:17.338 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 18:08:17.360 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 18:08:22.847 [Thread-137] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-08-04 18:08:29.256 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:08:29.257 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-04 18:08:40.415 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:08:40.416 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-04 18:08:51.566 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:08:51.567 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-04 18:09:02.741 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:09:02.742 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-08-04 18:09:10.922 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 18:09:10.923 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-08-04 18:09:11.063 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-04 18:09:11.102 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-04 18:09:11.120 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-04 18:09:11.205 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-04 18:09:11.302 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8081 (http)
2025-08-04 18:09:11.343 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 18:09:11.344 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 18:09:11.892 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8081 register finished
2025-08-04 18:09:11.898 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-04 18:09:11.898 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-04 18:09:11.937 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 103.137 seconds (JVM running for 109.412)
2025-08-04 18:09:11.969 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-08-04 18:09:11.969 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-08-04 18:09:11.969 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-08-04 18:09:12.483 [RMI TCP Connection(5)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 18:09:12.484 [RMI TCP Connection(5)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-04 18:09:12.495 [RMI TCP Connection(5)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 11 ms
2025-08-04 18:12:54.567 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.

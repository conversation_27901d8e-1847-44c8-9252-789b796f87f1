2025-08-04 17:06:30.734 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-04 17:06:30.800 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-04 17:06:31.416 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 17:06:31.416 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 17:06:33.153 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-08-04 17:06:33.202 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-04 17:06:34.458 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 17:06:34.463 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 17:06:34.503 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-08-04 17:06:34.710 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-04 17:06:35.022 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=e71913b0-8fc4-3ff7-bf63-17db799ab456
2025-08-04 17:06:35.148 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:06:35.149 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:06:35.150 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:06:35.150 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:06:35.153 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:06:35.157 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:06:35.752 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$7d5aa4e0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:06:36.628 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-04 17:06:36.628 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3406 ms
2025-08-04 17:06:47.696 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-04 17:06:56.550 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:06:56.650 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:06:56.665 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:06:57.519 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-04 17:06:57.519 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:06:57.519 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-04 17:07:04.017 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:07:04.054 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:07:04.666 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-04 17:07:06.763 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 17:07:09.993 [redisson-netty-2-19] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 17:07:17.142 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:07:17.746 [main] INFO  [  ,  ] com.kun.linkage.customer.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-04 17:07:17.790 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-08-04 17:07:18.208 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-04 17:07:19.872 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-04 17:07:19.921 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-04 17:07:19.921 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-04 17:07:19.931 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-04 17:07:19.934 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-04 17:07:19.934 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-04 17:07:19.934 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-04 17:07:19.935 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7bca3747
2025-08-04 17:07:23.142 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-08-04 17:07:23.925 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@33c4fc94[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$96160e44#mpcWalletEventRetryNotifyTask]
2025-08-04 17:07:23.925 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7ad9ed91[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$2063e9d4#organizationFeeMonthlyReportTask]
2025-08-04 17:07:23.926 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@60362b7[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$e76100c6#organizationSMSFeeCalculateTask]
2025-08-04 17:07:23.926 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@18809db8[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$73b394ff#syncCustomerInfoTask]
2025-08-04 17:07:23.940 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:07:23.959 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 17:07:29.349 [Thread-146] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-08-04 17:07:35.601 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:07:35.602 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-04 17:07:46.796 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:07:46.799 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-04 17:07:57.986 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:07:57.987 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-04 17:08:09.167 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:08:09.169 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-08-04 17:08:17.354 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 17:08:17.354 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-08-04 17:08:17.509 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-04 17:08:17.548 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-04 17:08:17.571 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-04 17:08:17.659 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-04 17:08:17.765 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-08-04 17:08:17.809 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 17:08:17.809 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 17:08:18.272 [XNIO-1 task-1] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 17:08:18.272 [XNIO-1 task-1] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-04 17:08:18.275 [XNIO-1 task-1] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 3 ms
2025-08-04 17:08:18.721 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-08-04 17:08:18.740 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-04 17:08:18.740 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-04 17:08:18.858 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 108.465 seconds (JVM running for 114.851)
2025-08-04 17:08:19.006 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-08-04 17:08:19.007 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-08-04 17:08:19.008 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-08-04 17:08:25.583 [XNIO-1 task-1] INFO  [ 03a843a5138f47e5 , 03a843a5138f47e5 ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 1124 ms
2025-08-04 17:09:29.454 [XNIO-1 task-1] INFO  [ baf71ae8db02fb2f , baf71ae8db02fb2f ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_fee_detail WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
2025-08-04 17:09:29.455 [XNIO-1 task-1] INFO  [ baf71ae8db02fb2f , baf71ae8db02fb2f ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:09:29.455 [XNIO-1 task-1] INFO  [ baf71ae8db02fb2f , baf71ae8db02fb2f ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_fee_detail_202505 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) UNION ALL SELECT count(0) FROM kl_organization_fee_detail_202506 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) UNION ALL SELECT count(0) FROM kl_organization_fee_detail_202507 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) UNION ALL SELECT count(0) FROM kl_organization_fee_detail_202508 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ::: [null, 2025-01-01T00:00, 2025-08-04T23:59:59, null, 2025-01-01T00:00, 2025-08-04T23:59:59, null, 2025-01-01T00:00, 2025-08-04T23:59:59, null, 2025-01-01T00:00, 2025-08-04T23:59:59]
2025-08-04 17:10:23.938 [XNIO-1 task-1] INFO  [ dbdfee223ff2bd6f , dbdfee223ff2bd6f ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_fee_detail WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
2025-08-04 17:10:23.939 [XNIO-1 task-1] INFO  [ dbdfee223ff2bd6f , dbdfee223ff2bd6f ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:10:23.939 [XNIO-1 task-1] INFO  [ dbdfee223ff2bd6f , dbdfee223ff2bd6f ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_fee_detail_202507 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ::: [null, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 17:11:28.292 [XNIO-1 task-1] INFO  [ 48992684b71da077 , 48992684b71da077 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_fee_detail WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
2025-08-04 17:11:28.293 [XNIO-1 task-1] INFO  [ 48992684b71da077 , 48992684b71da077 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:11:28.293 [XNIO-1 task-1] INFO  [ 48992684b71da077 , 48992684b71da077 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_fee_detail_202507 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ::: [null, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 17:11:54.018 [XNIO-1 task-1] INFO  [ d4bfa173b9375707 , d4bfa173b9375707 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_fee_detail WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
2025-08-04 17:11:54.020 [XNIO-1 task-1] INFO  [ d4bfa173b9375707 , d4bfa173b9375707 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:11:54.020 [XNIO-1 task-1] INFO  [ d4bfa173b9375707 , d4bfa173b9375707 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_fee_detail_202507 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ::: [12090276, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 17:11:54.189 [XNIO-1 task-1] INFO  [ d4bfa173b9375707 , d4bfa173b9375707 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,card_product_code,calculate_datetime,transaction_datetime,related_transaction_id,fee_type,fee_collection_method,transaction_amount,transaction_currency_code,transaction_currency_precision,fee_amount,fx_rate,deduct_processor,deduct_currency_code,deduct_currency_precision,deduct_fee_amount,deduct_request_no,remark,fee_collection_status,snapshot_billing_dimension,snapshot_min_amount,snapshot_max_amount,snapshot_proportion_rate,snapshot_proportion_min_amount,snapshot_proportion_max_amount,snapshot_fixed_amount,call_count,create_time,last_modify_time  FROM kl_organization_fee_detail 
 
 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ORDER BY create_time DESC
 LIMIT ? 
2025-08-04 17:11:54.189 [XNIO-1 task-1] INFO  [ d4bfa173b9375707 , d4bfa173b9375707 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@325fb8a4], lock=Optional.empty, window=Optional.empty)
2025-08-04 17:11:54.189 [XNIO-1 task-1] INFO  [ d4bfa173b9375707 , d4bfa173b9375707 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,card_product_code,calculate_datetime,transaction_datetime,related_transaction_id,fee_type,fee_collection_method,transaction_amount,transaction_currency_code,transaction_currency_precision,fee_amount,fx_rate,deduct_processor,deduct_currency_code,deduct_currency_precision,deduct_fee_amount,deduct_request_no,remark,fee_collection_status,snapshot_billing_dimension,snapshot_min_amount,snapshot_max_amount,snapshot_proportion_rate,snapshot_proportion_min_amount,snapshot_proportion_max_amount,snapshot_fixed_amount,call_count,create_time,last_modify_time  FROM kl_organization_fee_detail_202507 
 
 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ORDER BY create_time DESC
 LIMIT ?  ::: [12090276, 2025-07-01T00:00, 2025-07-31T23:59:59, 100]
2025-08-04 17:12:10.555 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - Logic SQL: INSERT INTO kl_export_file_record  ( file_record_id,
organization_no,
file_name,
file_type,


file_status,

create_time,
update_time )  VALUES  ( ?,
?,
?,
?,


?,

?,
? )
2025-08-04 17:12:10.555 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLInsertStatement(setAssignment=Optional.empty, onDuplicateKeyColumns=Optional.empty)
2025-08-04 17:12:10.555 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: INSERT INTO kl_export_file_record  ( file_record_id,
organization_no,
file_name,
file_type,


file_status,

create_time,
update_time )  VALUES  (?, ?, ?, ?, ?, ?, ?) ::: [1952296492996259841, 12090276, Settlement_12090276_20250804171210.csv, ORGANIZATION_FEE_EXPORT, PROCESSING, 2025-08-04 17:12:10.0, 2025-08-04 17:12:10.0]
2025-08-04 17:12:10.744 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] c.k.l.c.service.export.ExportFileRecordService.createFileRecord:40 - 创建文件记录成功，文件记录ID: 1952296492996259841, 文件名: Settlement_12090276_20250804171210.csv
2025-08-04 17:12:10.745 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] c.k.l.c.s.e.OrganizationFeeDetailExportService.asyncExportData:80 - 开始异步导出手续费明细数据，文件记录ID: 1952296492996259841
2025-08-04 17:12:10.753 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,card_product_code,calculate_datetime,transaction_datetime,related_transaction_id,fee_type,fee_collection_method,transaction_amount,transaction_currency_code,transaction_currency_precision,fee_amount,fx_rate,deduct_processor,deduct_currency_code,deduct_currency_precision,deduct_fee_amount,deduct_request_no,remark,fee_collection_status,snapshot_billing_dimension,snapshot_min_amount,snapshot_max_amount,snapshot_proportion_rate,snapshot_proportion_min_amount,snapshot_proportion_max_amount,snapshot_fixed_amount,call_count,create_time,last_modify_time  FROM kl_organization_fee_detail 
 
 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ORDER BY create_time DESC
2025-08-04 17:12:10.753 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 17:12:10.753 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,card_product_code,calculate_datetime,transaction_datetime,related_transaction_id,fee_type,fee_collection_method,transaction_amount,transaction_currency_code,transaction_currency_precision,fee_amount,fx_rate,deduct_processor,deduct_currency_code,deduct_currency_precision,deduct_fee_amount,deduct_request_no,remark,fee_collection_status,snapshot_billing_dimension,snapshot_min_amount,snapshot_max_amount,snapshot_proportion_rate,snapshot_proportion_min_amount,snapshot_proportion_max_amount,snapshot_fixed_amount,call_count,create_time,last_modify_time  FROM kl_organization_fee_detail_202507 
 
 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ORDER BY create_time DESC ::: [12090276, 2025-07-01T00:00, 2025-07-31T23:59:59]
2025-08-04 17:12:10.864 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] c.k.l.c.s.e.OrganizationFeeDetailExportService.asyncExportData:84 - 查询到 16 条手续费明细数据
2025-08-04 17:12:10.909 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] c.k.l.c.s.e.OrganizationFeeDetailExportService.asyncExportData:88 - CSV文件生成完成，文件大小: 2522 bytes
2025-08-04 17:12:13.767 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] c.k.l.c.s.e.OrganizationFeeDetailExportService.asyncExportData:92 - 文件上传S3成功，URL: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/customer/fee-reports/organization/202508/Settlement_12090276_20250804171210.csv
2025-08-04 17:12:13.792 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - Logic SQL: UPDATE kl_export_file_record  SET file_size=?,
s3_url=?,
file_status=?,


update_time=?  WHERE file_record_id=?
2025-08-04 17:12:13.792 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLUpdateStatement(orderBy=Optional.empty, limit=Optional.empty)
2025-08-04 17:12:13.793 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: UPDATE kl_export_file_record  SET file_size=?,
s3_url=?,
file_status=?,


update_time=?  WHERE file_record_id=? ::: [2522, https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/customer/fee-reports/organization/202508/Settlement_12090276_20250804171210.csv, SUCCESS, 2025-08-04 17:12:13.0, 1952296492996259841]
2025-08-04 17:12:13.954 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] c.k.l.c.service.export.ExportFileRecordService.updateFileRecordSuccess:56 - 更新文件记录为成功状态，文件记录ID: 1952296492996259841, S3 URL: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/customer/fee-reports/organization/202508/Settlement_12090276_20250804171210.csv
2025-08-04 17:12:13.954 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] c.k.l.c.s.e.OrganizationFeeDetailExportService.asyncExportData:96 - 手续费明细导出任务完成，文件记录ID: 1952296492996259841
2025-08-04 17:12:13.955 [XNIO-1 task-1] INFO  [ dccff57562c9457e , dccff57562c9457e ] c.k.l.c.s.e.OrganizationFeeDetailExportService.asyncExportData:101 - 临时文件删除成功: /var/folders/s_/n4rjfkbj7s13rzhsr4yxs34c0000gp/T/fee_export_5711474392184781733.csv
2025-08-04 17:16:54.885 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.

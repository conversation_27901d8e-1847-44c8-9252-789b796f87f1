2025-07-30 14:15:48.185 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-30 14:15:48.254 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 14:15:48.883 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 14:15:48.883 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 14:15:50.647 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing,DEFAULT_GROUP'}]
2025-07-30 14:15:50.678 [main] INFO  [  ,  ] c.k.linkage.clearing.KunLinkageClearingApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-30 14:15:51.930 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 14:15:51.935 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 14:15:51.970 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-07-30 14:15:52.172 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-30 14:15:52.502 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=bbb2ad06-7a8d-3098-b15b-2d3747938be3
2025-07-30 14:15:52.629 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:15:52.630 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:15:52.631 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$542/451768075] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:15:52.631 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:15:52.634 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:15:52.638 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:15:53.265 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$620f49b3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:15:54.272 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-30 14:15:54.273 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3584 ms
2025-07-30 14:16:06.551 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-30 14:16:17.774 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-30 14:16:17.774 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-30 14:16:17.774 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-30 14:16:24.779 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:16:25.506 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-30 14:16:26.799 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-30 14:16:30.157 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-30 14:16:30.932 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:74
2025-07-30 14:16:36.673 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:16:36.698 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:16:38.560 [main] INFO  [  ,  ] com.kun.linkage.clearing.config.AsyncConfiguration.clearingTaskExecutor:64 - 清算任务执行器初始化完成，核心线程数: 5, 最大线程数: 20, 队列容量: 200
2025-07-30 14:16:38.596 [main] INFO  [  ,  ] c.kun.linkage.clearing.config.XxlJobConfiguration.xxlJobExecutor:33 - >>>>>>>>>>> xxl-job config init.
2025-07-30 14:16:39.044 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-30 14:16:40.432 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-30 14:16:40.483 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 14:16:40.483 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-30 14:16:40.494 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-30 14:16:40.496 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 14:16:40.497 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 14:16:40.497 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-30 14:16:40.497 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4bc2cb11
2025-07-30 14:16:40.632 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:16:40.632 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:16:43.816 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 14:16:44.846 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:KLClearingTrigger, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@43f88cba[class com.kun.linkage.clearing.task.KLClearingTrigger$$EnhancerBySpringCGLIB$$9bc8b15d#triggerClearing]
2025-07-30 14:16:44.847 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:visaBase05DataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@59c68ed3[class com.kun.linkage.clearing.task.VisaBase05DataTask$$EnhancerBySpringCGLIB$$187c5769#visaBase05DataTask]
2025-07-30 14:16:44.847 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:visaBaseFileTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3cf6f3aa[class com.kun.linkage.clearing.task.VisaBaseFileTask$$EnhancerBySpringCGLIB$$c11d8470#ywClearingFileTask]
2025-07-30 14:16:44.860 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-clearing' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:16:44.908 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:16:44.930 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-auth' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:16:44.981 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-auth' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:16:44.993 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:16:45.016 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:16:50.492 [Thread-149] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 18084
2025-07-30 14:16:53.960 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:16:56.965 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='BPC_VISA_BASE_FILE_33_GROUP', nameServer='mq.dev.kun:9876', topic='BPC_VISA_BASE_FILE_33_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-30 14:16:56.965 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:16:56.966 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:bpcBaseFile33Listener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-30 14:16:56.975 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:16:59.982 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:17:05.295 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:17:08.299 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:17:08.299 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:17:08.300 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='BPC_VISA_BASE_FILE_05_GROUP', nameServer='mq.dev.kun:9876', topic='BPC_VISA_BASE_FILE_05_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-30 14:17:08.301 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:bpcBaseFile05Listener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-30 14:17:08.432 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-30 14:17:08.469 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-30 14:17:08.486 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-30 14:17:08.578 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-30 14:17:08.673 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 9020 (http)
2025-07-30 14:17:08.713 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 14:17:08.713 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 14:17:09.276 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-clearing **************:9020 register finished
2025-07-30 14:17:09.281 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-30 14:17:09.281 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 14:17:09.311 [main] INFO  [  ,  ] c.k.linkage.clearing.KunLinkageClearingApplication.logStarted:61 - Started KunLinkageClearingApplication in 81.47 seconds (JVM running for 88.115)
2025-07-30 14:17:09.344 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-clearing, group=DEFAULT_GROUP
2025-07-30 14:17:09.345 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-clearing-local.properties, group=DEFAULT_GROUP
2025-07-30 14:17:09.345 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-clearing.properties, group=DEFAULT_GROUP
2025-07-30 14:17:09.474 [RMI TCP Connection(4)-**************] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 14:17:09.474 [RMI TCP Connection(4)-**************] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-30 14:17:09.483 [RMI TCP Connection(4)-**************] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 8 ms
2025-07-30 14:17:10.624 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:17:11.305 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:17:22.992 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:17:23.001 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:17:25.996 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:17:34.307 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:17:34.310 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:17:37.311 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:17:40.625 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:17:40.632 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:17:49.006 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:17:52.009 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:17:52.011 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:18:00.320 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:18:02.701 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 14:18:03.322 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:18:03.323 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:18:18.923 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-30 14:18:18.986 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 14:18:19.603 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 14:18:19.603 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 14:18:21.341 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing,DEFAULT_GROUP'}]
2025-07-30 14:18:21.393 [main] INFO  [  ,  ] c.k.linkage.clearing.KunLinkageClearingApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-30 14:18:22.551 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 14:18:22.555 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 14:18:22.589 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-07-30 14:18:22.797 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-30 14:18:23.125 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=bbb2ad06-7a8d-3098-b15b-2d3747938be3
2025-07-30 14:18:23.263 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:18:23.264 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:18:23.265 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$542/451768075] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:18:23.266 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:18:23.270 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:18:23.275 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:18:23.902 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$620f49b3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:18:24.839 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-30 14:18:24.839 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3432 ms
2025-07-30 14:18:37.730 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-30 14:18:48.522 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-30 14:18:48.523 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-30 14:18:48.523 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-30 14:18:55.587 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:18:56.303 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-30 14:18:57.509 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-30 14:18:59.921 [redisson-netty-2-19] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-30 14:19:00.655 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:76
2025-07-30 14:19:06.400 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:19:06.425 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:19:08.320 [main] INFO  [  ,  ] com.kun.linkage.clearing.config.AsyncConfiguration.clearingTaskExecutor:64 - 清算任务执行器初始化完成，核心线程数: 5, 最大线程数: 20, 队列容量: 200
2025-07-30 14:19:08.357 [main] INFO  [  ,  ] c.kun.linkage.clearing.config.XxlJobConfiguration.xxlJobExecutor:33 - >>>>>>>>>>> xxl-job config init.
2025-07-30 14:19:08.814 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-30 14:19:10.193 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-30 14:19:10.243 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 14:19:10.243 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-30 14:19:10.254 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-30 14:19:10.257 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 14:19:10.257 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 14:19:10.257 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-30 14:19:10.257 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2f22049d
2025-07-30 14:19:10.379 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:19:10.379 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:19:13.928 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 14:19:14.971 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:KLClearingTrigger, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1b5359a1[class com.kun.linkage.clearing.task.KLClearingTrigger$$EnhancerBySpringCGLIB$$6a908dc9#triggerClearing]
2025-07-30 14:19:14.971 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:visaBase05DataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@78bbc584[class com.kun.linkage.clearing.task.VisaBase05DataTask$$EnhancerBySpringCGLIB$$e74433d5#visaBase05DataTask]
2025-07-30 14:19:14.972 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:visaBaseFileTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@31a89b54[class com.kun.linkage.clearing.task.VisaBaseFileTask$$EnhancerBySpringCGLIB$$8fe560dc#ywClearingFileTask]
2025-07-30 14:19:14.990 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-clearing' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:19:15.040 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:19:15.062 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-auth' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:19:15.084 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-auth' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:19:15.095 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:19:15.118 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:19:20.598 [Thread-147] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 18084
2025-07-30 14:19:24.049 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:19:27.055 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:19:27.056 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='BPC_VISA_BASE_FILE_33_GROUP', nameServer='mq.dev.kun:9876', topic='BPC_VISA_BASE_FILE_33_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-30 14:19:27.056 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:bpcBaseFile33Listener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-30 14:19:27.070 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:19:30.078 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:19:35.386 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:19:38.388 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='BPC_VISA_BASE_FILE_05_GROUP', nameServer='mq.dev.kun:9876', topic='BPC_VISA_BASE_FILE_05_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-30 14:19:38.388 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:19:38.389 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:bpcBaseFile05Listener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-30 14:19:38.395 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:19:38.536 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-30 14:19:38.573 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-30 14:19:38.590 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-30 14:19:38.705 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-30 14:19:38.803 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 9020 (http)
2025-07-30 14:19:38.843 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 14:19:38.843 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 14:19:39.389 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-clearing **************:9020 register finished
2025-07-30 14:19:39.393 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-30 14:19:39.393 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 14:19:39.421 [main] INFO  [  ,  ] c.k.linkage.clearing.KunLinkageClearingApplication.logStarted:61 - Started KunLinkageClearingApplication in 80.81 seconds (JVM running for 87.022)
2025-07-30 14:19:39.453 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-clearing, group=DEFAULT_GROUP
2025-07-30 14:19:39.453 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-clearing-local.properties, group=DEFAULT_GROUP
2025-07-30 14:19:39.453 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-clearing.properties, group=DEFAULT_GROUP
2025-07-30 14:19:39.712 [RMI TCP Connection(4)-**************] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 14:19:39.713 [RMI TCP Connection(4)-**************] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-30 14:19:39.721 [RMI TCP Connection(4)-**************] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 8 ms
2025-07-30 14:19:40.358 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:19:41.398 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:19:53.088 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:19:53.093 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:19:56.089 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:04.406 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:04.410 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:07.409 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:10.362 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:10.369 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:19.099 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:22.100 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:22.101 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:30.422 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:33.420 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:33.422 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:40.363 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:40.369 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:45.109 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:48.116 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:54.707 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:56.430 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:20:59.433 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:06.084 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:10.365 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:10.367 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:11.125 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:14.132 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:22.443 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:24.706 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:25.443 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:36.092 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:37.140 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:40.148 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:40.365 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:40.370 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:48.457 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:51.460 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:54.709 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:21:56.158 [XNIO-1 task-3] INFO  [ 7fdebb193d02c323 , 7fdebb193d02c323 ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 814 ms
2025-07-30 14:22:03.158 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:06.091 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:06.162 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:10.365 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:10.367 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:14.471 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:17.476 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:24.710 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:29.173 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:32.176 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:36.087 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:40.361 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:40.365 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:40.480 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:43.484 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:54.711 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:54.714 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:22:57.713 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:06.093 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:06.094 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:09.100 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:10.364 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:10.375 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:20.720 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:23.724 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:23.727 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:32.109 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:35.114 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:35.115 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:39.239 [XNIO-1 task-3] INFO  [ bf1a5670ed5e85db , bf1a5670ed5e85db ] ShardingSphere-SQL.log:74 - Logic SQL: INSERT INTO kl_export_file_record  ( file_record_id,
file_name,
file_type,
file_status,



created_time,
updated_time )  VALUES  ( ?,
?,
?,
?,



?,
? )
2025-07-30 14:23:39.240 [XNIO-1 task-3] INFO  [ bf1a5670ed5e85db , bf1a5670ed5e85db ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLInsertStatement(setAssignment=Optional.empty, onDuplicateKeyColumns=Optional.empty)
2025-07-30 14:23:39.240 [XNIO-1 task-3] INFO  [ bf1a5670ed5e85db , bf1a5670ed5e85db ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: INSERT INTO kl_export_file_record  ( file_record_id,
file_name,
file_type,
file_status,



created_time,
updated_time )  VALUES  (?, ?, ?, ?, ?, ?) ::: [1950442143491936258, VISA_SettlementDetail_20250730142338.csv, VISA_SETTLEMENT_DETAIL, PROCESSING, 2025-07-30T14:23:38.986, 2025-07-30T14:23:38.986]
2025-07-30 14:23:40.370 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:40.370 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:46.733 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:49.740 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:54.708 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:23:58.126 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:01.132 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:06.095 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:10.361 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:10.377 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:12.747 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:15.751 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:23.514 [XNIO-1 task-3] INFO  [ e54c740cc5dad548 , e54c740cc5dad548 ] ShardingSphere-SQL.log:74 - Logic SQL: INSERT INTO kl_export_file_record  ( file_record_id,
file_name,
file_type,
file_status,



created_time,
updated_time )  VALUES  ( ?,
?,
?,
?,



?,
? )
2025-07-30 14:24:23.515 [XNIO-1 task-3] INFO  [ e54c740cc5dad548 , e54c740cc5dad548 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLInsertStatement(setAssignment=Optional.empty, onDuplicateKeyColumns=Optional.empty)
2025-07-30 14:24:23.515 [XNIO-1 task-3] INFO  [ e54c740cc5dad548 , e54c740cc5dad548 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: INSERT INTO kl_export_file_record  ( file_record_id,
file_name,
file_type,
file_status,



created_time,
updated_time )  VALUES  (?, ?, ?, ?, ?, ?) ::: [1950442329534484482, VISA_SettlementDetail_20250730142423.csv, VISA_SETTLEMENT_DETAIL, PROCESSING, 2025-07-30T14:24:23.482, 2025-07-30T14:24:23.482]
2025-07-30 14:24:24.142 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:24.714 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:27.149 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:36.088 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:38.761 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:40.369 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:40.373 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:41.766 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:50.155 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:53.162 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:24:54.712 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:04.773 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:06.093 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:07.780 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:10.367 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:10.374 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:16.170 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:19.171 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:23.142 [XNIO-1 task-3] INFO  [ 0a6113873194600f , 0a6113873194600f ] ShardingSphere-SQL.log:74 - Logic SQL: INSERT INTO kl_export_file_record  ( file_record_id,
file_name,
file_type,
file_status,



created_time,
updated_time )  VALUES  ( ?,
?,
?,
?,



?,
? )
2025-07-30 14:25:23.143 [XNIO-1 task-3] INFO  [ 0a6113873194600f , 0a6113873194600f ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLInsertStatement(setAssignment=Optional.empty, onDuplicateKeyColumns=Optional.empty)
2025-07-30 14:25:23.143 [XNIO-1 task-3] INFO  [ 0a6113873194600f , 0a6113873194600f ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: INSERT INTO kl_export_file_record  ( file_record_id,
file_name,
file_type,
file_status,



created_time,
updated_time )  VALUES  (?, ?, ?, ?, ?, ?) ::: [1950442579716329474, VISA_SettlementDetail_20250730142523.csv, VISA_SETTLEMENT_DETAIL, PROCESSING, 2025-07-30T14:25:23.131, 2025-07-30T14:25:23.131]
2025-07-30 14:25:24.716 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:30.792 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:33.795 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:36.090 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:40.369 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:40.373 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:42.185 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:45.186 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:54.714 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:54.715 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:25:57.718 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:06.096 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:06.099 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:09.103 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:10.368 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:10.375 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:20.726 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:23.728 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:23.728 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:31.483 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] ShardingSphere-SQL.log:74 - Logic SQL: INSERT INTO kl_export_file_record  ( file_record_id,
file_name,
file_type,
file_status,



created_time,
updated_time )  VALUES  ( ?,
?,
?,
?,



?,
? )
2025-07-30 14:26:31.484 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLInsertStatement(setAssignment=Optional.empty, onDuplicateKeyColumns=Optional.empty)
2025-07-30 14:26:31.484 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: INSERT INTO kl_export_file_record  ( file_record_id,
file_name,
file_type,
file_status,



created_time,
updated_time )  VALUES  (?, ?, ?, ?, ?, ?) ::: [1950442866380230657, VISA_SettlementDetail_20250730142631.csv, VISA_SETTLEMENT_DETAIL, PROCESSING, 2025-07-30T14:26:31.468, 2025-07-30T14:26:31.468]
2025-07-30 14:26:31.969 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  clearing_id,clearing_no,clearing_file_id,clearing_file_name,channel_source,`system`,clearing_date,auth_id,trans_id,auth_remain_auth_amt,trans_code,auth_date,original_clearing_no,customer_mer_id,card_acceptor_id,card_acceptor_name,card_acceptor_country_code,card_acceptor_tid,transaction_type,transaction_date,transaction_datetime,transaction_currency_no,transaction_currency_code,transaction_currency_precision,clear_amount,reference_no,trace_audit_no,processor_card_id,kcard_id,card_no,masked_card_no,cardholder_currency_no,cardholder_currency_code,cardholder_currency_precision,cardholder_amount,acq_arn,auth_amount,difference_flag,transaction_amount_offset,markup_rate,cardholder_markup_amount,cardholder_billing_amount_with_markup,clearing_status,error_flag,error_reason,auth_code,pos_entry_mode_tcr0,acquiring_Identifier_tcr0,card_acceptor_mcc,cpd,intechange_fee_amt,intechange_fee_sign,pos_environment_tcr1,fx_rate_source_tcr5,fx_rate_destination_tcr5,authorization_response_code_tcr5,multiple_clearing_sequence_number_tcr5,multiple_clearing_sequence_count_tcr5,mvv,reversal_flag,processor_request_id,original_processor_request_id,fe_transaction_number,cardholder_billing_amount_offset,cardholder_markup_billing_amount_offset,card_schema_product_id,response_message,create_time,update_time,acquiring_id,clearing_type,remaining_clear_amount,original_clearing_id,auth_remain_bill_amt,auth_remain_bill_amt_with_markup,auth_remain_frozen_amt,auth_markup_rate,notify_flag,notify_results,card_product_code,usage_cod,reason_code,merchant_postal_code,merchant_city,settlement_flag,delete_flag  FROM kc_clearing_info 
 
 WHERE (clearing_date >= ? AND clearing_date <= ? AND delete_flag = ?) ORDER BY create_time DESC
2025-07-30 14:26:31.969 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-30 14:26:31.970 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  clearing_id,clearing_no,clearing_file_id,clearing_file_name,channel_source,`system`,clearing_date,auth_id,trans_id,auth_remain_auth_amt,trans_code,auth_date,original_clearing_no,customer_mer_id,card_acceptor_id,card_acceptor_name,card_acceptor_country_code,card_acceptor_tid,transaction_type,transaction_date,transaction_datetime,transaction_currency_no,transaction_currency_code,transaction_currency_precision,clear_amount,reference_no,trace_audit_no,processor_card_id,kcard_id,card_no,masked_card_no,cardholder_currency_no,cardholder_currency_code,cardholder_currency_precision,cardholder_amount,acq_arn,auth_amount,difference_flag,transaction_amount_offset,markup_rate,cardholder_markup_amount,cardholder_billing_amount_with_markup,clearing_status,error_flag,error_reason,auth_code,pos_entry_mode_tcr0,acquiring_Identifier_tcr0,card_acceptor_mcc,cpd,intechange_fee_amt,intechange_fee_sign,pos_environment_tcr1,fx_rate_source_tcr5,fx_rate_destination_tcr5,authorization_response_code_tcr5,multiple_clearing_sequence_number_tcr5,multiple_clearing_sequence_count_tcr5,mvv,reversal_flag,processor_request_id,original_processor_request_id,fe_transaction_number,cardholder_billing_amount_offset,cardholder_markup_billing_amount_offset,card_schema_product_id,response_message,create_time,update_time,acquiring_id,clearing_type,remaining_clear_amount,original_clearing_id,auth_remain_bill_amt,auth_remain_bill_amt_with_markup,auth_remain_frozen_amt,auth_markup_rate,notify_flag,notify_results,card_product_code,usage_cod,reason_code,merchant_postal_code,merchant_city,settlement_flag,delete_flag  FROM kc_clearing_info_2025_q2 
 
 WHERE (clearing_date >= ? AND clearing_date <= ? AND delete_flag = ?) ORDER BY create_time DESC ::: [2025-06-30, 2025-07-30, 0]
2025-07-30 14:26:31.970 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  clearing_id,clearing_no,clearing_file_id,clearing_file_name,channel_source,`system`,clearing_date,auth_id,trans_id,auth_remain_auth_amt,trans_code,auth_date,original_clearing_no,customer_mer_id,card_acceptor_id,card_acceptor_name,card_acceptor_country_code,card_acceptor_tid,transaction_type,transaction_date,transaction_datetime,transaction_currency_no,transaction_currency_code,transaction_currency_precision,clear_amount,reference_no,trace_audit_no,processor_card_id,kcard_id,card_no,masked_card_no,cardholder_currency_no,cardholder_currency_code,cardholder_currency_precision,cardholder_amount,acq_arn,auth_amount,difference_flag,transaction_amount_offset,markup_rate,cardholder_markup_amount,cardholder_billing_amount_with_markup,clearing_status,error_flag,error_reason,auth_code,pos_entry_mode_tcr0,acquiring_Identifier_tcr0,card_acceptor_mcc,cpd,intechange_fee_amt,intechange_fee_sign,pos_environment_tcr1,fx_rate_source_tcr5,fx_rate_destination_tcr5,authorization_response_code_tcr5,multiple_clearing_sequence_number_tcr5,multiple_clearing_sequence_count_tcr5,mvv,reversal_flag,processor_request_id,original_processor_request_id,fe_transaction_number,cardholder_billing_amount_offset,cardholder_markup_billing_amount_offset,card_schema_product_id,response_message,create_time,update_time,acquiring_id,clearing_type,remaining_clear_amount,original_clearing_id,auth_remain_bill_amt,auth_remain_bill_amt_with_markup,auth_remain_frozen_amt,auth_markup_rate,notify_flag,notify_results,card_product_code,usage_cod,reason_code,merchant_postal_code,merchant_city,settlement_flag,delete_flag  FROM kc_clearing_info_2025_q3 
 
 WHERE (clearing_date >= ? AND clearing_date <= ? AND delete_flag = ?) ORDER BY create_time DESC ::: [2025-06-30, 2025-07-30, 0]
2025-07-30 14:26:32.114 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:32.679 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] c.k.l.c.s.bpcFile.impl.ClearingInfoServiceImpl.asyncExportData:289 - 查询到 123 条清分数据
2025-07-30 14:26:32.690 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT account_no,account_name
        FROM kc_account_association_info
        WHERE account_type = 'COIN_ACCOUNT'
2025-07-30 14:26:32.690 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-30 14:26:32.690 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT account_no,account_name
        FROM kc_account_association_info
        WHERE account_type = 'COIN_ACCOUNT'
2025-07-30 14:26:33.005 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] c.k.l.c.s.bpcFile.impl.ClearingInfoServiceImpl.asyncExportData:293 - CSV文件生成完成，文件大小: 20207 bytes
2025-07-30 14:26:35.119 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:35.119 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:35.988 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] c.k.l.c.s.bpcFile.impl.ClearingInfoServiceImpl.asyncExportData:297 - 文件上传S3成功，URL: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/clearing-export/VISA_SettlementDetail_20250730142631.csv
2025-07-30 14:26:36.008 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] ShardingSphere-SQL.log:74 - Logic SQL: UPDATE kl_export_file_record  SET file_status=?,
s3_url=?,
file_size=?,


updated_time=?  WHERE file_record_id=?
2025-07-30 14:26:36.008 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLUpdateStatement(orderBy=Optional.empty, limit=Optional.empty)
2025-07-30 14:26:36.008 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: UPDATE kl_export_file_record  SET file_status=?,
s3_url=?,
file_size=?,


updated_time=?  WHERE file_record_id=? ::: [SUCCESS, https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com/kl-static-file/clearing-export/VISA_SettlementDetail_20250730142631.csv, 20207, 2025-07-30T14:26:35.989, 1950442866380230657]
2025-07-30 14:26:36.195 [XNIO-1 task-3] INFO  [ a130d8e392936b24 , a130d8e392936b24 ] c.k.l.c.s.bpcFile.impl.ClearingInfoServiceImpl.asyncExportData:307 - 异步导出清分数据完成，fileRecordId: 1950442866380230657
2025-07-30 14:26:40.373 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:40.379 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:46.739 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:49.745 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:54.715 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:26:58.129 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:01.134 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:06.090 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:10.374 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:10.379 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:12.757 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:15.758 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:24.139 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:24.717 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:27.144 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:32.727 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 14:27:35.739 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:36.093 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:38.747 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:38.755 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:9876] result: true
2025-07-30 14:27:40.373 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:40.381 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:40.384 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:43.389 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:46.396 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:46.398 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:9876] result: true
2025-07-30 14:27:49.408 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:49.417 [SpringApplicationShutdownHook] INFO  [  ,  ] io.undertow.stop:259 - stopping server: Undertow - 2.2.28.Final
2025-07-30 14:27:49.434 [SpringApplicationShutdownHook] INFO  [  ,  ] io.undertow.servlet.log:389 - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-30 14:27:49.437 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='BPC_VISA_BASE_FILE_05_GROUP', nameServer='mq.dev.kun:9876', topic='BPC_VISA_BASE_FILE_05_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-30 14:27:49.438 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='BPC_VISA_BASE_FILE_33_GROUP', nameServer='mq.dev.kun:9876', topic='BPC_VISA_BASE_FILE_33_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-30 14:27:49.539 [SpringApplicationShutdownHook] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.destroy:847 - Shutting down Quartz Scheduler
2025-07-30 14:27:49.540 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:666 - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 14:27:49.540 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 14:27:49.541 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:740 - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 14:27:49.543 [SpringApplicationShutdownHook] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.deregister:95 - De-registering from Nacos Server now...
2025-07-30 14:27:49.588 [SpringApplicationShutdownHook] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.deregister:115 - De-registration finished.
2025-07-30 14:27:49.597 [Thread-147] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:91 - >>>>>>>>>>> xxl-job remoting server stop.
2025-07-30 14:27:49.818 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:87 - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='kun-linkage-clearing', registryValue='http://**************:18084/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-30 14:27:49.818 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:105 - >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-30 14:27:49.819 [SpringApplicationShutdownHook] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.stop:117 - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-30 14:27:49.819 [xxl-job, executor JobLogFileCleanThread] INFO  [  ,  ] com.xxl.job.core.thread.JobLogFileCleanThread.run:99 - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-07-30 14:27:49.819 [xxl-job, executor TriggerCallbackThread] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:98 - >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-30 14:27:49.819 [Thread-136] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:128 - >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-30 14:27:52.836 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:55.833 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:27:55.834 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:9876] result: true
2025-07-30 14:27:58.846 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:28:01.851 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:28:01.854 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:9876] result: true
2025-07-30 14:28:02.015 [SpringApplicationShutdownHook] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-07-30 14:28:02.022 [SpringApplicationShutdownHook] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-07-30 14:28:27.918 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-30 14:28:27.988 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 14:28:28.619 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 14:28:28.620 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 14:28:30.359 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing,DEFAULT_GROUP'}]
2025-07-30 14:28:30.400 [main] INFO  [  ,  ] c.k.linkage.clearing.KunLinkageClearingApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-30 14:28:31.603 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 14:28:31.607 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 14:28:31.644 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-07-30 14:28:31.854 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-30 14:28:32.188 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=bbb2ad06-7a8d-3098-b15b-2d3747938be3
2025-07-30 14:28:32.328 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:28:32.329 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:28:32.329 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$542/451768075] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:28:32.330 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:28:32.333 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:28:32.336 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:28:32.958 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$620f49b3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 14:28:33.899 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-30 14:28:33.899 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3487 ms
2025-07-30 14:28:44.723 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-30 14:28:50.377 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-30 14:28:50.377 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-30 14:28:50.377 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-30 14:28:57.296 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:28:58.017 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-30 14:28:59.166 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-30 14:29:01.844 [redisson-netty-2-19] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-30 14:29:02.565 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:78
2025-07-30 14:29:08.324 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:29:08.350 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:29:10.123 [main] INFO  [  ,  ] com.kun.linkage.clearing.config.AsyncConfiguration.clearingTaskExecutor:64 - 清算任务执行器初始化完成，核心线程数: 5, 最大线程数: 20, 队列容量: 200
2025-07-30 14:29:10.159 [main] INFO  [  ,  ] c.kun.linkage.clearing.config.XxlJobConfiguration.xxlJobExecutor:33 - >>>>>>>>>>> xxl-job config init.
2025-07-30 14:29:10.606 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-30 14:29:12.301 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:29:12.301 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:29:12.314 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-30 14:29:12.393 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 14:29:12.393 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-30 14:29:12.408 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-30 14:29:12.412 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 14:29:12.413 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 14:29:12.413 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-30 14:29:12.413 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@19273390
2025-07-30 14:29:15.808 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 14:29:16.824 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:KLClearingTrigger, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@31a89b54[class com.kun.linkage.clearing.task.KLClearingTrigger$$EnhancerBySpringCGLIB$$9c2e67c7#triggerClearing]
2025-07-30 14:29:16.825 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:visaBase05DataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@11d16eb8[class com.kun.linkage.clearing.task.VisaBase05DataTask$$EnhancerBySpringCGLIB$$18e20dd3#visaBase05DataTask]
2025-07-30 14:29:16.825 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:visaBaseFileTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@104ea372[class com.kun.linkage.clearing.task.VisaBaseFileTask$$EnhancerBySpringCGLIB$$c1833ada#ywClearingFileTask]
2025-07-30 14:29:16.838 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-clearing' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:29:16.882 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:29:16.903 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-auth' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:29:16.924 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-auth' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:29:16.935 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:29:16.957 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 14:29:22.434 [Thread-132] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 18084
2025-07-30 14:29:25.838 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:29:28.841 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='BPC_VISA_BASE_FILE_33_GROUP', nameServer='mq.dev.kun:9876', topic='BPC_VISA_BASE_FILE_33_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-30 14:29:28.841 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:29:28.842 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:bpcBaseFile33Listener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-30 14:29:28.851 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:29:31.857 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:29:37.186 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:29:40.189 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:29:40.193 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='BPC_VISA_BASE_FILE_05_GROUP', nameServer='mq.dev.kun:9876', topic='BPC_VISA_BASE_FILE_05_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-30 14:29:40.193 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:29:40.193 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:bpcBaseFile05Listener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-30 14:29:40.298 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-30 14:29:40.335 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-30 14:29:40.351 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-30 14:29:40.436 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-30 14:29:40.532 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 9020 (http)
2025-07-30 14:29:40.571 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 14:29:40.571 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 14:29:41.116 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-clearing **************:9020 register finished
2025-07-30 14:29:41.119 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-30 14:29:41.120 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 14:29:41.149 [main] INFO  [  ,  ] c.k.linkage.clearing.KunLinkageClearingApplication.logStarted:61 - Started KunLinkageClearingApplication in 73.548 seconds (JVM running for 80.004)
2025-07-30 14:29:41.181 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-clearing, group=DEFAULT_GROUP
2025-07-30 14:29:41.182 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-clearing-local.properties, group=DEFAULT_GROUP
2025-07-30 14:29:41.182 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-clearing.properties, group=DEFAULT_GROUP
2025-07-30 14:29:41.404 [RMI TCP Connection(4)-**************] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 14:29:41.405 [RMI TCP Connection(4)-**************] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-30 14:29:41.413 [RMI TCP Connection(4)-**************] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 8 ms
2025-07-30 14:29:42.280 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:29:43.196 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:29:54.868 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:29:54.874 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:29:57.694 [XNIO-1 task-1] INFO  [ dce0d7122c8131ad , dce0d7122c8131ad ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT file_record_id,file_name,file_type,file_status,s3_url,file_size,created_by,created_time,updated_time,error_message FROM kl_export_file_record WHERE file_record_id=? 
2025-07-30 14:29:57.694 [XNIO-1 task-1] INFO  [ dce0d7122c8131ad , dce0d7122c8131ad ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-30 14:29:57.695 [XNIO-1 task-1] INFO  [ dce0d7122c8131ad , dce0d7122c8131ad ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT file_record_id,file_name,file_type,file_status,s3_url,file_size,created_by,created_time,updated_time,error_message FROM kl_export_file_record WHERE file_record_id=?  ::: [1950442866380230657]
2025-07-30 14:29:57.871 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:06.202 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:06.205 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:09.205 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:12.283 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:12.289 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:20.879 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:23.886 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:23.887 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:32.215 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:33.293 [XNIO-1 task-1] INFO  [ 8a709b6f43723d45 , 8a709b6f43723d45 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_export_file_record WHERE (file_type = ? AND file_status = ?)
2025-07-30 14:30:33.293 [XNIO-1 task-1] INFO  [ 8a709b6f43723d45 , 8a709b6f43723d45 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-30 14:30:33.294 [XNIO-1 task-1] INFO  [ 8a709b6f43723d45 , 8a709b6f43723d45 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_export_file_record WHERE (file_type = ? AND file_status = ?) ::: [VISA_SETTLEMENT_DETAIL, SUCCESS]
2025-07-30 14:30:33.674 [XNIO-1 task-1] INFO  [ 8a709b6f43723d45 , 8a709b6f43723d45 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  file_record_id,file_name,file_type,file_status,s3_url,file_size,created_by,created_time,updated_time,error_message  FROM kl_export_file_record 
 
 WHERE (file_type = ? AND file_status = ?) ORDER BY created_time DESC
 LIMIT ? 
2025-07-30 14:30:33.674 [XNIO-1 task-1] INFO  [ 8a709b6f43723d45 , 8a709b6f43723d45 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@12372453], lock=Optional.empty, window=Optional.empty)
2025-07-30 14:30:33.674 [XNIO-1 task-1] INFO  [ 8a709b6f43723d45 , 8a709b6f43723d45 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  file_record_id,file_name,file_type,file_status,s3_url,file_size,created_by,created_time,updated_time,error_message  FROM kl_export_file_record 
 
 WHERE (file_type = ? AND file_status = ?) ORDER BY created_time DESC
 LIMIT ?  ::: [VISA_SETTLEMENT_DETAIL, SUCCESS, 100]
2025-07-30 14:30:35.215 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:35.217 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:41.891 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 14:30:42.284 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:42.294 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:44.902 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:47.910 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:47.914 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:9876] result: true
2025-07-30 14:30:50.919 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:53.924 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:56.928 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:56.930 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:9876] result: true
2025-07-30 14:30:59.934 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:30:59.939 [SpringApplicationShutdownHook] INFO  [  ,  ] io.undertow.stop:259 - stopping server: Undertow - 2.2.28.Final
2025-07-30 14:30:59.952 [SpringApplicationShutdownHook] INFO  [  ,  ] io.undertow.servlet.log:389 - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-30 14:30:59.957 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='BPC_VISA_BASE_FILE_05_GROUP', nameServer='mq.dev.kun:9876', topic='BPC_VISA_BASE_FILE_05_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-30 14:30:59.958 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='BPC_VISA_BASE_FILE_33_GROUP', nameServer='mq.dev.kun:9876', topic='BPC_VISA_BASE_FILE_33_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-30 14:30:59.989 [SpringApplicationShutdownHook] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.destroy:847 - Shutting down Quartz Scheduler
2025-07-30 14:30:59.989 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:666 - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 14:30:59.989 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 14:30:59.990 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:740 - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 14:30:59.992 [SpringApplicationShutdownHook] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.deregister:95 - De-registering from Nacos Server now...
2025-07-30 14:31:00.047 [SpringApplicationShutdownHook] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.deregister:115 - De-registration finished.
2025-07-30 14:31:00.059 [Thread-132] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:91 - >>>>>>>>>>> xxl-job remoting server stop.
2025-07-30 14:31:00.730 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:87 - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='kun-linkage-clearing', registryValue='http://**************:18084/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-30 14:31:00.731 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:105 - >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-30 14:31:00.731 [SpringApplicationShutdownHook] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.stop:117 - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-30 14:31:00.732 [xxl-job, executor JobLogFileCleanThread] INFO  [  ,  ] com.xxl.job.core.thread.JobLogFileCleanThread.run:99 - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-07-30 14:31:00.732 [xxl-job, executor TriggerCallbackThread] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:98 - >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-30 14:31:00.733 [Thread-121] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:128 - >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-30 14:31:03.749 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:31:06.755 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:31:06.757 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:9876] result: true
2025-07-30 14:31:09.763 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:31:09.766 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:31:12.763 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-30 14:31:12.765 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:9876] result: true
2025-07-30 14:31:12.911 [SpringApplicationShutdownHook] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-07-30 14:31:12.917 [SpringApplicationShutdownHook] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed

2025-07-29 16:20:58.123 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-29 16:20:58.211 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-29 16:20:58.967 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 16:20:58.967 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 16:21:00.748 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-07-29 16:21:00.793 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-29 16:21:02.086 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-29 16:21:02.090 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 16:21:02.128 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-07-29 16:21:02.335 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-29 16:21:02.639 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=f84c7782-1139-3df0-92fb-2c0221979b3f
2025-07-29 16:21:02.754 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 16:21:02.755 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 16:21:02.756 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$533/586914348] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 16:21:02.756 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 16:21:02.759 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 16:21:02.763 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 16:21:03.393 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$f6d82c79] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 16:21:04.317 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-29 16:21:04.317 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3508 ms
2025-07-29 16:21:17.315 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-29 16:21:23.688 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:21:23.783 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:21:23.798 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:21:24.671 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-29 16:21:24.671 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-29 16:21:24.672 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-29 16:21:31.215 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:21:31.254 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:21:31.869 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-29 16:21:33.409 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-29 16:21:37.828 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-29 16:21:44.897 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:21:45.456 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-29 16:21:45.918 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-29 16:21:47.594 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-29 16:21:47.643 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 16:21:47.643 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-29 16:21:47.653 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-29 16:21:47.656 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 16:21:47.656 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 16:21:47.656 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-29 16:21:47.656 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7863e6b2
2025-07-29 16:21:51.200 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-29 16:21:52.043 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@595be089[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$b9c04d8c#mpcWalletEventRetryNotifyTask]
2025-07-29 16:21:52.043 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationFeeMonthlyReportTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5c4b230c[class com.kun.linkage.customer.task.OrganizationFeeMonthlyReportTask$$EnhancerBySpringCGLIB$$440e291c#organizationFeeMonthlyReportTask]
2025-07-29 16:21:52.044 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@31dbe419[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$b0b400e#organizationSMSFeeCalculateTask]
2025-07-29 16:21:52.044 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4cd0b8cf[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$975dd447#syncCustomerInfoTask]
2025-07-29 16:21:52.057 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:21:52.076 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:21:57.474 [Thread-147] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-29 16:22:03.816 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-29 16:22:03.817 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-29 16:22:15.079 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-29 16:22:15.080 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-29 16:22:26.346 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-29 16:22:26.346 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-29 16:22:37.570 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-29 16:22:37.571 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-29 16:22:45.821 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-29 16:22:45.821 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-29 16:22:45.942 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-29 16:22:45.979 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-29 16:22:45.998 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-29 16:22:46.084 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-29 16:22:46.178 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-29 16:22:46.217 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 16:22:46.217 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 16:22:46.825 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-07-29 16:22:46.832 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-29 16:22:46.832 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 16:22:46.872 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 109.077 seconds (JVM running for 115.732)
2025-07-29 16:22:46.914 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-07-29 16:22:46.915 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-07-29 16:22:46.915 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-07-29 16:22:47.105 [RMI TCP Connection(9)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 16:22:47.106 [RMI TCP Connection(9)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-29 16:22:47.114 [RMI TCP Connection(9)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 8 ms
2025-07-29 16:28:57.594 [XNIO-1 task-1] INFO  [ 564f6503423c2e26 , 84cb2f47b826abbe ] c.k.l.c.base.controller.DefaultErrorController.error:101 - Error attributes: {"timestamp":1753777737587,"status":404,"error":"Not Found","path":"/api/technicalParams/getByOrganizationNo"}
2025-07-29 16:29:47.326 [XNIO-1 task-4] INFO  [ ae24750483ec446a , ae24750483ec446a ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 1067 ms
2025-07-29 16:30:03.624 [XNIO-1 task-4] INFO  [ 82727bc21cf40bfb , 82727bc21cf40bfb ] c.k.l.c.base.controller.DefaultErrorController.error:101 - Error attributes: {"timestamp":1753777803624,"status":404,"error":"Not Found","path":"/.well-known/appspecific/com.chrome.devtools.json"}
2025-07-29 16:30:09.568 [XNIO-1 task-3] INFO  [ 989dafd6eed4a62b , 989dafd6eed4a62b ] c.k.l.c.c.webhook.TechnicalParamsController.getByOrganizationNo:39 - 根据机构号查询技术参数配置，机构号：11111111
2025-07-29 16:30:09.979 [XNIO-1 task-3] INFO  [ 989dafd6eed4a62b , 989dafd6eed4a62b ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,aes_key,webhook_enabled,webhook_url_kyc,webhook_url_third_party_auth,webhook_url_auth_result,webhook_url_3ds_otp,status,create_time,create_user_id,create_user_name,last_modify_time,last_modify_user_id,last_modify_user_name  FROM kl_technical_params 
 
 WHERE (organization_no = ? AND status = ?)
2025-07-29 16:30:09.979 [XNIO-1 task-3] INFO  [ 989dafd6eed4a62b , 989dafd6eed4a62b ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-29 16:30:09.979 [XNIO-1 task-3] INFO  [ 989dafd6eed4a62b , 989dafd6eed4a62b ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,aes_key,webhook_enabled,webhook_url_kyc,webhook_url_third_party_auth,webhook_url_auth_result,webhook_url_3ds_otp,status,create_time,create_user_id,create_user_name,last_modify_time,last_modify_user_id,last_modify_user_name  FROM kl_technical_params 
 
 WHERE (organization_no = ? AND status = ?) ::: [11111111, VALID]
2025-07-29 16:34:03.923 [XNIO-1 task-3] INFO  [ c1dbe608ae47dc82 , 1c71bb7ec1e7d111 ] c.k.l.c.base.controller.DefaultErrorController.error:101 - Error attributes: {"timestamp":1753778043923,"status":404,"error":"Not Found","path":"/linkage-customer/linkage-customer/api/technicalParams/getByOrganizationNo"}
2025-07-29 16:35:27.393 [XNIO-1 task-3] INFO  [ 072cbd1cb1e7e862 , 371df3a0986086c1 ] c.k.l.c.base.controller.DefaultErrorController.error:101 - Error attributes: {"timestamp":1753778127393,"status":404,"error":"Not Found","path":"/linkage-customer/linkage-customer/api/technicalParams/getByOrganizationNo"}
2025-07-29 16:36:51.402 [XNIO-1 task-3] INFO  [ c1c4adca2cf86a3f , 83544d3c8c1df885 ] c.k.l.c.c.webhook.TechnicalParamsController.getByOrganizationNo:39 - 根据机构号查询技术参数配置，机构号：11111111
2025-07-29 16:36:51.425 [XNIO-1 task-3] INFO  [ c1c4adca2cf86a3f , 83544d3c8c1df885 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,aes_key,webhook_enabled,webhook_url_kyc,webhook_url_third_party_auth,webhook_url_auth_result,webhook_url_3ds_otp,status,create_time,create_user_id,create_user_name,last_modify_time,last_modify_user_id,last_modify_user_name  FROM kl_technical_params 
 
 WHERE (organization_no = ? AND status = ?)
2025-07-29 16:36:51.425 [XNIO-1 task-3] INFO  [ c1c4adca2cf86a3f , 83544d3c8c1df885 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-29 16:36:51.425 [XNIO-1 task-3] INFO  [ c1c4adca2cf86a3f , 83544d3c8c1df885 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,aes_key,webhook_enabled,webhook_url_kyc,webhook_url_third_party_auth,webhook_url_auth_result,webhook_url_3ds_otp,status,create_time,create_user_id,create_user_name,last_modify_time,last_modify_user_id,last_modify_user_name  FROM kl_technical_params 
 
 WHERE (organization_no = ? AND status = ?) ::: [11111111, VALID]
2025-07-29 16:42:39.192 [XNIO-1 task-3] INFO  [ de3ba6d5130543e0 , d754d8708baca04e ] c.k.l.c.c.webhook.TechnicalParamsController.getByOrganizationNo:39 - 根据机构号查询技术参数配置，机构号：11111111
2025-07-29 16:42:39.201 [XNIO-1 task-3] INFO  [ de3ba6d5130543e0 , d754d8708baca04e ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,aes_key,webhook_enabled,webhook_url_kyc,webhook_url_third_party_auth,webhook_url_auth_result,webhook_url_3ds_otp,status,create_time,create_user_id,create_user_name,last_modify_time,last_modify_user_id,last_modify_user_name  FROM kl_technical_params 
 
 WHERE (organization_no = ? AND status = ?)
2025-07-29 16:42:39.203 [XNIO-1 task-3] INFO  [ de3ba6d5130543e0 , d754d8708baca04e ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-29 16:42:39.203 [XNIO-1 task-3] INFO  [ de3ba6d5130543e0 , d754d8708baca04e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,aes_key,webhook_enabled,webhook_url_kyc,webhook_url_third_party_auth,webhook_url_auth_result,webhook_url_3ds_otp,status,create_time,create_user_id,create_user_name,last_modify_time,last_modify_user_id,last_modify_user_name  FROM kl_technical_params 
 
 WHERE (organization_no = ? AND status = ?) ::: [11111111, VALID]
2025-07-29 17:13:28.168 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.

2025-08-01 15:46:36.302 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-01 15:46:36.367 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-01 15:46:36.990 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 15:46:36.990 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 15:46:38.733 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing,DEFAULT_GROUP'}]
2025-08-01 15:46:38.781 [main] INFO  [  ,  ] c.k.linkage.clearing.KunLinkageClearingApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-01 15:46:40.060 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 15:46:40.065 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 15:46:40.101 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-08-01 15:46:40.324 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-01 15:46:40.643 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=5471c19c-6379-32d6-9e29-1c14838d18cc
2025-08-01 15:46:40.786 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 15:46:40.787 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 15:46:40.788 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$542/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 15:46:40.789 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 15:46:40.792 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 15:46:40.797 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 15:46:41.491 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$1af605aa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 15:46:42.543 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-01 15:46:42.543 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3748 ms
2025-08-01 15:46:59.643 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-01 15:47:12.448 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-01 15:47:12.448 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-01 15:47:12.448 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-01 15:47:19.467 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 15:47:20.192 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-01 15:47:22.086 [redisson-netty-2-12] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-08-01 15:47:27.398 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-08-01 15:47:28.284 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:75
2025-08-01 15:47:34.090 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 15:47:34.117 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 15:47:36.011 [main] INFO  [  ,  ] com.kun.linkage.clearing.config.AsyncConfiguration.clearingTaskExecutor:64 - 清算任务执行器初始化完成，核心线程数: 5, 最大线程数: 20, 队列容量: 200
2025-08-01 15:47:36.048 [main] INFO  [  ,  ] c.kun.linkage.clearing.config.XxlJobConfiguration.xxlJobExecutor:33 - >>>>>>>>>>> xxl-job config init.
2025-08-01 15:47:36.522 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-01 15:47:37.944 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-01 15:47:37.995 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-01 15:47:37.995 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-01 15:47:38.006 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-01 15:47:38.008 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-01 15:47:38.009 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-01 15:47:38.009 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-01 15:47:38.009 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@25b531b2
2025-08-01 15:47:38.050 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:47:38.050 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:47:41.550 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-01 15:47:42.991 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:KLClearingTrigger, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@59af828a[class com.kun.linkage.clearing.task.KLClearingTrigger$$EnhancerBySpringCGLIB$$a6f4be78#triggerClearing]
2025-08-01 15:47:42.992 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:visaBase05DataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2177809a[class com.kun.linkage.clearing.task.VisaBase05DataTask$$EnhancerBySpringCGLIB$$23a86484#visaBase05DataTask]
2025-08-01 15:47:42.992 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:visaBaseFileTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7f006525[class com.kun.linkage.clearing.task.VisaBaseFileTask$$EnhancerBySpringCGLIB$$cc49918b#ywClearingFileTask]
2025-08-01 15:47:43.007 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-clearing' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 15:47:43.045 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-clearing' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 15:47:43.084 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 15:47:43.117 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-auth' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 15:47:43.142 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-auth' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 15:47:43.154 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 15:47:43.179 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 15:47:48.749 [Thread-167] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 18084
2025-08-01 15:47:52.559 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:47:55.567 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:47:55.567 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='BPC_VISA_BASE_FILE_33_GROUP', nameServer='mq.dev.kun:9876', topic='BPC_VISA_BASE_FILE_33_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-01 15:47:55.568 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:bpcBaseFile33Listener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-01 15:47:55.573 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:47:58.577 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:04.073 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:07.079 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:07.079 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='BPC_VISA_BASE_FILE_05_GROUP', nameServer='mq.dev.kun:9876', topic='BPC_VISA_BASE_FILE_05_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-01 15:48:07.080 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:bpcBaseFile05Listener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-01 15:48:07.083 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:07.202 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-01 15:48:07.249 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-01 15:48:07.269 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-01 15:48:07.359 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-01 15:48:07.464 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 9020 (http)
2025-08-01 15:48:07.506 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 15:48:07.506 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 15:48:08.028 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:08.195 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-clearing **************:9020 register finished
2025-08-01 15:48:08.201 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-01 15:48:08.201 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-01 15:48:08.236 [main] INFO  [  ,  ] c.k.linkage.clearing.KunLinkageClearingApplication.logStarted:61 - Started KunLinkageClearingApplication in 92.259 seconds (JVM running for 98.471)
2025-08-01 15:48:08.276 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-clearing, group=DEFAULT_GROUP
2025-08-01 15:48:08.277 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-clearing-local.properties, group=DEFAULT_GROUP
2025-08-01 15:48:08.277 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-clearing.properties, group=DEFAULT_GROUP
2025-08-01 15:48:08.492 [RMI TCP Connection(6)-**************] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 15:48:08.493 [RMI TCP Connection(6)-**************] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-01 15:48:08.502 [RMI TCP Connection(6)-**************] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 9 ms
2025-08-01 15:48:10.082 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:18.595 [XNIO-1 task-1] INFO  [ f73b9cc6d3c247b1 , f73b9cc6d3c247b1 ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 803 ms
2025-08-01 15:48:21.584 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:21.591 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:24.588 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:33.085 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:33.088 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:36.088 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:38.034 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:38.038 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:47.606 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:50.605 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:50.607 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:48:59.095 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:02.095 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:02.097 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:08.032 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:08.041 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:13.613 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:16.614 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:22.852 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:25.104 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:28.109 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:34.591 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:38.035 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:38.043 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:39.623 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:42.628 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:51.117 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:52.854 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:54.118 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:49:54.324 [XNIO-1 task-1] INFO  [ ddd5cd26de5cf3dd , ddd5cd26de5cf3dd ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_clearing_trans_202507 WHERE (create_time >= ? AND create_time <= ?)
2025-08-01 15:49:54.324 [XNIO-1 task-1] INFO  [ ddd5cd26de5cf3dd , ddd5cd26de5cf3dd ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-01 15:49:54.324 [XNIO-1 task-1] INFO  [ ddd5cd26de5cf3dd , ddd5cd26de5cf3dd ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_clearing_trans_202507 WHERE (create_time >= ? AND create_time <= ?) ::: [2025-01-01 00:00:00.0, 2025-08-01 23:59:59.0]
2025-08-01 15:49:55.392 [XNIO-1 task-1] INFO  [ ddd5cd26de5cf3dd , ddd5cd26de5cf3dd ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  clearing_id,auth_flow_id,processor,gateway_request_id,gateway_clearing_id,original_gateway_clearing_id,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,clearing_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_postal_code,card_acceptor_region,card_acceptor_city,card_acceptor_street,mcc,processor_ext_1,original_auth_flow_id,original_processor_request_id,original_trans_time,clear_flag,trans_date,clearing_date,trans_time,clear_amount,clear_time,fee_interchange_sign,fee_interchange_amount,issuer_charge,national_reimb_fee,ing_fee_id,inter_fee_indicator,fee_program_indicator,overseas_flag,conversion_date,reversal_flag,arn,acq_bin,usage_code,reason_code,card_schema_product_id,original_clearing_id,create_time,update_time  FROM kl_clearing_trans_202507 
 
 WHERE (create_time >= ? AND create_time <= ?) ORDER BY create_time DESC
 LIMIT ? 
2025-08-01 15:49:55.392 [XNIO-1 task-1] INFO  [ ddd5cd26de5cf3dd , ddd5cd26de5cf3dd ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@9332698], lock=Optional.empty, window=Optional.empty)
2025-08-01 15:49:55.392 [XNIO-1 task-1] INFO  [ ddd5cd26de5cf3dd , ddd5cd26de5cf3dd ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  clearing_id,auth_flow_id,processor,gateway_request_id,gateway_clearing_id,original_gateway_clearing_id,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,clearing_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_postal_code,card_acceptor_region,card_acceptor_city,card_acceptor_street,mcc,processor_ext_1,original_auth_flow_id,original_processor_request_id,original_trans_time,clear_flag,trans_date,clearing_date,trans_time,clear_amount,clear_time,fee_interchange_sign,fee_interchange_amount,issuer_charge,national_reimb_fee,ing_fee_id,inter_fee_indicator,fee_program_indicator,overseas_flag,conversion_date,reversal_flag,arn,acq_bin,usage_code,reason_code,card_schema_product_id,original_clearing_id,create_time,update_time  FROM kl_clearing_trans_202507 
 
 WHERE (create_time >= ? AND create_time <= ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-01-01 00:00:00.0, 2025-08-01 23:59:59.0, 100]
2025-08-01 15:50:04.591 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:50:05.636 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:50:08.039 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:50:08.044 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:50:08.641 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:50:12.143 [XNIO-1 task-1] INFO  [ 5b076dacb98d5db2 , dcbcfefaf6d84603 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_clearing_trans_202507 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?)
2025-08-01 15:50:12.144 [XNIO-1 task-1] INFO  [ 5b076dacb98d5db2 , dcbcfefaf6d84603 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-01 15:50:12.144 [XNIO-1 task-1] INFO  [ 5b076dacb98d5db2 , dcbcfefaf6d84603 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_clearing_trans_202507 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ::: [2025-01-01 00:00:00.0, 2025-08-01 23:59:59.0, 12001944]
2025-08-01 15:50:12.260 [XNIO-1 task-1] INFO  [ 5b076dacb98d5db2 , dcbcfefaf6d84603 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  clearing_id,auth_flow_id,processor,gateway_request_id,gateway_clearing_id,original_gateway_clearing_id,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,clearing_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_postal_code,card_acceptor_region,card_acceptor_city,card_acceptor_street,mcc,processor_ext_1,original_auth_flow_id,original_processor_request_id,original_trans_time,clear_flag,trans_date,clearing_date,trans_time,clear_amount,clear_time,fee_interchange_sign,fee_interchange_amount,issuer_charge,national_reimb_fee,ing_fee_id,inter_fee_indicator,fee_program_indicator,overseas_flag,conversion_date,reversal_flag,arn,acq_bin,usage_code,reason_code,card_schema_product_id,original_clearing_id,create_time,update_time  FROM kl_clearing_trans_202507 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ? 
2025-08-01 15:50:12.260 [XNIO-1 task-1] INFO  [ 5b076dacb98d5db2 , dcbcfefaf6d84603 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@6a0aa179], lock=Optional.empty, window=Optional.empty)
2025-08-01 15:50:12.260 [XNIO-1 task-1] INFO  [ 5b076dacb98d5db2 , dcbcfefaf6d84603 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  clearing_id,auth_flow_id,processor,gateway_request_id,gateway_clearing_id,original_gateway_clearing_id,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,clearing_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_postal_code,card_acceptor_region,card_acceptor_city,card_acceptor_street,mcc,processor_ext_1,original_auth_flow_id,original_processor_request_id,original_trans_time,clear_flag,trans_date,clearing_date,trans_time,clear_amount,clear_time,fee_interchange_sign,fee_interchange_amount,issuer_charge,national_reimb_fee,ing_fee_id,inter_fee_indicator,fee_program_indicator,overseas_flag,conversion_date,reversal_flag,arn,acq_bin,usage_code,reason_code,card_schema_product_id,original_clearing_id,create_time,update_time  FROM kl_clearing_trans_202507 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-01-01 00:00:00.0, 2025-08-01 23:59:59.0, 12001944, 100]
2025-08-01 15:50:17.128 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:50:20.131 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:50:22.856 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:50:31.644 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:50:34.594 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:50:34.650 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:50:38.031 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:50:38.044 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:50:43.141 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:50:46.141 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:50:52.855 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:50:57.657 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:00.662 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:04.590 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:08.040 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:08.043 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:09.149 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:12.154 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:22.857 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:22.860 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:25.864 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:34.593 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:34.597 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:37.599 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:38.037 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:38.042 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:48.868 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:51.340 [XNIO-1 task-1] INFO  [ a1da7da367945020 , a1da7da367945020 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_clearing_trans_202507 WHERE (create_time >= ? AND create_time <= ?)
2025-08-01 15:51:51.341 [XNIO-1 task-1] INFO  [ a1da7da367945020 , a1da7da367945020 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-01 15:51:51.341 [XNIO-1 task-1] INFO  [ a1da7da367945020 , a1da7da367945020 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_clearing_trans_202507 WHERE (create_time >= ? AND create_time <= ?) ::: [2025-01-01 00:00:00.0, 2025-08-01 23:59:59.0]
2025-08-01 15:51:51.563 [XNIO-1 task-1] INFO  [ a1da7da367945020 , a1da7da367945020 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  clearing_id,auth_flow_id,processor,gateway_request_id,gateway_clearing_id,original_gateway_clearing_id,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,clearing_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_postal_code,card_acceptor_region,card_acceptor_city,card_acceptor_street,mcc,processor_ext_1,original_auth_flow_id,original_processor_request_id,original_trans_time,clear_flag,trans_date,clearing_date,trans_time,clear_amount,clear_time,fee_interchange_sign,fee_interchange_amount,issuer_charge,national_reimb_fee,ing_fee_id,inter_fee_indicator,fee_program_indicator,overseas_flag,conversion_date,reversal_flag,arn,acq_bin,usage_code,reason_code,card_schema_product_id,original_clearing_id,create_time,update_time  FROM kl_clearing_trans_202507 
 
 WHERE (create_time >= ? AND create_time <= ?) ORDER BY create_time DESC
 LIMIT ? 
2025-08-01 15:51:51.565 [XNIO-1 task-1] INFO  [ a1da7da367945020 , a1da7da367945020 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@9332698], lock=Optional.empty, window=Optional.empty)
2025-08-01 15:51:51.565 [XNIO-1 task-1] INFO  [ a1da7da367945020 , a1da7da367945020 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  clearing_id,auth_flow_id,processor,gateway_request_id,gateway_clearing_id,original_gateway_clearing_id,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,clearing_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_postal_code,card_acceptor_region,card_acceptor_city,card_acceptor_street,mcc,processor_ext_1,original_auth_flow_id,original_processor_request_id,original_trans_time,clear_flag,trans_date,clearing_date,trans_time,clear_amount,clear_time,fee_interchange_sign,fee_interchange_amount,issuer_charge,national_reimb_fee,ing_fee_id,inter_fee_indicator,fee_program_indicator,overseas_flag,conversion_date,reversal_flag,arn,acq_bin,usage_code,reason_code,card_schema_product_id,original_clearing_id,create_time,update_time  FROM kl_clearing_trans_202507 
 
 WHERE (create_time >= ? AND create_time <= ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-01-01 00:00:00.0, 2025-08-01 23:59:59.0, 100]
2025-08-01 15:51:51.871 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:51:51.874 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:00.607 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:03.611 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:03.612 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:08.039 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:08.039 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:14.881 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:17.889 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:22.853 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:26.621 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:29.625 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:34.597 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:38.039 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:38.046 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:40.894 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:43.896 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:52.636 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:52.856 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:52:55.637 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:04.593 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:06.907 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:08.036 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:08.044 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:09.910 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:18.640 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:21.644 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:22.857 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:32.919 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:34.593 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:35.922 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:38.037 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:38.045 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:44.653 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:47.657 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:52.864 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:53:58.928 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:01.929 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:04.597 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:08.040 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:08.049 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:10.665 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:13.671 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:22.861 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:22.864 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:25.867 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:34.589 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:34.591 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:37.595 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:38.042 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:38.046 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:48.876 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:51.879 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:54:51.882 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:00.600 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:03.605 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:03.606 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:08.043 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:08.048 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:14.886 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:17.893 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:22.864 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:26.615 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:29.622 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:34.595 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:38.040 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:38.051 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:40.903 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:43.907 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:52.629 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:52.860 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:55.635 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-08-01 15:55:59.411 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.

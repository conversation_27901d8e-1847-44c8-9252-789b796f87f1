package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 机构账户信息审核记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@TableName("kl_organization_account_info_review_record")
public class OrganizationAccountInfoReviewRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 审核id
     */
    @TableId(value = "review_id", type = IdType.ASSIGN_ID)
    private Long reviewId;

    /**
     * 操作类型:Add,Modify
     */
    private String operatorType;

    /**
     * 机构账户信息表id,修改时有值
     */
    private Long organizationAccountInfoId;

    private String organizationNo;

    /**
     * 账户类型001:法币基本账户/002:数币基本账户/003:信用法币账户
     */
    private String accountType;

    /**
     * 币种字母码
     */
    private String currencyCode;

    /**
     * 状态
     */
    private String status;

    /**
     * 最小余额
     */
    private BigDecimal minimumAmount;

    /**
     * 所属方
     */
    private String network;

    /**
     * 账户唯一标识
     */
    private String accountNo;

    /**
     * 审核状态
     */
    private String reviewStatus;

    /**
     * 审核备注
     */
    private String reviewReason;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 提交人id
     */
    private String submitUserId;

    /**
     * 提交人名称
     */
    private String submitUserName;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 审核人id
     */
    private String reviewUserId;

    /**
     * 审核人名称
     */
    private String reviewUserName;

    public Long getReviewId() {
        return reviewId;
    }

    public void setReviewId(Long reviewId) {
        this.reviewId = reviewId;
    }
    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }
    public Long getOrganizationAccountInfoId() {
        return organizationAccountInfoId;
    }

    public void setOrganizationAccountInfoId(Long organizationAccountInfoId) {
        this.organizationAccountInfoId = organizationAccountInfoId;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public BigDecimal getMinimumAmount() {
        return minimumAmount;
    }

    public void setMinimumAmount(BigDecimal minimumAmount) {
        this.minimumAmount = minimumAmount;
    }
    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }
    public String getReviewReason() {
        return reviewReason;
    }

    public void setReviewReason(String reviewReason) {
        this.reviewReason = reviewReason;
    }
    public LocalDateTime getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(LocalDateTime submitTime) {
        this.submitTime = submitTime;
    }
    public String getSubmitUserId() {
        return submitUserId;
    }

    public void setSubmitUserId(String submitUserId) {
        this.submitUserId = submitUserId;
    }
    public String getSubmitUserName() {
        return submitUserName;
    }

    public void setSubmitUserName(String submitUserName) {
        this.submitUserName = submitUserName;
    }
    public LocalDateTime getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(LocalDateTime reviewTime) {
        this.reviewTime = reviewTime;
    }
    public String getReviewUserId() {
        return reviewUserId;
    }

    public void setReviewUserId(String reviewUserId) {
        this.reviewUserId = reviewUserId;
    }
    public String getReviewUserName() {
        return reviewUserName;
    }

    public void setReviewUserName(String reviewUserName) {
        this.reviewUserName = reviewUserName;
    }

    @Override
    public String toString() {
        return "OrganizationAccountInfoReviewRecord{" +
            "reviewId=" + reviewId +
            ", operatorType=" + operatorType +
            ", organizationAccountInfoId=" + organizationAccountInfoId +
            ", organizationNo=" + organizationNo +
            ", accountType=" + accountType +
            ", currencyCode=" + currencyCode +
            ", status=" + status +
            ", minimumAmount=" + minimumAmount +
            ", network=" + network +
            ", accountNo=" + accountNo +
            ", reviewStatus=" + reviewStatus +
            ", reviewReason=" + reviewReason +
            ", submitTime=" + submitTime +
            ", submitUserId=" + submitUserId +
            ", submitUserName=" + submitUserName +
            ", reviewTime=" + reviewTime +
            ", reviewUserId=" + reviewUserId +
            ", reviewUserName=" + reviewUserName +
        "}";
    }
}

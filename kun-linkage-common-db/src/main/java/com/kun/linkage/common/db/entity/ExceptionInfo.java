package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 清算05没有匹配上授权,25没有匹配上清分数据;差异数据
 * </p>
 *
 * @since 2025-06-22
 */
@TableName("kc_exception_info")
public class ExceptionInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "exception_id", type = IdType.ASSIGN_ID)
    private Long exceptionId;

    /**
     * 清算表主键id
     */
    @TableField("clearing_id")
    private Long clearingId;

    /**
     * 清算流水号
     */
    @TableField("clearing_no")
    private String clearingNo;

    /**
     * 通道来源;YW:yeewallex；
     */
    @TableField("channel_source")
    private String channelSource;

    /**
     * 来源系统:VCC;KL
     */
    @TableField("`system`")
    private String system;

    /**
     * 清算日期；yyyymmdd
     */
    @TableField("clearing_date")
    private LocalDate clearingDate;

    /**
     * 关联授权流水表的主键id
     */
    @TableField("auth_id")
    private Long authId;

    /**
     * 05:消费;06:退货；07：取现;25:消费查询;26:退货撤销；27：取现撤销
     */
    @TableField("trans_code")
    private String transCode;

    /**
     * 商户号
     */
    @TableField("customer_mer_id")
    private String customerMerId;

    /**
     * 商户名称
     */
    @TableField("card_acceptor_name")
    private String cardAcceptorName;

    /**
     * 商户国家代码
     */
    @TableField("card_acceptor_country_code")
    private String cardAcceptorCountryCode;

    /**
     * 交易时间
     */
    @TableField("transaction_date")
    private String transactionDate;

    /**
     * 交易币种
     */
    @TableField("transaction_currency_code")
    private String transactionCurrencyCode;

    /**
     * 交易金额
     */
    @TableField("transaction_amount")
    private BigDecimal transactionAmount;

    /**
     * 参考号;F37
     */
    @TableField("reference_no")
    private String referenceNo;

    /**
     * 审计追踪:F11
     */
    @TableField("trace_audit_no")
    private String traceAuditNo;

    /**
     * 卡id
     */
    @TableField("processor_card_id")
    private String processorCardId;

    /**
     * kcard系统卡id
     */
    @TableField("kcard_id")
    private String kcardId;

    /**
     * 脱敏卡号
     */
    @TableField("masked_card_no")
    private String maskedCardNo;

    /**
     * 持卡人币种
     */
    @TableField("cardholder_currency_code")
    private String cardholderCurrencyCode;

    /**
     * 持卡人金额
     */
    @TableField("cardholder_amount")
    private BigDecimal cardholderAmount;

    /**
     * 收单参考号
     */
    @TableField("acq_arn")
    private String acqArn;

    /**
     * 授权交易金额
     */
    @TableField("auth_amount")
    private BigDecimal authAmount;

    /**
     * 差异金额标记;0:无差异金额;1:有差异金额
     */
    @TableField("difference_flag")
    private Integer differenceFlag;

    /**
     * 差异金额；清算的交易金额-授权的交易金额
     */
    @TableField("difference_amount")
    private BigDecimal differenceAmount;

    /**
     * markup利率
     */
    @TableField("markup_rate")
    private BigDecimal markupRate;

    /**
     * 如果有差异金额的情况下:持卡人markup金额;
     */
    @TableField("cardhold_markup_amount")
    private BigDecimal cardholdMarkupAmount;

    /**
     * F38:清分文件授权码；
     */
    @TableField("auth_code")
    private String authCode;

    /**
     * PosEntryMode
     */
    @TableField("pos_entry_mode_tcr0")
    private String posEntryModeTcr0;

    /**
     * AcquiringIdentifier 
     */
    @TableField("acquiring_Identifier_tcr0")
    private String acquiringIdentifierTcr0;

    /**
     * MerchantCategoryCode
     */
    @TableField("card_acceptor_mcc")
    private String cardAcceptorMcc;

    /**
     * CentralProcessingDate格式:YYYYMMDD
     */
    @TableField("cpd")
    private String cpd;

    /**
     * InterchangeFeeAmount
     */
    @TableField("intechange_fee_amt")
    private BigDecimal intechangeFeeAmt;

    /**
     * InterchangeFeeSign
     */
    @TableField("intechange_fee_sign")
    private String intechangeFeeSign;

    /**
     * PosEnvironment
     */
    @TableField("pos_environment_tcr1")
    private String posEnvironmentTcr1;

    /**
     * Source Currency to Base 
Currency Exchange Rate 
     */
    @TableField("fx_rate_source_tcr5")
    private String fxRateSourceTcr5;

    /**
     * Base Currency to Destination 
Currency Exchange Rate 
     */
    @TableField("fx_rate_destination_tcr5")
    private String fxRateDestinationTcr5;

    /**
     * AuthorizationResponseCode
     */
    @TableField("authorization_response_code_tcr5")
    private String authorizationResponseCodeTcr5;

    /**
     * Multiple Clearing Sequence Number
     */
    @TableField("multiple_clearing_sequence_number_tcr5")
    private String multipleClearingSequenceNumberTcr5;

    /**
     * Multiple Clearing Sequence Count
     */
    @TableField("multiple_clearing_sequence_count_tcr5")
    private String multipleClearingSequenceCountTcr5;

    /**
     * Merchant Verification Value 
     */
    @TableField("mvv")
    private String mvv;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     *交易类型
     */
    @TableField("transaction_type")
    private String transactionType;

    /**
     * 清分金额
     */
    @TableField("clear_amount")
    private BigDecimal clearAmount;

    /**
     *差异原因
     */
    @TableField("error_reason")
    private String errorReason;

    /**
     * 处理状态
     */
    @TableField("process_status")
    private String processStatus;

    /**
     * 操作类型
     */
    @TableField("operation_type")
    private String operationType;

    /**
     * 操作人
     */
    @TableField("operation_user_name")
    private String operationUserName;

    /**
     * 操作日期
     */
    @TableField("operation_date_time")
    private Date operationDateTime;

    /**
     * 交易币种;三位数字
     */
    @TableField("transaction_currency_no")
    private String transactionCurrencyNo;

    /**
     * 持卡人币种;三位数字
     */
    @TableField("cardholder_currency_no")
    private String cardholderCurrencyNo;

    public Long getExceptionId() {
        return exceptionId;
    }

    public void setExceptionId(Long exceptionId) {
        this.exceptionId = exceptionId;
    }

    public Long getClearingId() {
        return clearingId;
    }

    public void setClearingId(Long clearingId) {
        this.clearingId = clearingId;
    }

    public String getClearingNo() {
        return clearingNo;
    }

    public void setClearingNo(String clearingNo) {
        this.clearingNo = clearingNo;
    }

    public String getChannelSource() {
        return channelSource;
    }

    public void setChannelSource(String channelSource) {
        this.channelSource = channelSource;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public LocalDate getClearingDate() {
        return clearingDate;
    }

    public void setClearingDate(LocalDate clearingDate) {
        this.clearingDate = clearingDate;
    }

    public Long getAuthId() {
        return authId;
    }

    public void setAuthId(Long authId) {
        this.authId = authId;
    }

    public String getTransCode() {
        return transCode;
    }

    public void setTransCode(String transCode) {
        this.transCode = transCode;
    }

    public String getCustomerMerId() {
        return customerMerId;
    }

    public void setCustomerMerId(String customerMerId) {
        this.customerMerId = customerMerId;
    }

    public String getCardAcceptorName() {
        return cardAcceptorName;
    }

    public void setCardAcceptorName(String cardAcceptorName) {
        this.cardAcceptorName = cardAcceptorName;
    }

    public String getCardAcceptorCountryCode() {
        return cardAcceptorCountryCode;
    }

    public void setCardAcceptorCountryCode(String cardAcceptorCountryCode) {
        this.cardAcceptorCountryCode = cardAcceptorCountryCode;
    }

    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getTransactionCurrencyCode() {
        return transactionCurrencyCode;
    }

    public void setTransactionCurrencyCode(String transactionCurrencyCode) {
        this.transactionCurrencyCode = transactionCurrencyCode;
    }

    public BigDecimal getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(BigDecimal transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    public String getTraceAuditNo() {
        return traceAuditNo;
    }

    public void setTraceAuditNo(String traceAuditNo) {
        this.traceAuditNo = traceAuditNo;
    }

    public String getProcessorCardId() {
        return processorCardId;
    }

    public void setProcessorCardId(String processorCardId) {
        this.processorCardId = processorCardId;
    }

    public String getKcardId() {
        return kcardId;
    }

    public void setKcardId(String kcardId) {
        this.kcardId = kcardId;
    }

    public String getMaskedCardNo() {
        return maskedCardNo;
    }

    public void setMaskedCardNo(String maskedCardNo) {
        this.maskedCardNo = maskedCardNo;
    }

    public String getCardholderCurrencyCode() {
        return cardholderCurrencyCode;
    }

    public void setCardholderCurrencyCode(String cardholderCurrencyCode) {
        this.cardholderCurrencyCode = cardholderCurrencyCode;
    }

    public BigDecimal getCardholderAmount() {
        return cardholderAmount;
    }

    public void setCardholderAmount(BigDecimal cardholderAmount) {
        this.cardholderAmount = cardholderAmount;
    }

    public String getAcqArn() {
        return acqArn;
    }

    public void setAcqArn(String acqArn) {
        this.acqArn = acqArn;
    }

    public BigDecimal getAuthAmount() {
        return authAmount;
    }

    public void setAuthAmount(BigDecimal authAmount) {
        this.authAmount = authAmount;
    }

    public Integer getDifferenceFlag() {
        return differenceFlag;
    }

    public void setDifferenceFlag(Integer differenceFlag) {
        this.differenceFlag = differenceFlag;
    }

    public BigDecimal getDifferenceAmount() {
        return differenceAmount;
    }

    public void setDifferenceAmount(BigDecimal differenceAmount) {
        this.differenceAmount = differenceAmount;
    }

    public BigDecimal getMarkupRate() {
        return markupRate;
    }

    public void setMarkupRate(BigDecimal markupRate) {
        this.markupRate = markupRate;
    }

    public BigDecimal getCardholdMarkupAmount() {
        return cardholdMarkupAmount;
    }

    public void setCardholdMarkupAmount(BigDecimal cardholdMarkupAmount) {
        this.cardholdMarkupAmount = cardholdMarkupAmount;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getPosEntryModeTcr0() {
        return posEntryModeTcr0;
    }

    public void setPosEntryModeTcr0(String posEntryModeTcr0) {
        this.posEntryModeTcr0 = posEntryModeTcr0;
    }

    public String getAcquiringIdentifierTcr0() {
        return acquiringIdentifierTcr0;
    }

    public void setAcquiringIdentifierTcr0(String acquiringIdentifierTcr0) {
        this.acquiringIdentifierTcr0 = acquiringIdentifierTcr0;
    }

    public String getCardAcceptorMcc() {
        return cardAcceptorMcc;
    }

    public void setCardAcceptorMcc(String cardAcceptorMcc) {
        this.cardAcceptorMcc = cardAcceptorMcc;
    }

    public String getCpd() {
        return cpd;
    }

    public void setCpd(String cpd) {
        this.cpd = cpd;
    }

    public BigDecimal getIntechangeFeeAmt() {
        return intechangeFeeAmt;
    }

    public void setIntechangeFeeAmt(BigDecimal intechangeFeeAmt) {
        this.intechangeFeeAmt = intechangeFeeAmt;
    }

    public String getIntechangeFeeSign() {
        return intechangeFeeSign;
    }

    public void setIntechangeFeeSign(String intechangeFeeSign) {
        this.intechangeFeeSign = intechangeFeeSign;
    }

    public String getPosEnvironmentTcr1() {
        return posEnvironmentTcr1;
    }

    public void setPosEnvironmentTcr1(String posEnvironmentTcr1) {
        this.posEnvironmentTcr1 = posEnvironmentTcr1;
    }

    public String getFxRateSourceTcr5() {
        return fxRateSourceTcr5;
    }

    public void setFxRateSourceTcr5(String fxRateSourceTcr5) {
        this.fxRateSourceTcr5 = fxRateSourceTcr5;
    }

    public String getFxRateDestinationTcr5() {
        return fxRateDestinationTcr5;
    }

    public void setFxRateDestinationTcr5(String fxRateDestinationTcr5) {
        this.fxRateDestinationTcr5 = fxRateDestinationTcr5;
    }

    public String getAuthorizationResponseCodeTcr5() {
        return authorizationResponseCodeTcr5;
    }

    public void setAuthorizationResponseCodeTcr5(String authorizationResponseCodeTcr5) {
        this.authorizationResponseCodeTcr5 = authorizationResponseCodeTcr5;
    }

    public String getMultipleClearingSequenceNumberTcr5() {
        return multipleClearingSequenceNumberTcr5;
    }

    public void setMultipleClearingSequenceNumberTcr5(String multipleClearingSequenceNumberTcr5) {
        this.multipleClearingSequenceNumberTcr5 = multipleClearingSequenceNumberTcr5;
    }

    public String getMultipleClearingSequenceCountTcr5() {
        return multipleClearingSequenceCountTcr5;
    }

    public void setMultipleClearingSequenceCountTcr5(String multipleClearingSequenceCountTcr5) {
        this.multipleClearingSequenceCountTcr5 = multipleClearingSequenceCountTcr5;
    }

    public String getMvv() {
        return mvv;
    }

    public void setMvv(String mvv) {
        this.mvv = mvv;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public BigDecimal getClearAmount() {
        return clearAmount;
    }

    public void setClearAmount(BigDecimal clearAmount) {
        this.clearAmount = clearAmount;
    }

    public String getErrorReason() {
        return errorReason;
    }

    public void setErrorReason(String errorReason) {
        this.errorReason = errorReason;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationUserName() {
        return operationUserName;
    }

    public void setOperationUserName(String operationUserName) {
        this.operationUserName = operationUserName;
    }

    public Date getOperationDateTime() {
        return operationDateTime;
    }

    public void setOperationDateTime(Date operationDateTime) {
        this.operationDateTime = operationDateTime;
    }

    public String getTransactionCurrencyNo() {
        return transactionCurrencyNo;
    }

    public void setTransactionCurrencyNo(String transactionCurrencyNo) {
        this.transactionCurrencyNo = transactionCurrencyNo;
    }

    public String getCardholderCurrencyNo() {
        return cardholderCurrencyNo;
    }

    public void setCardholderCurrencyNo(String cardholderCurrencyNo) {
        this.cardholderCurrencyNo = cardholderCurrencyNo;
    }

    @Override
    public String toString() {
        return "ExceptionInfo{" +
            "exceptionId=" + exceptionId +
            ", clearingId=" + clearingId +
            ", clearingNo=" + clearingNo +
            ", channelSource=" + channelSource +
            ", system=" + system +
            ", clearingDate=" + clearingDate +
            ", authId=" + authId +
            ", transCode=" + transCode +
            ", customerMerId=" + customerMerId +
            ", cardAcceptorName=" + cardAcceptorName +
            ", cardAcceptorCountryCode=" + cardAcceptorCountryCode +
            ", transactionDate=" + transactionDate +
            ", transactionCurrencyCode=" + transactionCurrencyCode +
            ", transactionAmount=" + transactionAmount +
            ", referenceNo=" + referenceNo +
            ", traceAuditNo=" + traceAuditNo +
            ", processorCardId=" + processorCardId +
            ", kcardId=" + kcardId +
            ", maskedCardNo=" + maskedCardNo +
            ", cardholderCurrencyCode=" + cardholderCurrencyCode +
            ", cardholderAmount=" + cardholderAmount +
            ", acqArn=" + acqArn +
            ", authAmount=" + authAmount +
            ", differenceFlag=" + differenceFlag +
            ", differenceAmount=" + differenceAmount +
            ", markupRate=" + markupRate +
            ", cardholdMarkupAmount=" + cardholdMarkupAmount +
            ", authCode=" + authCode +
            ", posEntryModeTcr0=" + posEntryModeTcr0 +
            ", acquiringIdentifierTcr0=" + acquiringIdentifierTcr0 +
            ", cardAcceptorMcc=" + cardAcceptorMcc +
            ", cpd=" + cpd +
            ", intechangeFeeAmt=" + intechangeFeeAmt +
            ", intechangeFeeSign=" + intechangeFeeSign +
            ", posEnvironmentTcr1=" + posEnvironmentTcr1 +
            ", fxRateSourceTcr5=" + fxRateSourceTcr5 +
            ", fxRateDestinationTcr5=" + fxRateDestinationTcr5 +
            ", authorizationResponseCodeTcr5=" + authorizationResponseCodeTcr5 +
            ", multipleClearingSequenceNumberTcr5=" + multipleClearingSequenceNumberTcr5 +
            ", multipleClearingSequenceCountTcr5=" + multipleClearingSequenceCountTcr5 +
            ", mvv=" + mvv +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", transactionType='" + transactionType +
            ", clearAmount=" + clearAmount +
            ", processStatus='" + processStatus +
            ", errorReason='" + errorReason +
            ", operationType='" + operationType +
            ", operationUserName='" + operationUserName +
            ", transactionCurrencyNo='" + transactionCurrencyNo +
            ", cardholderCurrencyNo='" + cardholderCurrencyNo +
        "}";
    }


}

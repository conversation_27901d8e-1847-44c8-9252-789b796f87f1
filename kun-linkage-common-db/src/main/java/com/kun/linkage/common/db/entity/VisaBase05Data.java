package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * Visa清分base05清分解析文件
 * </p>
 *
 */
@TableName("kc_visa_base_05_data")
public class VisaBase05Data implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    @TableField("transaction_code")
    private String transactionCode;

    @TableField("transaction_code_qualifier_0")
    private String transactionCodeQualifier0;

    @TableField("transaction_component_sequence_number_0")
    private String transactionComponentSequenceNumber0;

    @TableField("account_number_0")
    private String accountNumber0;

    @TableField("account_number_extension_0")
    private String accountNumberExtension0;

    @TableField("floor_limit_indicator_0")
    private String floorLimitIndicator0;

    @TableField("crb_or_exception_file_indicator_0")
    private String crbOrExceptionFileIndicator0;

    @TableField("reserved1_0")
    private String reserved10;

    @TableField("acquirer_reference_number_0")
    private String acquirerReferenceNumber0;

    @TableField("acquirer_business_id_0")
    private String acquirerBusinessId0;

    @TableField("purchase_date_0")
    private String purchaseDate0;

    @TableField("destination_amount_0")
    private String destinationAmount0;

    @TableField("destination_currency_code_0")
    private String destinationCurrencyCode0;

    @TableField("source_amount_0")
    private String sourceAmount0;

    @TableField("source_currency_code_0")
    private String sourceCurrencyCode0;

    @TableField("merchant_name_0")
    private String merchantName0;

    @TableField("merchant_city_0")
    private String merchantCity0;

    @TableField("merchant_country_code_0")
    private String merchantCountryCode0;

    @TableField("merchant_category_code_0")
    private String merchantCategoryCode0;

    @TableField("merchant_zip_code_0")
    private String merchantZipCode0;

    @TableField("merchant_state_or_province_code_0")
    private String merchantStateOrProvinceCode0;

    @TableField("requested_payment_service_0")
    private String requestedPaymentService0;

    @TableField("number_of_payment_forms_0")
    private String numberOfPaymentForms0;

    @TableField("usage_code_0")
    private String usageCode0;

    @TableField("reason_code_0")
    private String reasonCode0;

    @TableField("settlement_flag_0")
    private String settlementFlag0;

    @TableField("authorization_characteristics_indicator_0")
    private String authorizationCharacteristicsIndicator0;

    @TableField("authorization_code_0")
    private String authorizationCode0;

    @TableField("pos_terminal_capability_0")
    private String posTerminalCapability0;

    @TableField("reserved2_0")
    private String reserved20;

    @TableField("cardholder_id_method_0")
    private String cardholderIdMethod0;

    @TableField("collection_only_flag_0")
    private String collectionOnlyFlag0;

    @TableField("pos_entry_mode_0")
    private String posEntryMode0;

    @TableField("central_processing_date_0")
    private String centralProcessingDate0;

    @TableField("reimbursement_attribute_0")
    private String reimbursementAttribute0;

    @TableField("transaction_code_qualifier_1")
    private String transactionCodeQualifier1;

    @TableField("transaction_component_sequence_number_1")
    private String transactionComponentSequenceNumber1;

    @TableField("business_format_code_1")
    private String businessFormatCode1;

    @TableField("token_assurance_method_1")
    private String tokenAssuranceMethod1;

    @TableField("rate_table_id_1")
    private String rateTableId1;

    @TableField("reserved1_1")
    private String reserved11;

    @TableField("reserved2_1")
    private String reserved21;

    @TableField("documentation_indicator_1")
    private String documentationIndicator1;

    @TableField("member_message_text_1")
    private String memberMessageText1;

    @TableField("special_condition_indicators_1")
    private String specialConditionIndicators1;

    @TableField("fee_program_indicator_1")
    private String feeProgramIndicator1;

    @TableField("issuer_charge_1")
    private String issuerCharge1;

    @TableField("persistent_fx_applied_indicator_1")
    private String persistentFxAppliedIndicator1;

    @TableField("card_acceptor_id_1")
    private String cardAcceptorId1;

    @TableField("terminal_id_1")
    private String terminalId1;

    @TableField("national_reimbursement_fee_1")
    private String nationalReimbursementFee1;

    @TableField("mail_or_phone_or_electronic_commerce_and_payment_indicator_1")
    private String mailOrPhoneOrElectronicCommerceAndPaymentIndicator1;

    @TableField("special_chargeback_indicator_1")
    private String specialChargebackIndicator1;

    @TableField("conversion_date_1")
    private String conversionDate1;

    @TableField("additional_token_response_information_1")
    private String additionalTokenResponseInformation1;

    @TableField("reserved3_1")
    private String reserved31;

    @TableField("acceptance_terminal_indicator_1")
    private String acceptanceTerminalIndicator1;

    @TableField("prepaid_card_indicator_1")
    private String prepaidCardIndicator1;

    @TableField("service_development_field_1")
    private String serviceDevelopmentField1;

    @TableField("avs_response_code_1")
    private String avsResponseCode1;

    @TableField("authorization_source_code_1")
    private String authorizationSourceCode1;

    @TableField("purchase_identifier_format_1")
    private String purchaseIdentifierFormat1;

    @TableField("account_selection_1")
    private String accountSelection1;

    @TableField("installment_payment_count_1")
    private String installmentPaymentCount1;

    @TableField("purchase_identifier_1")
    private String purchaseIdentifier1;

    @TableField("cashback_1")
    private String cashback1;

    @TableField("chip_condition_code_1")
    private String chipConditionCode1;

    @TableField("pos_environment_1")
    private String posEnvironment1;

    @TableField("transaction_code_qualifier_4_sd")
    private String transactionCodeQualifier4Sd;

    @TableField("transaction_component_sequence_number_4_sd")
    private String transactionComponentSequenceNumber4Sd;

    @TableField("agent_unique_id_4_sd")
    private String agentUniqueId4Sd;

    @TableField("reserved1_4_sd")
    private String reserved14Sd;

    @TableField("network_identification_code_4_sd")
    private String networkIdentificationCode4Sd;

    @TableField("contact_for_information_4_sd")
    private String contactForInformation4Sd;

    @TableField("adjustment_processing_indicator_4_sd")
    private String adjustmentProcessingIndicator4Sd;

    @TableField("message_reason_code_4_sd")
    private String messageReasonCode4Sd;

    @TableField("surcharge_amount_4_sd")
    private String surchargeAmount4Sd;

    @TableField("surcharge_credit_debit_indicator_4_sd")
    private String surchargeCreditDebitIndicator4Sd;

    @TableField("visa_internal_use_only_4_sd")
    private String visaInternalUseOnly4Sd;

    @TableField("additional_transaction_fee1_amount_4_sd")
    private String additionalTransactionFee1Amount4Sd;

    @TableField("additional_transaction_fee2_amount_4_sd")
    private String additionalTransactionFee2Amount4Sd;

    @TableField("total_discount_amount_4_sd")
    private String totalDiscountAmount4Sd;

    @TableField("reserved2_4_sd")
    private String reserved24Sd;

    @TableField("surcharge_amount_in_billing_currency_4_sd")
    private String surchargeAmountInBillingCurrency4Sd;

    @TableField("money_transfer_foreign_exchange_fee_4_sd")
    private String moneyTransferForeignExchangeFee4Sd;

    @TableField("payment_account_reference_4_sd")
    private String paymentAccountReference4Sd;

    @TableField("token_requestor_id_4_sd")
    private String tokenRequestorId4Sd;

    @TableField("reserved3_4_sd")
    private String reserved34Sd;

    @TableField("transaction_code_qualifier_4_pd")
    private String transactionCodeQualifier4Pd;

    @TableField("transaction_component_sequence_number_4_pd")
    private String transactionComponentSequenceNumber4Pd;

    @TableField("business_format_code_4_pd")
    private String businessFormatCode4Pd;

    @TableField("reserved1_4_pd")
    private String reserved14Pd;

    @TableField("promotion_type_4_pd")
    private String promotionType4Pd;

    @TableField("promotion_code_4_pd")
    private String promotionCode4Pd;

    @TableField("reserved2_4_pd")
    private String reserved24Pd;

    @TableField("transaction_code_qualifier_4_df")
    private String transactionCodeQualifier4Df;

    @TableField("transaction_component_sequence_number_4_df")
    private String transactionComponentSequenceNumber4Df;

    @TableField("business_format_code_4_df")
    private String businessFormatCode4Df;

    @TableField("agent_unique_id_4_df")
    private String agentUniqueId4Df;

    @TableField("reserved1_4_df")
    private String reserved14Df;

    @TableField("network_identification_code_4_df")
    private String networkIdentificationCode4Df;

    @TableField("contact_for_information_4_df")
    private String contactForInformation4Df;

    @TableField("adjustment_processing_indicator_4_df")
    private String adjustmentProcessingIndicator4Df;

    @TableField("message_reason_code_4_df")
    private String messageReasonCode4Df;

    @TableField("dispute_condition_4_df")
    private String disputeCondition4Df;

    @TableField("vrol_financial_id_4_df")
    private String vrolFinancialId4Df;

    @TableField("vrol_case_number_4_df")
    private String vrolCaseNumber4Df;

    @TableField("vrol_bundle_case_number_4_df")
    private String vrolBundleCaseNumber4Df;

    @TableField("client_case_number_4_df")
    private String clientCaseNumber4Df;

    @TableField("dispute_status_4_df")
    private String disputeStatus4Df;

    @TableField("surcharge_amount_4_df")
    private String surchargeAmount4Df;

    @TableField("surcharge_credit_debit_indicator_4_df")
    private String surchargeCreditDebitIndicator4Df;

    @TableField("reserved2_4_df")
    private String reserved24Df;

    @TableField("transaction_code_qualifier_4_sp")
    private String transactionCodeQualifier4Sp;

    @TableField("transaction_component_sequence_number_4_sp")
    private String transactionComponentSequenceNumber4Sp;

    @TableField("business_format_code_4_sp")
    private String businessFormatCode4Sp;

    @TableField("agent_unique_id_4_sp")
    private String agentUniqueId4Sp;

    @TableField("reserved1_4_sp")
    private String reserved14Sp;

    @TableField("network_identification_code_4_sp")
    private String networkIdentificationCode4Sp;

    @TableField("contact_for_information_4_sp")
    private String contactForInformation4Sp;

    @TableField("adjustment_processing_indicator_4_sp")
    private String adjustmentProcessingIndicator4Sp;

    @TableField("message_reason_code_4_sp")
    private String messageReasonCode4Sp;

    @TableField("surcharge_amount_4_sp")
    private String surchargeAmount4Sp;

    @TableField("surcharge_credit_debit_indicator_4_sp")
    private String surchargeCreditDebitIndicator4Sp;

    @TableField("visa_internal_use_only_4_sp")
    private String visaInternalUseOnly4Sp;

    @TableField("promotion_type_4_sp")
    private String promotionType4Sp;

    @TableField("promotion_code_4_sp")
    private String promotionCode4Sp;

    @TableField("surcharge_amount_in_cardholder_billing_currency_4_sp")
    private String surchargeAmountInCardholderBillingCurrency4Sp;

    @TableField("payment_account_reference_4_sp")
    private String paymentAccountReference4Sp;

    @TableField("token_requestor_id_4_sp")
    private String tokenRequestorId4Sp;

    @TableField("additional_transaction_fee1_amount_4_sp")
    private String additionalTransactionFee1Amount4Sp;

    @TableField("total_discount_amount_4_sp")
    private String totalDiscountAmount4Sp;

    @TableField("reserved2_4_sp")
    private String reserved24Sp;

    @TableField("transaction_code_qualifier_5")
    private String transactionCodeQualifier5;

    @TableField("transaction_component_sequence_number_5")
    private String transactionComponentSequenceNumber5;

    @TableField("transaction_identifier_5")
    private String transactionIdentifier5;

    @TableField("authorized_amount_5")
    private String authorizedAmount5;

    @TableField("authorization_currency_code_5")
    private String authorizationCurrencyCode5;

    @TableField("authorization_response_code_5")
    private String authorizationResponseCode5;

    @TableField("validation_code_5")
    private String validationCode5;

    @TableField("excluded_transaction_identifier_reason_5")
    private String excludedTransactionIdentifierReason5;

    @TableField("reserved1_5")
    private String reserved15;

    @TableField("reserved2_5")
    private String reserved25;

    @TableField("multiple_clearing_sequence_number_5")
    private String multipleClearingSequenceNumber5;

    @TableField("multiple_clearing_sequence_count_5")
    private String multipleClearingSequenceCount5;

    @TableField("market_specific_authorization_data_indicator_5")
    private String marketSpecificAuthorizationDataIndicator5;

    @TableField("total_authorized_amount_5")
    private String totalAuthorizedAmount5;

    @TableField("information_indicator_5")
    private String informationIndicator5;

    @TableField("merchant_telephone_number_5")
    private String merchantTelephoneNumber5;

    @TableField("additional_data_indicator_5")
    private String additionalDataIndicator5;

    @TableField("merchant_volume_indicator_5")
    private String merchantVolumeIndicator5;

    @TableField("electronic_commerce_goods_indicator_5")
    private String electronicCommerceGoodsIndicator5;

    @TableField("merchant_verification_value_5")
    private String merchantVerificationValue5;

    @TableField("interchange_fee_amount_5")
    private String interchangeFeeAmount5;

    @TableField("interchange_fee_sign_5")
    private String interchangeFeeSign5;

    @TableField("source_currency_to_base_currency_exchange_rate_5")
    private String sourceCurrencyToBaseCurrencyExchangeRate5;

    @TableField("base_currency_to_destination_currency_exchange_rate_5")
    private String baseCurrencyToDestinationCurrencyExchangeRate5;

    @TableField("optional_issuer_isa_amount_5")
    private String optionalIssuerIsaAmount5;

    @TableField("product_id_5")
    private String productId5;

    @TableField("program_id_5")
    private String programId5;

    @TableField("dynamic_currency_conversion_indicator_5")
    private String dynamicCurrencyConversionIndicator5;

    @TableField("account_type_identification_5")
    private String accountTypeIdentification5;

    @TableField("spend_qualified_indicator_5")
    private String spendQualifiedIndicator5;

    @TableField("pan_token_5")
    private String panToken5;

    @TableField("reserved3_5")
    private String reserved35;

    @TableField("account_funding_source_5")
    private String accountFundingSource5;

    @TableField("cvv2_result_code_5")
    private String cvv2ResultCode5;

    @TableField("transaction_code_qualifier_6")
    private String transactionCodeQualifier6;

    @TableField("transaction_component_sequence_number_6")
    private String transactionComponentSequenceNumber6;

    @TableField("local_tax_6")
    private String localTax6;

    @TableField("local_tax_included_6")
    private String localTaxIncluded6;

    @TableField("national_tax_6")
    private String nationalTax6;

    @TableField("national_tax_included_6")
    private String nationalTaxIncluded6;

    @TableField("merchant_vat_registration_business_reference_number_6")
    private String merchantVatRegistrationBusinessReferenceNumber6;

    @TableField("customer_vat_regisration_number_6")
    private String customerVatRegisrationNumber6;

    @TableField("visa_merchant_identifier_6")
    private String visaMerchantIdentifier6;

    @TableField("reserved_6")
    private String reserved6;

    @TableField("summary_commodity_code_6")
    private String summaryCommodityCode6;

    @TableField("other_tax_6")
    private String otherTax6;

    @TableField("message_identifier_6")
    private String messageIdentifier6;

    @TableField("time_of_purchase_6")
    private String timeOfPurchase6;

    @TableField("customer_code_reference_identifier_cri_6")
    private String customerCodeReferenceIdentifierCri6;

    @TableField("non_fuel_product_code1_6")
    private String nonFuelProductCode16;

    @TableField("non_fuel_product_code2_6")
    private String nonFuelProductCode26;

    @TableField("non_fuel_product_code3_6")
    private String nonFuelProductCode36;

    @TableField("non_fuel_product_code4_6")
    private String nonFuelProductCode46;

    @TableField("non_fuel_product_code5_6")
    private String nonFuelProductCode56;

    @TableField("non_fuel_product_code6_6")
    private String nonFuelProductCode66;

    @TableField("non_fuel_product_code7_6")
    private String nonFuelProductCode76;

    @TableField("non_fuel_product_code8_6")
    private String nonFuelProductCode86;

    @TableField("merchant_postal_code_6")
    private String merchantPostalCode6;

    @TableField("reserved2_6")
    private String reserved26;

    @TableField("transaction_code_qualifier_7")
    private String transactionCodeQualifier7;

    @TableField("transaction_component_sequence_number_7")
    private String transactionComponentSequenceNumber7;

    @TableField("transaction_type_7")
    private String transactionType7;

    @TableField("card_sequence_number_7")
    private String cardSequenceNumber7;

    @TableField("terminal_transaction_date_7")
    private String terminalTransactionDate7;

    @TableField("terminal_capability_profile_7")
    private String terminalCapabilityProfile7;

    @TableField("terminal_country_code_7")
    private String terminalCountryCode7;

    @TableField("terminal_serial_number_7")
    private String terminalSerialNumber7;

    @TableField("unpredictable_number_7")
    private String unpredictableNumber7;

    @TableField("application_transaction_counter_7")
    private String applicationTransactionCounter7;

    @TableField("application_interchange_profile_7")
    private String applicationInterchangeProfile7;

    @TableField("cryptogram_7")
    private String cryptogram7;

    @TableField("issuer_application_data_byte2_7")
    private String issuerApplicationDataByte27;

    @TableField("issuer_application_data_byte3_7")
    private String issuerApplicationDataByte37;

    @TableField("terminal_verification_results_7")
    private String terminalVerificationResults7;

    @TableField("issuer_application_data_byte4_7")
    private String issuerApplicationDataByte47;

    @TableField("cryptogram_amount_7")
    private String cryptogramAmount7;

    @TableField("issuer_application_data_byte8_7")
    private String issuerApplicationDataByte87;

    @TableField("issuer_application_data_byte9_16_7")
    private String issuerApplicationDataByte9167;

    @TableField("issuer_application_data_byte1_7")
    private String issuerApplicationDataByte17;

    @TableField("issuer_application_data_byte17_7")
    private String issuerApplicationDataByte177;

    @TableField("issuer_application_data_byte18_32_7")
    private String issuerApplicationDataByte18327;

    @TableField("form_factor_indicator_7")
    private String formFactorIndicator7;

    @TableField("issuer_script1_results_7")
    private String issuerScript1Results7;

    @TableField("transaction_code_qualifier_d_fs")
    private String transactionCodeQualifierDFs;

    @TableField("transaction_component_sequence_number_d_fs")
    private String transactionComponentSequenceNumberDFs;

    @TableField("business_format_code_d_fs")
    private String businessFormatCodeDFs;

    @TableField("charging_power_output_capacity_d_fs")
    private String chargingPowerOutputCapacityDFs;

    @TableField("charging_reason_code_d_fs")
    private String chargingReasonCodeDFs;

    @TableField("total_time_plugged_in_d_fs")
    private String totalTimePluggedInDFs;

    @TableField("total_charging_time_d_fs")
    private String totalChargingTimeDFs;

    @TableField("start_time_of_charge_d_fs")
    private String startTimeOfChargeDFs;

    @TableField("finish_time_of_charge_d_fs")
    private String finishTimeOfChargeDFs;

    @TableField("estimated_km_miles_added_d_fs")
    private String estimatedKmMilesAddedDFs;

    @TableField("carbon_footprint_d_fs")
    private String carbonFootprintDFs;

    @TableField("estimated_vehicle_km_miles_available_d_fs")
    private String estimatedVehicleKmMilesAvailableDFs;

    @TableField("maximum_power_dispensed_d_fs")
    private String maximumPowerDispensedDFs;

    @TableField("connector_type_d_fs")
    private String connectorTypeDFs;

    @TableField("discount_method_d_fs")
    private String discountMethodDFs;

    @TableField("discount_agent_d_fs")
    private String discountAgentDFs;

    @TableField("discount_plan_id_d_fs")
    private String discountPlanIdDFs;

    @TableField("client_id_d_fs")
    private String clientIdDFs;

    @TableField("reserved_d_fs")
    private String reservedDFs;

    @TableField("transaction_code_qualifier_d_ip")
    private String transactionCodeQualifierDIp;

    @TableField("transaction_component_sequence_number_d_ip")
    private String transactionComponentSequenceNumberDIp;

    @TableField("business_format_code_d_ip")
    private String businessFormatCodeDIp;

    @TableField("instalment_payment_total_amount_d_ip")
    private String instalmentPaymentTotalAmountDIp;

    @TableField("instalment_payment_currency_code_d_ip")
    private String instalmentPaymentCurrencyCodeDIp;

    @TableField("number_of_installments_d_ip")
    private String numberOfInstallmentsDIp;

    @TableField("amount_of_each_instalment_d_ip")
    private String amountOfEachInstalmentDIp;

    @TableField("instalment_payment_number_d_ip")
    private String instalmentPaymentNumberDIp;

    @TableField("frequency_of_installments_d_ip")
    private String frequencyOfInstallmentsDIp;

    @TableField("plan_owner_d_ip")
    private String planOwnerDIp;

    @TableField("plan_registration_system_identifier_d_ip")
    private String planRegistrationSystemIdentifierDIp;

    @TableField("reserved_d_ip")
    private String reservedDIp;

    @TableField("transaction_code_qualifier_d_rp")
    private String transactionCodeQualifierDRp;

    @TableField("transaction_component_sequence_number_d_rp")
    private String transactionComponentSequenceNumberDRp;

    @TableField("business_format_code_d_rp")
    private String businessFormatCodeDRp;

    @TableField("recurring_payment_type_d_rp")
    private String recurringPaymentTypeDRp;

    @TableField("payment_amount_indicator_per_transaction_d_rp")
    private String paymentAmountIndicatorPerTransactionDRp;

    @TableField("number_of_recurring_payment_d_rp")
    private String numberOfRecurringPaymentDRp;

    @TableField("frequency_of_recurring_payment_d_rp")
    private String frequencyOfRecurringPaymentDRp;

    @TableField("registration_reference_number_d_rp")
    private String registrationReferenceNumberDRp;

    @TableField("maximum_recurring_payment_amount_d_rp")
    private String maximumRecurringPaymentAmountDRp;

    @TableField("validation_indicator_d_rp")
    private String validationIndicatorDRp;

    @TableField("reserved_d_rp")
    private String reservedDRp;

    @TableField("transaction_code_qualifier_d_oc")
    private String transactionCodeQualifierDOc;

    @TableField("transaction_component_sequence_number_d_oc")
    private String transactionComponentSequenceNumberDOc;

    @TableField("business_format_code_d_oc")
    private String businessFormatCodeDOc;

    @TableField("recipient_name_d_oc")
    private String recipientNameDOc;

    @TableField("purpose_of_payment_d_oc")
    private String purposeOfPaymentDOc;

    @TableField("pre_currency_conversion_amount_d_oc")
    private String preCurrencyConversionAmountDOc;

    @TableField("pre_currency_conversion_currency_code_d_oc")
    private String preCurrencyConversionCurrencyCodeDOc;

    @TableField("acceptor_legal_business_name_d_oc")
    private String acceptorLegalBusinessNameDOc;

    @TableField("payment_facilitator_name_d_oc")
    private String paymentFacilitatorNameDOc;

    @TableField("customer_reference_code_d_oc")
    private String customerReferenceCodeDOc;

    @TableField("identification_type_code_d_oc")
    private String identificationTypeCodeDOc;

    @TableField("identification_subtype_d_oc")
    private String identificationSubtypeDOc;

    @TableField("identification_value_d_oc")
    private String identificationValueDOc;

    @TableField("identification_issuing_country_code_d_oc")
    private String identificationIssuingCountryCodeDOc;

    @TableField("reserved_d_oc")
    private String reservedDOc;

    @TableField("transaction_code_qualifier_e")
    private String transactionCodeQualifierE;

    @TableField("transaction_component_sequence_number_e")
    private String transactionComponentSequenceNumberE;

    @TableField("business_format_code_e")
    private String businessFormatCodeE;

    @TableField("vfc_payment_credential_e")
    private String vfcPaymentCredentialE;

    @TableField("account_rule_identifier_e")
    private String accountRuleIdentifierE;

    @TableField("reserved_e")
    private String reservedE;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    public String getTransactionCode() {
        return transactionCode;
    }

    public void setTransactionCode(String transactionCode) {
        this.transactionCode = transactionCode;
    }
    public String getTransactionCodeQualifier0() {
        return transactionCodeQualifier0;
    }

    public void setTransactionCodeQualifier0(String transactionCodeQualifier0) {
        this.transactionCodeQualifier0 = transactionCodeQualifier0;
    }
    public String getTransactionComponentSequenceNumber0() {
        return transactionComponentSequenceNumber0;
    }

    public void setTransactionComponentSequenceNumber0(String transactionComponentSequenceNumber0) {
        this.transactionComponentSequenceNumber0 = transactionComponentSequenceNumber0;
    }
    public String getAccountNumber0() {
        return accountNumber0;
    }

    public void setAccountNumber0(String accountNumber0) {
        this.accountNumber0 = accountNumber0;
    }
    public String getAccountNumberExtension0() {
        return accountNumberExtension0;
    }

    public void setAccountNumberExtension0(String accountNumberExtension0) {
        this.accountNumberExtension0 = accountNumberExtension0;
    }
    public String getFloorLimitIndicator0() {
        return floorLimitIndicator0;
    }

    public void setFloorLimitIndicator0(String floorLimitIndicator0) {
        this.floorLimitIndicator0 = floorLimitIndicator0;
    }
    public String getCrbOrExceptionFileIndicator0() {
        return crbOrExceptionFileIndicator0;
    }

    public void setCrbOrExceptionFileIndicator0(String crbOrExceptionFileIndicator0) {
        this.crbOrExceptionFileIndicator0 = crbOrExceptionFileIndicator0;
    }
    public String getReserved10() {
        return reserved10;
    }

    public void setReserved10(String reserved10) {
        this.reserved10 = reserved10;
    }
    public String getAcquirerReferenceNumber0() {
        return acquirerReferenceNumber0;
    }

    public void setAcquirerReferenceNumber0(String acquirerReferenceNumber0) {
        this.acquirerReferenceNumber0 = acquirerReferenceNumber0;
    }
    public String getAcquirerBusinessId0() {
        return acquirerBusinessId0;
    }

    public void setAcquirerBusinessId0(String acquirerBusinessId0) {
        this.acquirerBusinessId0 = acquirerBusinessId0;
    }
    public String getPurchaseDate0() {
        return purchaseDate0;
    }

    public void setPurchaseDate0(String purchaseDate0) {
        this.purchaseDate0 = purchaseDate0;
    }
    public String getDestinationAmount0() {
        return destinationAmount0;
    }

    public void setDestinationAmount0(String destinationAmount0) {
        this.destinationAmount0 = destinationAmount0;
    }
    public String getDestinationCurrencyCode0() {
        return destinationCurrencyCode0;
    }

    public void setDestinationCurrencyCode0(String destinationCurrencyCode0) {
        this.destinationCurrencyCode0 = destinationCurrencyCode0;
    }
    public String getSourceAmount0() {
        return sourceAmount0;
    }

    public void setSourceAmount0(String sourceAmount0) {
        this.sourceAmount0 = sourceAmount0;
    }
    public String getSourceCurrencyCode0() {
        return sourceCurrencyCode0;
    }

    public void setSourceCurrencyCode0(String sourceCurrencyCode0) {
        this.sourceCurrencyCode0 = sourceCurrencyCode0;
    }
    public String getMerchantName0() {
        return merchantName0;
    }

    public void setMerchantName0(String merchantName0) {
        this.merchantName0 = merchantName0;
    }
    public String getMerchantCity0() {
        return merchantCity0;
    }

    public void setMerchantCity0(String merchantCity0) {
        this.merchantCity0 = merchantCity0;
    }
    public String getMerchantCountryCode0() {
        return merchantCountryCode0;
    }

    public void setMerchantCountryCode0(String merchantCountryCode0) {
        this.merchantCountryCode0 = merchantCountryCode0;
    }
    public String getMerchantCategoryCode0() {
        return merchantCategoryCode0;
    }

    public void setMerchantCategoryCode0(String merchantCategoryCode0) {
        this.merchantCategoryCode0 = merchantCategoryCode0;
    }
    public String getMerchantZipCode0() {
        return merchantZipCode0;
    }

    public void setMerchantZipCode0(String merchantZipCode0) {
        this.merchantZipCode0 = merchantZipCode0;
    }
    public String getMerchantStateOrProvinceCode0() {
        return merchantStateOrProvinceCode0;
    }

    public void setMerchantStateOrProvinceCode0(String merchantStateOrProvinceCode0) {
        this.merchantStateOrProvinceCode0 = merchantStateOrProvinceCode0;
    }
    public String getRequestedPaymentService0() {
        return requestedPaymentService0;
    }

    public void setRequestedPaymentService0(String requestedPaymentService0) {
        this.requestedPaymentService0 = requestedPaymentService0;
    }
    public String getNumberOfPaymentForms0() {
        return numberOfPaymentForms0;
    }

    public void setNumberOfPaymentForms0(String numberOfPaymentForms0) {
        this.numberOfPaymentForms0 = numberOfPaymentForms0;
    }
    public String getUsageCode0() {
        return usageCode0;
    }

    public void setUsageCode0(String usageCode0) {
        this.usageCode0 = usageCode0;
    }
    public String getReasonCode0() {
        return reasonCode0;
    }

    public void setReasonCode0(String reasonCode0) {
        this.reasonCode0 = reasonCode0;
    }
    public String getSettlementFlag0() {
        return settlementFlag0;
    }

    public void setSettlementFlag0(String settlementFlag0) {
        this.settlementFlag0 = settlementFlag0;
    }
    public String getAuthorizationCharacteristicsIndicator0() {
        return authorizationCharacteristicsIndicator0;
    }

    public void setAuthorizationCharacteristicsIndicator0(String authorizationCharacteristicsIndicator0) {
        this.authorizationCharacteristicsIndicator0 = authorizationCharacteristicsIndicator0;
    }
    public String getAuthorizationCode0() {
        return authorizationCode0;
    }

    public void setAuthorizationCode0(String authorizationCode0) {
        this.authorizationCode0 = authorizationCode0;
    }
    public String getPosTerminalCapability0() {
        return posTerminalCapability0;
    }

    public void setPosTerminalCapability0(String posTerminalCapability0) {
        this.posTerminalCapability0 = posTerminalCapability0;
    }
    public String getReserved20() {
        return reserved20;
    }

    public void setReserved20(String reserved20) {
        this.reserved20 = reserved20;
    }
    public String getCardholderIdMethod0() {
        return cardholderIdMethod0;
    }

    public void setCardholderIdMethod0(String cardholderIdMethod0) {
        this.cardholderIdMethod0 = cardholderIdMethod0;
    }
    public String getCollectionOnlyFlag0() {
        return collectionOnlyFlag0;
    }

    public void setCollectionOnlyFlag0(String collectionOnlyFlag0) {
        this.collectionOnlyFlag0 = collectionOnlyFlag0;
    }
    public String getPosEntryMode0() {
        return posEntryMode0;
    }

    public void setPosEntryMode0(String posEntryMode0) {
        this.posEntryMode0 = posEntryMode0;
    }
    public String getCentralProcessingDate0() {
        return centralProcessingDate0;
    }

    public void setCentralProcessingDate0(String centralProcessingDate0) {
        this.centralProcessingDate0 = centralProcessingDate0;
    }
    public String getReimbursementAttribute0() {
        return reimbursementAttribute0;
    }

    public void setReimbursementAttribute0(String reimbursementAttribute0) {
        this.reimbursementAttribute0 = reimbursementAttribute0;
    }
    public String getTransactionCodeQualifier1() {
        return transactionCodeQualifier1;
    }

    public void setTransactionCodeQualifier1(String transactionCodeQualifier1) {
        this.transactionCodeQualifier1 = transactionCodeQualifier1;
    }
    public String getTransactionComponentSequenceNumber1() {
        return transactionComponentSequenceNumber1;
    }

    public void setTransactionComponentSequenceNumber1(String transactionComponentSequenceNumber1) {
        this.transactionComponentSequenceNumber1 = transactionComponentSequenceNumber1;
    }
    public String getBusinessFormatCode1() {
        return businessFormatCode1;
    }

    public void setBusinessFormatCode1(String businessFormatCode1) {
        this.businessFormatCode1 = businessFormatCode1;
    }
    public String getTokenAssuranceMethod1() {
        return tokenAssuranceMethod1;
    }

    public void setTokenAssuranceMethod1(String tokenAssuranceMethod1) {
        this.tokenAssuranceMethod1 = tokenAssuranceMethod1;
    }
    public String getRateTableId1() {
        return rateTableId1;
    }

    public void setRateTableId1(String rateTableId1) {
        this.rateTableId1 = rateTableId1;
    }
    public String getReserved11() {
        return reserved11;
    }

    public void setReserved11(String reserved11) {
        this.reserved11 = reserved11;
    }
    public String getReserved21() {
        return reserved21;
    }

    public void setReserved21(String reserved21) {
        this.reserved21 = reserved21;
    }
    public String getDocumentationIndicator1() {
        return documentationIndicator1;
    }

    public void setDocumentationIndicator1(String documentationIndicator1) {
        this.documentationIndicator1 = documentationIndicator1;
    }
    public String getMemberMessageText1() {
        return memberMessageText1;
    }

    public void setMemberMessageText1(String memberMessageText1) {
        this.memberMessageText1 = memberMessageText1;
    }
    public String getSpecialConditionIndicators1() {
        return specialConditionIndicators1;
    }

    public void setSpecialConditionIndicators1(String specialConditionIndicators1) {
        this.specialConditionIndicators1 = specialConditionIndicators1;
    }
    public String getFeeProgramIndicator1() {
        return feeProgramIndicator1;
    }

    public void setFeeProgramIndicator1(String feeProgramIndicator1) {
        this.feeProgramIndicator1 = feeProgramIndicator1;
    }
    public String getIssuerCharge1() {
        return issuerCharge1;
    }

    public void setIssuerCharge1(String issuerCharge1) {
        this.issuerCharge1 = issuerCharge1;
    }
    public String getPersistentFxAppliedIndicator1() {
        return persistentFxAppliedIndicator1;
    }

    public void setPersistentFxAppliedIndicator1(String persistentFxAppliedIndicator1) {
        this.persistentFxAppliedIndicator1 = persistentFxAppliedIndicator1;
    }
    public String getCardAcceptorId1() {
        return cardAcceptorId1;
    }

    public void setCardAcceptorId1(String cardAcceptorId1) {
        this.cardAcceptorId1 = cardAcceptorId1;
    }
    public String getTerminalId1() {
        return terminalId1;
    }

    public void setTerminalId1(String terminalId1) {
        this.terminalId1 = terminalId1;
    }
    public String getNationalReimbursementFee1() {
        return nationalReimbursementFee1;
    }

    public void setNationalReimbursementFee1(String nationalReimbursementFee1) {
        this.nationalReimbursementFee1 = nationalReimbursementFee1;
    }
    public String getMailOrPhoneOrElectronicCommerceAndPaymentIndicator1() {
        return mailOrPhoneOrElectronicCommerceAndPaymentIndicator1;
    }

    public void setMailOrPhoneOrElectronicCommerceAndPaymentIndicator1(String mailOrPhoneOrElectronicCommerceAndPaymentIndicator1) {
        this.mailOrPhoneOrElectronicCommerceAndPaymentIndicator1 = mailOrPhoneOrElectronicCommerceAndPaymentIndicator1;
    }
    public String getSpecialChargebackIndicator1() {
        return specialChargebackIndicator1;
    }

    public void setSpecialChargebackIndicator1(String specialChargebackIndicator1) {
        this.specialChargebackIndicator1 = specialChargebackIndicator1;
    }
    public String getConversionDate1() {
        return conversionDate1;
    }

    public void setConversionDate1(String conversionDate1) {
        this.conversionDate1 = conversionDate1;
    }
    public String getAdditionalTokenResponseInformation1() {
        return additionalTokenResponseInformation1;
    }

    public void setAdditionalTokenResponseInformation1(String additionalTokenResponseInformation1) {
        this.additionalTokenResponseInformation1 = additionalTokenResponseInformation1;
    }
    public String getReserved31() {
        return reserved31;
    }

    public void setReserved31(String reserved31) {
        this.reserved31 = reserved31;
    }
    public String getAcceptanceTerminalIndicator1() {
        return acceptanceTerminalIndicator1;
    }

    public void setAcceptanceTerminalIndicator1(String acceptanceTerminalIndicator1) {
        this.acceptanceTerminalIndicator1 = acceptanceTerminalIndicator1;
    }
    public String getPrepaidCardIndicator1() {
        return prepaidCardIndicator1;
    }

    public void setPrepaidCardIndicator1(String prepaidCardIndicator1) {
        this.prepaidCardIndicator1 = prepaidCardIndicator1;
    }
    public String getServiceDevelopmentField1() {
        return serviceDevelopmentField1;
    }

    public void setServiceDevelopmentField1(String serviceDevelopmentField1) {
        this.serviceDevelopmentField1 = serviceDevelopmentField1;
    }
    public String getAvsResponseCode1() {
        return avsResponseCode1;
    }

    public void setAvsResponseCode1(String avsResponseCode1) {
        this.avsResponseCode1 = avsResponseCode1;
    }
    public String getAuthorizationSourceCode1() {
        return authorizationSourceCode1;
    }

    public void setAuthorizationSourceCode1(String authorizationSourceCode1) {
        this.authorizationSourceCode1 = authorizationSourceCode1;
    }
    public String getPurchaseIdentifierFormat1() {
        return purchaseIdentifierFormat1;
    }

    public void setPurchaseIdentifierFormat1(String purchaseIdentifierFormat1) {
        this.purchaseIdentifierFormat1 = purchaseIdentifierFormat1;
    }
    public String getAccountSelection1() {
        return accountSelection1;
    }

    public void setAccountSelection1(String accountSelection1) {
        this.accountSelection1 = accountSelection1;
    }
    public String getInstallmentPaymentCount1() {
        return installmentPaymentCount1;
    }

    public void setInstallmentPaymentCount1(String installmentPaymentCount1) {
        this.installmentPaymentCount1 = installmentPaymentCount1;
    }
    public String getPurchaseIdentifier1() {
        return purchaseIdentifier1;
    }

    public void setPurchaseIdentifier1(String purchaseIdentifier1) {
        this.purchaseIdentifier1 = purchaseIdentifier1;
    }
    public String getCashback1() {
        return cashback1;
    }

    public void setCashback1(String cashback1) {
        this.cashback1 = cashback1;
    }
    public String getChipConditionCode1() {
        return chipConditionCode1;
    }

    public void setChipConditionCode1(String chipConditionCode1) {
        this.chipConditionCode1 = chipConditionCode1;
    }
    public String getPosEnvironment1() {
        return posEnvironment1;
    }

    public void setPosEnvironment1(String posEnvironment1) {
        this.posEnvironment1 = posEnvironment1;
    }
    public String getTransactionCodeQualifier4Sd() {
        return transactionCodeQualifier4Sd;
    }

    public void setTransactionCodeQualifier4Sd(String transactionCodeQualifier4Sd) {
        this.transactionCodeQualifier4Sd = transactionCodeQualifier4Sd;
    }
    public String getTransactionComponentSequenceNumber4Sd() {
        return transactionComponentSequenceNumber4Sd;
    }

    public void setTransactionComponentSequenceNumber4Sd(String transactionComponentSequenceNumber4Sd) {
        this.transactionComponentSequenceNumber4Sd = transactionComponentSequenceNumber4Sd;
    }
    public String getAgentUniqueId4Sd() {
        return agentUniqueId4Sd;
    }

    public void setAgentUniqueId4Sd(String agentUniqueId4Sd) {
        this.agentUniqueId4Sd = agentUniqueId4Sd;
    }
    public String getReserved14Sd() {
        return reserved14Sd;
    }

    public void setReserved14Sd(String reserved14Sd) {
        this.reserved14Sd = reserved14Sd;
    }
    public String getNetworkIdentificationCode4Sd() {
        return networkIdentificationCode4Sd;
    }

    public void setNetworkIdentificationCode4Sd(String networkIdentificationCode4Sd) {
        this.networkIdentificationCode4Sd = networkIdentificationCode4Sd;
    }
    public String getContactForInformation4Sd() {
        return contactForInformation4Sd;
    }

    public void setContactForInformation4Sd(String contactForInformation4Sd) {
        this.contactForInformation4Sd = contactForInformation4Sd;
    }
    public String getAdjustmentProcessingIndicator4Sd() {
        return adjustmentProcessingIndicator4Sd;
    }

    public void setAdjustmentProcessingIndicator4Sd(String adjustmentProcessingIndicator4Sd) {
        this.adjustmentProcessingIndicator4Sd = adjustmentProcessingIndicator4Sd;
    }
    public String getMessageReasonCode4Sd() {
        return messageReasonCode4Sd;
    }

    public void setMessageReasonCode4Sd(String messageReasonCode4Sd) {
        this.messageReasonCode4Sd = messageReasonCode4Sd;
    }
    public String getSurchargeAmount4Sd() {
        return surchargeAmount4Sd;
    }

    public void setSurchargeAmount4Sd(String surchargeAmount4Sd) {
        this.surchargeAmount4Sd = surchargeAmount4Sd;
    }
    public String getSurchargeCreditDebitIndicator4Sd() {
        return surchargeCreditDebitIndicator4Sd;
    }

    public void setSurchargeCreditDebitIndicator4Sd(String surchargeCreditDebitIndicator4Sd) {
        this.surchargeCreditDebitIndicator4Sd = surchargeCreditDebitIndicator4Sd;
    }
    public String getVisaInternalUseOnly4Sd() {
        return visaInternalUseOnly4Sd;
    }

    public void setVisaInternalUseOnly4Sd(String visaInternalUseOnly4Sd) {
        this.visaInternalUseOnly4Sd = visaInternalUseOnly4Sd;
    }
    public String getAdditionalTransactionFee1Amount4Sd() {
        return additionalTransactionFee1Amount4Sd;
    }

    public void setAdditionalTransactionFee1Amount4Sd(String additionalTransactionFee1Amount4Sd) {
        this.additionalTransactionFee1Amount4Sd = additionalTransactionFee1Amount4Sd;
    }
    public String getAdditionalTransactionFee2Amount4Sd() {
        return additionalTransactionFee2Amount4Sd;
    }

    public void setAdditionalTransactionFee2Amount4Sd(String additionalTransactionFee2Amount4Sd) {
        this.additionalTransactionFee2Amount4Sd = additionalTransactionFee2Amount4Sd;
    }
    public String getTotalDiscountAmount4Sd() {
        return totalDiscountAmount4Sd;
    }

    public void setTotalDiscountAmount4Sd(String totalDiscountAmount4Sd) {
        this.totalDiscountAmount4Sd = totalDiscountAmount4Sd;
    }
    public String getReserved24Sd() {
        return reserved24Sd;
    }

    public void setReserved24Sd(String reserved24Sd) {
        this.reserved24Sd = reserved24Sd;
    }
    public String getSurchargeAmountInBillingCurrency4Sd() {
        return surchargeAmountInBillingCurrency4Sd;
    }

    public void setSurchargeAmountInBillingCurrency4Sd(String surchargeAmountInBillingCurrency4Sd) {
        this.surchargeAmountInBillingCurrency4Sd = surchargeAmountInBillingCurrency4Sd;
    }
    public String getMoneyTransferForeignExchangeFee4Sd() {
        return moneyTransferForeignExchangeFee4Sd;
    }

    public void setMoneyTransferForeignExchangeFee4Sd(String moneyTransferForeignExchangeFee4Sd) {
        this.moneyTransferForeignExchangeFee4Sd = moneyTransferForeignExchangeFee4Sd;
    }
    public String getPaymentAccountReference4Sd() {
        return paymentAccountReference4Sd;
    }

    public void setPaymentAccountReference4Sd(String paymentAccountReference4Sd) {
        this.paymentAccountReference4Sd = paymentAccountReference4Sd;
    }
    public String getTokenRequestorId4Sd() {
        return tokenRequestorId4Sd;
    }

    public void setTokenRequestorId4Sd(String tokenRequestorId4Sd) {
        this.tokenRequestorId4Sd = tokenRequestorId4Sd;
    }
    public String getReserved34Sd() {
        return reserved34Sd;
    }

    public void setReserved34Sd(String reserved34Sd) {
        this.reserved34Sd = reserved34Sd;
    }
    public String getTransactionCodeQualifier4Pd() {
        return transactionCodeQualifier4Pd;
    }

    public void setTransactionCodeQualifier4Pd(String transactionCodeQualifier4Pd) {
        this.transactionCodeQualifier4Pd = transactionCodeQualifier4Pd;
    }
    public String getTransactionComponentSequenceNumber4Pd() {
        return transactionComponentSequenceNumber4Pd;
    }

    public void setTransactionComponentSequenceNumber4Pd(String transactionComponentSequenceNumber4Pd) {
        this.transactionComponentSequenceNumber4Pd = transactionComponentSequenceNumber4Pd;
    }
    public String getBusinessFormatCode4Pd() {
        return businessFormatCode4Pd;
    }

    public void setBusinessFormatCode4Pd(String businessFormatCode4Pd) {
        this.businessFormatCode4Pd = businessFormatCode4Pd;
    }
    public String getReserved14Pd() {
        return reserved14Pd;
    }

    public void setReserved14Pd(String reserved14Pd) {
        this.reserved14Pd = reserved14Pd;
    }
    public String getPromotionType4Pd() {
        return promotionType4Pd;
    }

    public void setPromotionType4Pd(String promotionType4Pd) {
        this.promotionType4Pd = promotionType4Pd;
    }
    public String getPromotionCode4Pd() {
        return promotionCode4Pd;
    }

    public void setPromotionCode4Pd(String promotionCode4Pd) {
        this.promotionCode4Pd = promotionCode4Pd;
    }
    public String getReserved24Pd() {
        return reserved24Pd;
    }

    public void setReserved24Pd(String reserved24Pd) {
        this.reserved24Pd = reserved24Pd;
    }
    public String getTransactionCodeQualifier4Df() {
        return transactionCodeQualifier4Df;
    }

    public void setTransactionCodeQualifier4Df(String transactionCodeQualifier4Df) {
        this.transactionCodeQualifier4Df = transactionCodeQualifier4Df;
    }
    public String getTransactionComponentSequenceNumber4Df() {
        return transactionComponentSequenceNumber4Df;
    }

    public void setTransactionComponentSequenceNumber4Df(String transactionComponentSequenceNumber4Df) {
        this.transactionComponentSequenceNumber4Df = transactionComponentSequenceNumber4Df;
    }
    public String getBusinessFormatCode4Df() {
        return businessFormatCode4Df;
    }

    public void setBusinessFormatCode4Df(String businessFormatCode4Df) {
        this.businessFormatCode4Df = businessFormatCode4Df;
    }
    public String getAgentUniqueId4Df() {
        return agentUniqueId4Df;
    }

    public void setAgentUniqueId4Df(String agentUniqueId4Df) {
        this.agentUniqueId4Df = agentUniqueId4Df;
    }
    public String getReserved14Df() {
        return reserved14Df;
    }

    public void setReserved14Df(String reserved14Df) {
        this.reserved14Df = reserved14Df;
    }
    public String getNetworkIdentificationCode4Df() {
        return networkIdentificationCode4Df;
    }

    public void setNetworkIdentificationCode4Df(String networkIdentificationCode4Df) {
        this.networkIdentificationCode4Df = networkIdentificationCode4Df;
    }
    public String getContactForInformation4Df() {
        return contactForInformation4Df;
    }

    public void setContactForInformation4Df(String contactForInformation4Df) {
        this.contactForInformation4Df = contactForInformation4Df;
    }
    public String getAdjustmentProcessingIndicator4Df() {
        return adjustmentProcessingIndicator4Df;
    }

    public void setAdjustmentProcessingIndicator4Df(String adjustmentProcessingIndicator4Df) {
        this.adjustmentProcessingIndicator4Df = adjustmentProcessingIndicator4Df;
    }
    public String getMessageReasonCode4Df() {
        return messageReasonCode4Df;
    }

    public void setMessageReasonCode4Df(String messageReasonCode4Df) {
        this.messageReasonCode4Df = messageReasonCode4Df;
    }
    public String getDisputeCondition4Df() {
        return disputeCondition4Df;
    }

    public void setDisputeCondition4Df(String disputeCondition4Df) {
        this.disputeCondition4Df = disputeCondition4Df;
    }
    public String getVrolFinancialId4Df() {
        return vrolFinancialId4Df;
    }

    public void setVrolFinancialId4Df(String vrolFinancialId4Df) {
        this.vrolFinancialId4Df = vrolFinancialId4Df;
    }
    public String getVrolCaseNumber4Df() {
        return vrolCaseNumber4Df;
    }

    public void setVrolCaseNumber4Df(String vrolCaseNumber4Df) {
        this.vrolCaseNumber4Df = vrolCaseNumber4Df;
    }
    public String getVrolBundleCaseNumber4Df() {
        return vrolBundleCaseNumber4Df;
    }

    public void setVrolBundleCaseNumber4Df(String vrolBundleCaseNumber4Df) {
        this.vrolBundleCaseNumber4Df = vrolBundleCaseNumber4Df;
    }
    public String getClientCaseNumber4Df() {
        return clientCaseNumber4Df;
    }

    public void setClientCaseNumber4Df(String clientCaseNumber4Df) {
        this.clientCaseNumber4Df = clientCaseNumber4Df;
    }
    public String getDisputeStatus4Df() {
        return disputeStatus4Df;
    }

    public void setDisputeStatus4Df(String disputeStatus4Df) {
        this.disputeStatus4Df = disputeStatus4Df;
    }
    public String getSurchargeAmount4Df() {
        return surchargeAmount4Df;
    }

    public void setSurchargeAmount4Df(String surchargeAmount4Df) {
        this.surchargeAmount4Df = surchargeAmount4Df;
    }
    public String getSurchargeCreditDebitIndicator4Df() {
        return surchargeCreditDebitIndicator4Df;
    }

    public void setSurchargeCreditDebitIndicator4Df(String surchargeCreditDebitIndicator4Df) {
        this.surchargeCreditDebitIndicator4Df = surchargeCreditDebitIndicator4Df;
    }
    public String getReserved24Df() {
        return reserved24Df;
    }

    public void setReserved24Df(String reserved24Df) {
        this.reserved24Df = reserved24Df;
    }
    public String getTransactionCodeQualifier4Sp() {
        return transactionCodeQualifier4Sp;
    }

    public void setTransactionCodeQualifier4Sp(String transactionCodeQualifier4Sp) {
        this.transactionCodeQualifier4Sp = transactionCodeQualifier4Sp;
    }
    public String getTransactionComponentSequenceNumber4Sp() {
        return transactionComponentSequenceNumber4Sp;
    }

    public void setTransactionComponentSequenceNumber4Sp(String transactionComponentSequenceNumber4Sp) {
        this.transactionComponentSequenceNumber4Sp = transactionComponentSequenceNumber4Sp;
    }
    public String getBusinessFormatCode4Sp() {
        return businessFormatCode4Sp;
    }

    public void setBusinessFormatCode4Sp(String businessFormatCode4Sp) {
        this.businessFormatCode4Sp = businessFormatCode4Sp;
    }
    public String getAgentUniqueId4Sp() {
        return agentUniqueId4Sp;
    }

    public void setAgentUniqueId4Sp(String agentUniqueId4Sp) {
        this.agentUniqueId4Sp = agentUniqueId4Sp;
    }
    public String getReserved14Sp() {
        return reserved14Sp;
    }

    public void setReserved14Sp(String reserved14Sp) {
        this.reserved14Sp = reserved14Sp;
    }
    public String getNetworkIdentificationCode4Sp() {
        return networkIdentificationCode4Sp;
    }

    public void setNetworkIdentificationCode4Sp(String networkIdentificationCode4Sp) {
        this.networkIdentificationCode4Sp = networkIdentificationCode4Sp;
    }
    public String getContactForInformation4Sp() {
        return contactForInformation4Sp;
    }

    public void setContactForInformation4Sp(String contactForInformation4Sp) {
        this.contactForInformation4Sp = contactForInformation4Sp;
    }
    public String getAdjustmentProcessingIndicator4Sp() {
        return adjustmentProcessingIndicator4Sp;
    }

    public void setAdjustmentProcessingIndicator4Sp(String adjustmentProcessingIndicator4Sp) {
        this.adjustmentProcessingIndicator4Sp = adjustmentProcessingIndicator4Sp;
    }
    public String getMessageReasonCode4Sp() {
        return messageReasonCode4Sp;
    }

    public void setMessageReasonCode4Sp(String messageReasonCode4Sp) {
        this.messageReasonCode4Sp = messageReasonCode4Sp;
    }
    public String getSurchargeAmount4Sp() {
        return surchargeAmount4Sp;
    }

    public void setSurchargeAmount4Sp(String surchargeAmount4Sp) {
        this.surchargeAmount4Sp = surchargeAmount4Sp;
    }
    public String getSurchargeCreditDebitIndicator4Sp() {
        return surchargeCreditDebitIndicator4Sp;
    }

    public void setSurchargeCreditDebitIndicator4Sp(String surchargeCreditDebitIndicator4Sp) {
        this.surchargeCreditDebitIndicator4Sp = surchargeCreditDebitIndicator4Sp;
    }
    public String getVisaInternalUseOnly4Sp() {
        return visaInternalUseOnly4Sp;
    }

    public void setVisaInternalUseOnly4Sp(String visaInternalUseOnly4Sp) {
        this.visaInternalUseOnly4Sp = visaInternalUseOnly4Sp;
    }
    public String getPromotionType4Sp() {
        return promotionType4Sp;
    }

    public void setPromotionType4Sp(String promotionType4Sp) {
        this.promotionType4Sp = promotionType4Sp;
    }
    public String getPromotionCode4Sp() {
        return promotionCode4Sp;
    }

    public void setPromotionCode4Sp(String promotionCode4Sp) {
        this.promotionCode4Sp = promotionCode4Sp;
    }
    public String getSurchargeAmountInCardholderBillingCurrency4Sp() {
        return surchargeAmountInCardholderBillingCurrency4Sp;
    }

    public void setSurchargeAmountInCardholderBillingCurrency4Sp(String surchargeAmountInCardholderBillingCurrency4Sp) {
        this.surchargeAmountInCardholderBillingCurrency4Sp = surchargeAmountInCardholderBillingCurrency4Sp;
    }
    public String getPaymentAccountReference4Sp() {
        return paymentAccountReference4Sp;
    }

    public void setPaymentAccountReference4Sp(String paymentAccountReference4Sp) {
        this.paymentAccountReference4Sp = paymentAccountReference4Sp;
    }
    public String getTokenRequestorId4Sp() {
        return tokenRequestorId4Sp;
    }

    public void setTokenRequestorId4Sp(String tokenRequestorId4Sp) {
        this.tokenRequestorId4Sp = tokenRequestorId4Sp;
    }
    public String getAdditionalTransactionFee1Amount4Sp() {
        return additionalTransactionFee1Amount4Sp;
    }

    public void setAdditionalTransactionFee1Amount4Sp(String additionalTransactionFee1Amount4Sp) {
        this.additionalTransactionFee1Amount4Sp = additionalTransactionFee1Amount4Sp;
    }
    public String getTotalDiscountAmount4Sp() {
        return totalDiscountAmount4Sp;
    }

    public void setTotalDiscountAmount4Sp(String totalDiscountAmount4Sp) {
        this.totalDiscountAmount4Sp = totalDiscountAmount4Sp;
    }
    public String getReserved24Sp() {
        return reserved24Sp;
    }

    public void setReserved24Sp(String reserved24Sp) {
        this.reserved24Sp = reserved24Sp;
    }
    public String getTransactionCodeQualifier5() {
        return transactionCodeQualifier5;
    }

    public void setTransactionCodeQualifier5(String transactionCodeQualifier5) {
        this.transactionCodeQualifier5 = transactionCodeQualifier5;
    }
    public String getTransactionComponentSequenceNumber5() {
        return transactionComponentSequenceNumber5;
    }

    public void setTransactionComponentSequenceNumber5(String transactionComponentSequenceNumber5) {
        this.transactionComponentSequenceNumber5 = transactionComponentSequenceNumber5;
    }
    public String getTransactionIdentifier5() {
        return transactionIdentifier5;
    }

    public void setTransactionIdentifier5(String transactionIdentifier5) {
        this.transactionIdentifier5 = transactionIdentifier5;
    }
    public String getAuthorizedAmount5() {
        return authorizedAmount5;
    }

    public void setAuthorizedAmount5(String authorizedAmount5) {
        this.authorizedAmount5 = authorizedAmount5;
    }
    public String getAuthorizationCurrencyCode5() {
        return authorizationCurrencyCode5;
    }

    public void setAuthorizationCurrencyCode5(String authorizationCurrencyCode5) {
        this.authorizationCurrencyCode5 = authorizationCurrencyCode5;
    }
    public String getAuthorizationResponseCode5() {
        return authorizationResponseCode5;
    }

    public void setAuthorizationResponseCode5(String authorizationResponseCode5) {
        this.authorizationResponseCode5 = authorizationResponseCode5;
    }
    public String getValidationCode5() {
        return validationCode5;
    }

    public void setValidationCode5(String validationCode5) {
        this.validationCode5 = validationCode5;
    }
    public String getExcludedTransactionIdentifierReason5() {
        return excludedTransactionIdentifierReason5;
    }

    public void setExcludedTransactionIdentifierReason5(String excludedTransactionIdentifierReason5) {
        this.excludedTransactionIdentifierReason5 = excludedTransactionIdentifierReason5;
    }
    public String getReserved15() {
        return reserved15;
    }

    public void setReserved15(String reserved15) {
        this.reserved15 = reserved15;
    }
    public String getReserved25() {
        return reserved25;
    }

    public void setReserved25(String reserved25) {
        this.reserved25 = reserved25;
    }
    public String getMultipleClearingSequenceNumber5() {
        return multipleClearingSequenceNumber5;
    }

    public void setMultipleClearingSequenceNumber5(String multipleClearingSequenceNumber5) {
        this.multipleClearingSequenceNumber5 = multipleClearingSequenceNumber5;
    }
    public String getMultipleClearingSequenceCount5() {
        return multipleClearingSequenceCount5;
    }

    public void setMultipleClearingSequenceCount5(String multipleClearingSequenceCount5) {
        this.multipleClearingSequenceCount5 = multipleClearingSequenceCount5;
    }
    public String getMarketSpecificAuthorizationDataIndicator5() {
        return marketSpecificAuthorizationDataIndicator5;
    }

    public void setMarketSpecificAuthorizationDataIndicator5(String marketSpecificAuthorizationDataIndicator5) {
        this.marketSpecificAuthorizationDataIndicator5 = marketSpecificAuthorizationDataIndicator5;
    }
    public String getTotalAuthorizedAmount5() {
        return totalAuthorizedAmount5;
    }

    public void setTotalAuthorizedAmount5(String totalAuthorizedAmount5) {
        this.totalAuthorizedAmount5 = totalAuthorizedAmount5;
    }
    public String getInformationIndicator5() {
        return informationIndicator5;
    }

    public void setInformationIndicator5(String informationIndicator5) {
        this.informationIndicator5 = informationIndicator5;
    }
    public String getMerchantTelephoneNumber5() {
        return merchantTelephoneNumber5;
    }

    public void setMerchantTelephoneNumber5(String merchantTelephoneNumber5) {
        this.merchantTelephoneNumber5 = merchantTelephoneNumber5;
    }
    public String getAdditionalDataIndicator5() {
        return additionalDataIndicator5;
    }

    public void setAdditionalDataIndicator5(String additionalDataIndicator5) {
        this.additionalDataIndicator5 = additionalDataIndicator5;
    }
    public String getMerchantVolumeIndicator5() {
        return merchantVolumeIndicator5;
    }

    public void setMerchantVolumeIndicator5(String merchantVolumeIndicator5) {
        this.merchantVolumeIndicator5 = merchantVolumeIndicator5;
    }
    public String getElectronicCommerceGoodsIndicator5() {
        return electronicCommerceGoodsIndicator5;
    }

    public void setElectronicCommerceGoodsIndicator5(String electronicCommerceGoodsIndicator5) {
        this.electronicCommerceGoodsIndicator5 = electronicCommerceGoodsIndicator5;
    }
    public String getMerchantVerificationValue5() {
        return merchantVerificationValue5;
    }

    public void setMerchantVerificationValue5(String merchantVerificationValue5) {
        this.merchantVerificationValue5 = merchantVerificationValue5;
    }
    public String getInterchangeFeeAmount5() {
        return interchangeFeeAmount5;
    }

    public void setInterchangeFeeAmount5(String interchangeFeeAmount5) {
        this.interchangeFeeAmount5 = interchangeFeeAmount5;
    }
    public String getInterchangeFeeSign5() {
        return interchangeFeeSign5;
    }

    public void setInterchangeFeeSign5(String interchangeFeeSign5) {
        this.interchangeFeeSign5 = interchangeFeeSign5;
    }
    public String getSourceCurrencyToBaseCurrencyExchangeRate5() {
        return sourceCurrencyToBaseCurrencyExchangeRate5;
    }

    public void setSourceCurrencyToBaseCurrencyExchangeRate5(String sourceCurrencyToBaseCurrencyExchangeRate5) {
        this.sourceCurrencyToBaseCurrencyExchangeRate5 = sourceCurrencyToBaseCurrencyExchangeRate5;
    }
    public String getBaseCurrencyToDestinationCurrencyExchangeRate5() {
        return baseCurrencyToDestinationCurrencyExchangeRate5;
    }

    public void setBaseCurrencyToDestinationCurrencyExchangeRate5(String baseCurrencyToDestinationCurrencyExchangeRate5) {
        this.baseCurrencyToDestinationCurrencyExchangeRate5 = baseCurrencyToDestinationCurrencyExchangeRate5;
    }
    public String getOptionalIssuerIsaAmount5() {
        return optionalIssuerIsaAmount5;
    }

    public void setOptionalIssuerIsaAmount5(String optionalIssuerIsaAmount5) {
        this.optionalIssuerIsaAmount5 = optionalIssuerIsaAmount5;
    }
    public String getProductId5() {
        return productId5;
    }

    public void setProductId5(String productId5) {
        this.productId5 = productId5;
    }
    public String getProgramId5() {
        return programId5;
    }

    public void setProgramId5(String programId5) {
        this.programId5 = programId5;
    }
    public String getDynamicCurrencyConversionIndicator5() {
        return dynamicCurrencyConversionIndicator5;
    }

    public void setDynamicCurrencyConversionIndicator5(String dynamicCurrencyConversionIndicator5) {
        this.dynamicCurrencyConversionIndicator5 = dynamicCurrencyConversionIndicator5;
    }
    public String getAccountTypeIdentification5() {
        return accountTypeIdentification5;
    }

    public void setAccountTypeIdentification5(String accountTypeIdentification5) {
        this.accountTypeIdentification5 = accountTypeIdentification5;
    }
    public String getSpendQualifiedIndicator5() {
        return spendQualifiedIndicator5;
    }

    public void setSpendQualifiedIndicator5(String spendQualifiedIndicator5) {
        this.spendQualifiedIndicator5 = spendQualifiedIndicator5;
    }
    public String getPanToken5() {
        return panToken5;
    }

    public void setPanToken5(String panToken5) {
        this.panToken5 = panToken5;
    }
    public String getReserved35() {
        return reserved35;
    }

    public void setReserved35(String reserved35) {
        this.reserved35 = reserved35;
    }
    public String getAccountFundingSource5() {
        return accountFundingSource5;
    }

    public void setAccountFundingSource5(String accountFundingSource5) {
        this.accountFundingSource5 = accountFundingSource5;
    }
    public String getCvv2ResultCode5() {
        return cvv2ResultCode5;
    }

    public void setCvv2ResultCode5(String cvv2ResultCode5) {
        this.cvv2ResultCode5 = cvv2ResultCode5;
    }
    public String getTransactionCodeQualifier6() {
        return transactionCodeQualifier6;
    }

    public void setTransactionCodeQualifier6(String transactionCodeQualifier6) {
        this.transactionCodeQualifier6 = transactionCodeQualifier6;
    }
    public String getTransactionComponentSequenceNumber6() {
        return transactionComponentSequenceNumber6;
    }

    public void setTransactionComponentSequenceNumber6(String transactionComponentSequenceNumber6) {
        this.transactionComponentSequenceNumber6 = transactionComponentSequenceNumber6;
    }
    public String getLocalTax6() {
        return localTax6;
    }

    public void setLocalTax6(String localTax6) {
        this.localTax6 = localTax6;
    }
    public String getLocalTaxIncluded6() {
        return localTaxIncluded6;
    }

    public void setLocalTaxIncluded6(String localTaxIncluded6) {
        this.localTaxIncluded6 = localTaxIncluded6;
    }
    public String getNationalTax6() {
        return nationalTax6;
    }

    public void setNationalTax6(String nationalTax6) {
        this.nationalTax6 = nationalTax6;
    }
    public String getNationalTaxIncluded6() {
        return nationalTaxIncluded6;
    }

    public void setNationalTaxIncluded6(String nationalTaxIncluded6) {
        this.nationalTaxIncluded6 = nationalTaxIncluded6;
    }
    public String getMerchantVatRegistrationBusinessReferenceNumber6() {
        return merchantVatRegistrationBusinessReferenceNumber6;
    }

    public void setMerchantVatRegistrationBusinessReferenceNumber6(String merchantVatRegistrationBusinessReferenceNumber6) {
        this.merchantVatRegistrationBusinessReferenceNumber6 = merchantVatRegistrationBusinessReferenceNumber6;
    }
    public String getCustomerVatRegisrationNumber6() {
        return customerVatRegisrationNumber6;
    }

    public void setCustomerVatRegisrationNumber6(String customerVatRegisrationNumber6) {
        this.customerVatRegisrationNumber6 = customerVatRegisrationNumber6;
    }
    public String getVisaMerchantIdentifier6() {
        return visaMerchantIdentifier6;
    }

    public void setVisaMerchantIdentifier6(String visaMerchantIdentifier6) {
        this.visaMerchantIdentifier6 = visaMerchantIdentifier6;
    }
    public String getReserved6() {
        return reserved6;
    }

    public void setReserved6(String reserved6) {
        this.reserved6 = reserved6;
    }
    public String getSummaryCommodityCode6() {
        return summaryCommodityCode6;
    }

    public void setSummaryCommodityCode6(String summaryCommodityCode6) {
        this.summaryCommodityCode6 = summaryCommodityCode6;
    }
    public String getOtherTax6() {
        return otherTax6;
    }

    public void setOtherTax6(String otherTax6) {
        this.otherTax6 = otherTax6;
    }
    public String getMessageIdentifier6() {
        return messageIdentifier6;
    }

    public void setMessageIdentifier6(String messageIdentifier6) {
        this.messageIdentifier6 = messageIdentifier6;
    }
    public String getTimeOfPurchase6() {
        return timeOfPurchase6;
    }

    public void setTimeOfPurchase6(String timeOfPurchase6) {
        this.timeOfPurchase6 = timeOfPurchase6;
    }
    public String getCustomerCodeReferenceIdentifierCri6() {
        return customerCodeReferenceIdentifierCri6;
    }

    public void setCustomerCodeReferenceIdentifierCri6(String customerCodeReferenceIdentifierCri6) {
        this.customerCodeReferenceIdentifierCri6 = customerCodeReferenceIdentifierCri6;
    }
    public String getNonFuelProductCode16() {
        return nonFuelProductCode16;
    }

    public void setNonFuelProductCode16(String nonFuelProductCode16) {
        this.nonFuelProductCode16 = nonFuelProductCode16;
    }
    public String getNonFuelProductCode26() {
        return nonFuelProductCode26;
    }

    public void setNonFuelProductCode26(String nonFuelProductCode26) {
        this.nonFuelProductCode26 = nonFuelProductCode26;
    }
    public String getNonFuelProductCode36() {
        return nonFuelProductCode36;
    }

    public void setNonFuelProductCode36(String nonFuelProductCode36) {
        this.nonFuelProductCode36 = nonFuelProductCode36;
    }
    public String getNonFuelProductCode46() {
        return nonFuelProductCode46;
    }

    public void setNonFuelProductCode46(String nonFuelProductCode46) {
        this.nonFuelProductCode46 = nonFuelProductCode46;
    }
    public String getNonFuelProductCode56() {
        return nonFuelProductCode56;
    }

    public void setNonFuelProductCode56(String nonFuelProductCode56) {
        this.nonFuelProductCode56 = nonFuelProductCode56;
    }
    public String getNonFuelProductCode66() {
        return nonFuelProductCode66;
    }

    public void setNonFuelProductCode66(String nonFuelProductCode66) {
        this.nonFuelProductCode66 = nonFuelProductCode66;
    }
    public String getNonFuelProductCode76() {
        return nonFuelProductCode76;
    }

    public void setNonFuelProductCode76(String nonFuelProductCode76) {
        this.nonFuelProductCode76 = nonFuelProductCode76;
    }
    public String getNonFuelProductCode86() {
        return nonFuelProductCode86;
    }

    public void setNonFuelProductCode86(String nonFuelProductCode86) {
        this.nonFuelProductCode86 = nonFuelProductCode86;
    }
    public String getMerchantPostalCode6() {
        return merchantPostalCode6;
    }

    public void setMerchantPostalCode6(String merchantPostalCode6) {
        this.merchantPostalCode6 = merchantPostalCode6;
    }
    public String getReserved26() {
        return reserved26;
    }

    public void setReserved26(String reserved26) {
        this.reserved26 = reserved26;
    }
    public String getTransactionCodeQualifier7() {
        return transactionCodeQualifier7;
    }

    public void setTransactionCodeQualifier7(String transactionCodeQualifier7) {
        this.transactionCodeQualifier7 = transactionCodeQualifier7;
    }
    public String getTransactionComponentSequenceNumber7() {
        return transactionComponentSequenceNumber7;
    }

    public void setTransactionComponentSequenceNumber7(String transactionComponentSequenceNumber7) {
        this.transactionComponentSequenceNumber7 = transactionComponentSequenceNumber7;
    }
    public String getTransactionType7() {
        return transactionType7;
    }

    public void setTransactionType7(String transactionType7) {
        this.transactionType7 = transactionType7;
    }
    public String getCardSequenceNumber7() {
        return cardSequenceNumber7;
    }

    public void setCardSequenceNumber7(String cardSequenceNumber7) {
        this.cardSequenceNumber7 = cardSequenceNumber7;
    }
    public String getTerminalTransactionDate7() {
        return terminalTransactionDate7;
    }

    public void setTerminalTransactionDate7(String terminalTransactionDate7) {
        this.terminalTransactionDate7 = terminalTransactionDate7;
    }
    public String getTerminalCapabilityProfile7() {
        return terminalCapabilityProfile7;
    }

    public void setTerminalCapabilityProfile7(String terminalCapabilityProfile7) {
        this.terminalCapabilityProfile7 = terminalCapabilityProfile7;
    }
    public String getTerminalCountryCode7() {
        return terminalCountryCode7;
    }

    public void setTerminalCountryCode7(String terminalCountryCode7) {
        this.terminalCountryCode7 = terminalCountryCode7;
    }
    public String getTerminalSerialNumber7() {
        return terminalSerialNumber7;
    }

    public void setTerminalSerialNumber7(String terminalSerialNumber7) {
        this.terminalSerialNumber7 = terminalSerialNumber7;
    }
    public String getUnpredictableNumber7() {
        return unpredictableNumber7;
    }

    public void setUnpredictableNumber7(String unpredictableNumber7) {
        this.unpredictableNumber7 = unpredictableNumber7;
    }
    public String getApplicationTransactionCounter7() {
        return applicationTransactionCounter7;
    }

    public void setApplicationTransactionCounter7(String applicationTransactionCounter7) {
        this.applicationTransactionCounter7 = applicationTransactionCounter7;
    }
    public String getApplicationInterchangeProfile7() {
        return applicationInterchangeProfile7;
    }

    public void setApplicationInterchangeProfile7(String applicationInterchangeProfile7) {
        this.applicationInterchangeProfile7 = applicationInterchangeProfile7;
    }
    public String getCryptogram7() {
        return cryptogram7;
    }

    public void setCryptogram7(String cryptogram7) {
        this.cryptogram7 = cryptogram7;
    }
    public String getIssuerApplicationDataByte27() {
        return issuerApplicationDataByte27;
    }

    public void setIssuerApplicationDataByte27(String issuerApplicationDataByte27) {
        this.issuerApplicationDataByte27 = issuerApplicationDataByte27;
    }
    public String getIssuerApplicationDataByte37() {
        return issuerApplicationDataByte37;
    }

    public void setIssuerApplicationDataByte37(String issuerApplicationDataByte37) {
        this.issuerApplicationDataByte37 = issuerApplicationDataByte37;
    }
    public String getTerminalVerificationResults7() {
        return terminalVerificationResults7;
    }

    public void setTerminalVerificationResults7(String terminalVerificationResults7) {
        this.terminalVerificationResults7 = terminalVerificationResults7;
    }
    public String getIssuerApplicationDataByte47() {
        return issuerApplicationDataByte47;
    }

    public void setIssuerApplicationDataByte47(String issuerApplicationDataByte47) {
        this.issuerApplicationDataByte47 = issuerApplicationDataByte47;
    }
    public String getCryptogramAmount7() {
        return cryptogramAmount7;
    }

    public void setCryptogramAmount7(String cryptogramAmount7) {
        this.cryptogramAmount7 = cryptogramAmount7;
    }
    public String getIssuerApplicationDataByte87() {
        return issuerApplicationDataByte87;
    }

    public void setIssuerApplicationDataByte87(String issuerApplicationDataByte87) {
        this.issuerApplicationDataByte87 = issuerApplicationDataByte87;
    }
    public String getIssuerApplicationDataByte9167() {
        return issuerApplicationDataByte9167;
    }

    public void setIssuerApplicationDataByte9167(String issuerApplicationDataByte9167) {
        this.issuerApplicationDataByte9167 = issuerApplicationDataByte9167;
    }
    public String getIssuerApplicationDataByte17() {
        return issuerApplicationDataByte17;
    }

    public void setIssuerApplicationDataByte17(String issuerApplicationDataByte17) {
        this.issuerApplicationDataByte17 = issuerApplicationDataByte17;
    }
    public String getIssuerApplicationDataByte177() {
        return issuerApplicationDataByte177;
    }

    public void setIssuerApplicationDataByte177(String issuerApplicationDataByte177) {
        this.issuerApplicationDataByte177 = issuerApplicationDataByte177;
    }
    public String getIssuerApplicationDataByte18327() {
        return issuerApplicationDataByte18327;
    }

    public void setIssuerApplicationDataByte18327(String issuerApplicationDataByte18327) {
        this.issuerApplicationDataByte18327 = issuerApplicationDataByte18327;
    }
    public String getFormFactorIndicator7() {
        return formFactorIndicator7;
    }

    public void setFormFactorIndicator7(String formFactorIndicator7) {
        this.formFactorIndicator7 = formFactorIndicator7;
    }
    public String getIssuerScript1Results7() {
        return issuerScript1Results7;
    }

    public void setIssuerScript1Results7(String issuerScript1Results7) {
        this.issuerScript1Results7 = issuerScript1Results7;
    }
    public String getTransactionCodeQualifierDFs() {
        return transactionCodeQualifierDFs;
    }

    public void setTransactionCodeQualifierDFs(String transactionCodeQualifierDFs) {
        this.transactionCodeQualifierDFs = transactionCodeQualifierDFs;
    }
    public String getTransactionComponentSequenceNumberDFs() {
        return transactionComponentSequenceNumberDFs;
    }

    public void setTransactionComponentSequenceNumberDFs(String transactionComponentSequenceNumberDFs) {
        this.transactionComponentSequenceNumberDFs = transactionComponentSequenceNumberDFs;
    }
    public String getBusinessFormatCodeDFs() {
        return businessFormatCodeDFs;
    }

    public void setBusinessFormatCodeDFs(String businessFormatCodeDFs) {
        this.businessFormatCodeDFs = businessFormatCodeDFs;
    }
    public String getChargingPowerOutputCapacityDFs() {
        return chargingPowerOutputCapacityDFs;
    }

    public void setChargingPowerOutputCapacityDFs(String chargingPowerOutputCapacityDFs) {
        this.chargingPowerOutputCapacityDFs = chargingPowerOutputCapacityDFs;
    }
    public String getChargingReasonCodeDFs() {
        return chargingReasonCodeDFs;
    }

    public void setChargingReasonCodeDFs(String chargingReasonCodeDFs) {
        this.chargingReasonCodeDFs = chargingReasonCodeDFs;
    }
    public String getTotalTimePluggedInDFs() {
        return totalTimePluggedInDFs;
    }

    public void setTotalTimePluggedInDFs(String totalTimePluggedInDFs) {
        this.totalTimePluggedInDFs = totalTimePluggedInDFs;
    }
    public String getTotalChargingTimeDFs() {
        return totalChargingTimeDFs;
    }

    public void setTotalChargingTimeDFs(String totalChargingTimeDFs) {
        this.totalChargingTimeDFs = totalChargingTimeDFs;
    }
    public String getStartTimeOfChargeDFs() {
        return startTimeOfChargeDFs;
    }

    public void setStartTimeOfChargeDFs(String startTimeOfChargeDFs) {
        this.startTimeOfChargeDFs = startTimeOfChargeDFs;
    }
    public String getFinishTimeOfChargeDFs() {
        return finishTimeOfChargeDFs;
    }

    public void setFinishTimeOfChargeDFs(String finishTimeOfChargeDFs) {
        this.finishTimeOfChargeDFs = finishTimeOfChargeDFs;
    }
    public String getEstimatedKmMilesAddedDFs() {
        return estimatedKmMilesAddedDFs;
    }

    public void setEstimatedKmMilesAddedDFs(String estimatedKmMilesAddedDFs) {
        this.estimatedKmMilesAddedDFs = estimatedKmMilesAddedDFs;
    }
    public String getCarbonFootprintDFs() {
        return carbonFootprintDFs;
    }

    public void setCarbonFootprintDFs(String carbonFootprintDFs) {
        this.carbonFootprintDFs = carbonFootprintDFs;
    }
    public String getEstimatedVehicleKmMilesAvailableDFs() {
        return estimatedVehicleKmMilesAvailableDFs;
    }

    public void setEstimatedVehicleKmMilesAvailableDFs(String estimatedVehicleKmMilesAvailableDFs) {
        this.estimatedVehicleKmMilesAvailableDFs = estimatedVehicleKmMilesAvailableDFs;
    }
    public String getMaximumPowerDispensedDFs() {
        return maximumPowerDispensedDFs;
    }

    public void setMaximumPowerDispensedDFs(String maximumPowerDispensedDFs) {
        this.maximumPowerDispensedDFs = maximumPowerDispensedDFs;
    }
    public String getConnectorTypeDFs() {
        return connectorTypeDFs;
    }

    public void setConnectorTypeDFs(String connectorTypeDFs) {
        this.connectorTypeDFs = connectorTypeDFs;
    }
    public String getDiscountMethodDFs() {
        return discountMethodDFs;
    }

    public void setDiscountMethodDFs(String discountMethodDFs) {
        this.discountMethodDFs = discountMethodDFs;
    }
    public String getDiscountAgentDFs() {
        return discountAgentDFs;
    }

    public void setDiscountAgentDFs(String discountAgentDFs) {
        this.discountAgentDFs = discountAgentDFs;
    }
    public String getDiscountPlanIdDFs() {
        return discountPlanIdDFs;
    }

    public void setDiscountPlanIdDFs(String discountPlanIdDFs) {
        this.discountPlanIdDFs = discountPlanIdDFs;
    }
    public String getClientIdDFs() {
        return clientIdDFs;
    }

    public void setClientIdDFs(String clientIdDFs) {
        this.clientIdDFs = clientIdDFs;
    }
    public String getReservedDFs() {
        return reservedDFs;
    }

    public void setReservedDFs(String reservedDFs) {
        this.reservedDFs = reservedDFs;
    }
    public String getTransactionCodeQualifierDIp() {
        return transactionCodeQualifierDIp;
    }

    public void setTransactionCodeQualifierDIp(String transactionCodeQualifierDIp) {
        this.transactionCodeQualifierDIp = transactionCodeQualifierDIp;
    }
    public String getTransactionComponentSequenceNumberDIp() {
        return transactionComponentSequenceNumberDIp;
    }

    public void setTransactionComponentSequenceNumberDIp(String transactionComponentSequenceNumberDIp) {
        this.transactionComponentSequenceNumberDIp = transactionComponentSequenceNumberDIp;
    }
    public String getBusinessFormatCodeDIp() {
        return businessFormatCodeDIp;
    }

    public void setBusinessFormatCodeDIp(String businessFormatCodeDIp) {
        this.businessFormatCodeDIp = businessFormatCodeDIp;
    }
    public String getInstalmentPaymentTotalAmountDIp() {
        return instalmentPaymentTotalAmountDIp;
    }

    public void setInstalmentPaymentTotalAmountDIp(String instalmentPaymentTotalAmountDIp) {
        this.instalmentPaymentTotalAmountDIp = instalmentPaymentTotalAmountDIp;
    }
    public String getInstalmentPaymentCurrencyCodeDIp() {
        return instalmentPaymentCurrencyCodeDIp;
    }

    public void setInstalmentPaymentCurrencyCodeDIp(String instalmentPaymentCurrencyCodeDIp) {
        this.instalmentPaymentCurrencyCodeDIp = instalmentPaymentCurrencyCodeDIp;
    }
    public String getNumberOfInstallmentsDIp() {
        return numberOfInstallmentsDIp;
    }

    public void setNumberOfInstallmentsDIp(String numberOfInstallmentsDIp) {
        this.numberOfInstallmentsDIp = numberOfInstallmentsDIp;
    }
    public String getAmountOfEachInstalmentDIp() {
        return amountOfEachInstalmentDIp;
    }

    public void setAmountOfEachInstalmentDIp(String amountOfEachInstalmentDIp) {
        this.amountOfEachInstalmentDIp = amountOfEachInstalmentDIp;
    }
    public String getInstalmentPaymentNumberDIp() {
        return instalmentPaymentNumberDIp;
    }

    public void setInstalmentPaymentNumberDIp(String instalmentPaymentNumberDIp) {
        this.instalmentPaymentNumberDIp = instalmentPaymentNumberDIp;
    }
    public String getFrequencyOfInstallmentsDIp() {
        return frequencyOfInstallmentsDIp;
    }

    public void setFrequencyOfInstallmentsDIp(String frequencyOfInstallmentsDIp) {
        this.frequencyOfInstallmentsDIp = frequencyOfInstallmentsDIp;
    }
    public String getPlanOwnerDIp() {
        return planOwnerDIp;
    }

    public void setPlanOwnerDIp(String planOwnerDIp) {
        this.planOwnerDIp = planOwnerDIp;
    }
    public String getPlanRegistrationSystemIdentifierDIp() {
        return planRegistrationSystemIdentifierDIp;
    }

    public void setPlanRegistrationSystemIdentifierDIp(String planRegistrationSystemIdentifierDIp) {
        this.planRegistrationSystemIdentifierDIp = planRegistrationSystemIdentifierDIp;
    }
    public String getReservedDIp() {
        return reservedDIp;
    }

    public void setReservedDIp(String reservedDIp) {
        this.reservedDIp = reservedDIp;
    }
    public String getTransactionCodeQualifierDRp() {
        return transactionCodeQualifierDRp;
    }

    public void setTransactionCodeQualifierDRp(String transactionCodeQualifierDRp) {
        this.transactionCodeQualifierDRp = transactionCodeQualifierDRp;
    }
    public String getTransactionComponentSequenceNumberDRp() {
        return transactionComponentSequenceNumberDRp;
    }

    public void setTransactionComponentSequenceNumberDRp(String transactionComponentSequenceNumberDRp) {
        this.transactionComponentSequenceNumberDRp = transactionComponentSequenceNumberDRp;
    }
    public String getBusinessFormatCodeDRp() {
        return businessFormatCodeDRp;
    }

    public void setBusinessFormatCodeDRp(String businessFormatCodeDRp) {
        this.businessFormatCodeDRp = businessFormatCodeDRp;
    }
    public String getRecurringPaymentTypeDRp() {
        return recurringPaymentTypeDRp;
    }

    public void setRecurringPaymentTypeDRp(String recurringPaymentTypeDRp) {
        this.recurringPaymentTypeDRp = recurringPaymentTypeDRp;
    }
    public String getPaymentAmountIndicatorPerTransactionDRp() {
        return paymentAmountIndicatorPerTransactionDRp;
    }

    public void setPaymentAmountIndicatorPerTransactionDRp(String paymentAmountIndicatorPerTransactionDRp) {
        this.paymentAmountIndicatorPerTransactionDRp = paymentAmountIndicatorPerTransactionDRp;
    }
    public String getNumberOfRecurringPaymentDRp() {
        return numberOfRecurringPaymentDRp;
    }

    public void setNumberOfRecurringPaymentDRp(String numberOfRecurringPaymentDRp) {
        this.numberOfRecurringPaymentDRp = numberOfRecurringPaymentDRp;
    }
    public String getFrequencyOfRecurringPaymentDRp() {
        return frequencyOfRecurringPaymentDRp;
    }

    public void setFrequencyOfRecurringPaymentDRp(String frequencyOfRecurringPaymentDRp) {
        this.frequencyOfRecurringPaymentDRp = frequencyOfRecurringPaymentDRp;
    }
    public String getRegistrationReferenceNumberDRp() {
        return registrationReferenceNumberDRp;
    }

    public void setRegistrationReferenceNumberDRp(String registrationReferenceNumberDRp) {
        this.registrationReferenceNumberDRp = registrationReferenceNumberDRp;
    }
    public String getMaximumRecurringPaymentAmountDRp() {
        return maximumRecurringPaymentAmountDRp;
    }

    public void setMaximumRecurringPaymentAmountDRp(String maximumRecurringPaymentAmountDRp) {
        this.maximumRecurringPaymentAmountDRp = maximumRecurringPaymentAmountDRp;
    }
    public String getValidationIndicatorDRp() {
        return validationIndicatorDRp;
    }

    public void setValidationIndicatorDRp(String validationIndicatorDRp) {
        this.validationIndicatorDRp = validationIndicatorDRp;
    }
    public String getReservedDRp() {
        return reservedDRp;
    }

    public void setReservedDRp(String reservedDRp) {
        this.reservedDRp = reservedDRp;
    }
    public String getTransactionCodeQualifierDOc() {
        return transactionCodeQualifierDOc;
    }

    public void setTransactionCodeQualifierDOc(String transactionCodeQualifierDOc) {
        this.transactionCodeQualifierDOc = transactionCodeQualifierDOc;
    }
    public String getTransactionComponentSequenceNumberDOc() {
        return transactionComponentSequenceNumberDOc;
    }

    public void setTransactionComponentSequenceNumberDOc(String transactionComponentSequenceNumberDOc) {
        this.transactionComponentSequenceNumberDOc = transactionComponentSequenceNumberDOc;
    }
    public String getBusinessFormatCodeDOc() {
        return businessFormatCodeDOc;
    }

    public void setBusinessFormatCodeDOc(String businessFormatCodeDOc) {
        this.businessFormatCodeDOc = businessFormatCodeDOc;
    }
    public String getRecipientNameDOc() {
        return recipientNameDOc;
    }

    public void setRecipientNameDOc(String recipientNameDOc) {
        this.recipientNameDOc = recipientNameDOc;
    }
    public String getPurposeOfPaymentDOc() {
        return purposeOfPaymentDOc;
    }

    public void setPurposeOfPaymentDOc(String purposeOfPaymentDOc) {
        this.purposeOfPaymentDOc = purposeOfPaymentDOc;
    }
    public String getPreCurrencyConversionAmountDOc() {
        return preCurrencyConversionAmountDOc;
    }

    public void setPreCurrencyConversionAmountDOc(String preCurrencyConversionAmountDOc) {
        this.preCurrencyConversionAmountDOc = preCurrencyConversionAmountDOc;
    }
    public String getPreCurrencyConversionCurrencyCodeDOc() {
        return preCurrencyConversionCurrencyCodeDOc;
    }

    public void setPreCurrencyConversionCurrencyCodeDOc(String preCurrencyConversionCurrencyCodeDOc) {
        this.preCurrencyConversionCurrencyCodeDOc = preCurrencyConversionCurrencyCodeDOc;
    }
    public String getAcceptorLegalBusinessNameDOc() {
        return acceptorLegalBusinessNameDOc;
    }

    public void setAcceptorLegalBusinessNameDOc(String acceptorLegalBusinessNameDOc) {
        this.acceptorLegalBusinessNameDOc = acceptorLegalBusinessNameDOc;
    }
    public String getPaymentFacilitatorNameDOc() {
        return paymentFacilitatorNameDOc;
    }

    public void setPaymentFacilitatorNameDOc(String paymentFacilitatorNameDOc) {
        this.paymentFacilitatorNameDOc = paymentFacilitatorNameDOc;
    }
    public String getCustomerReferenceCodeDOc() {
        return customerReferenceCodeDOc;
    }

    public void setCustomerReferenceCodeDOc(String customerReferenceCodeDOc) {
        this.customerReferenceCodeDOc = customerReferenceCodeDOc;
    }
    public String getIdentificationTypeCodeDOc() {
        return identificationTypeCodeDOc;
    }

    public void setIdentificationTypeCodeDOc(String identificationTypeCodeDOc) {
        this.identificationTypeCodeDOc = identificationTypeCodeDOc;
    }
    public String getIdentificationSubtypeDOc() {
        return identificationSubtypeDOc;
    }

    public void setIdentificationSubtypeDOc(String identificationSubtypeDOc) {
        this.identificationSubtypeDOc = identificationSubtypeDOc;
    }
    public String getIdentificationValueDOc() {
        return identificationValueDOc;
    }

    public void setIdentificationValueDOc(String identificationValueDOc) {
        this.identificationValueDOc = identificationValueDOc;
    }
    public String getIdentificationIssuingCountryCodeDOc() {
        return identificationIssuingCountryCodeDOc;
    }

    public void setIdentificationIssuingCountryCodeDOc(String identificationIssuingCountryCodeDOc) {
        this.identificationIssuingCountryCodeDOc = identificationIssuingCountryCodeDOc;
    }
    public String getReservedDOc() {
        return reservedDOc;
    }

    public void setReservedDOc(String reservedDOc) {
        this.reservedDOc = reservedDOc;
    }
    public String getTransactionCodeQualifierE() {
        return transactionCodeQualifierE;
    }

    public void setTransactionCodeQualifierE(String transactionCodeQualifierE) {
        this.transactionCodeQualifierE = transactionCodeQualifierE;
    }
    public String getTransactionComponentSequenceNumberE() {
        return transactionComponentSequenceNumberE;
    }

    public void setTransactionComponentSequenceNumberE(String transactionComponentSequenceNumberE) {
        this.transactionComponentSequenceNumberE = transactionComponentSequenceNumberE;
    }
    public String getBusinessFormatCodeE() {
        return businessFormatCodeE;
    }

    public void setBusinessFormatCodeE(String businessFormatCodeE) {
        this.businessFormatCodeE = businessFormatCodeE;
    }
    public String getVfcPaymentCredentialE() {
        return vfcPaymentCredentialE;
    }

    public void setVfcPaymentCredentialE(String vfcPaymentCredentialE) {
        this.vfcPaymentCredentialE = vfcPaymentCredentialE;
    }
    public String getAccountRuleIdentifierE() {
        return accountRuleIdentifierE;
    }

    public void setAccountRuleIdentifierE(String accountRuleIdentifierE) {
        this.accountRuleIdentifierE = accountRuleIdentifierE;
    }
    public String getReservedE() {
        return reservedE;
    }

    public void setReservedE(String reservedE) {
        this.reservedE = reservedE;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "VisaBase05Data{" +
            "id=" + id +
            ", fileName=" + fileName +
            ", transactionCode=" + transactionCode +
            ", transactionCodeQualifier0=" + transactionCodeQualifier0 +
            ", transactionComponentSequenceNumber0=" + transactionComponentSequenceNumber0 +
            ", accountNumber0=" + accountNumber0 +
            ", accountNumberExtension0=" + accountNumberExtension0 +
            ", floorLimitIndicator0=" + floorLimitIndicator0 +
            ", crbOrExceptionFileIndicator0=" + crbOrExceptionFileIndicator0 +
            ", reserved10=" + reserved10 +
            ", acquirerReferenceNumber0=" + acquirerReferenceNumber0 +
            ", acquirerBusinessId0=" + acquirerBusinessId0 +
            ", purchaseDate0=" + purchaseDate0 +
            ", destinationAmount0=" + destinationAmount0 +
            ", destinationCurrencyCode0=" + destinationCurrencyCode0 +
            ", sourceAmount0=" + sourceAmount0 +
            ", sourceCurrencyCode0=" + sourceCurrencyCode0 +
            ", merchantName0=" + merchantName0 +
            ", merchantCity0=" + merchantCity0 +
            ", merchantCountryCode0=" + merchantCountryCode0 +
            ", merchantCategoryCode0=" + merchantCategoryCode0 +
            ", merchantZipCode0=" + merchantZipCode0 +
            ", merchantStateOrProvinceCode0=" + merchantStateOrProvinceCode0 +
            ", requestedPaymentService0=" + requestedPaymentService0 +
            ", numberOfPaymentForms0=" + numberOfPaymentForms0 +
            ", usageCode0=" + usageCode0 +
            ", reasonCode0=" + reasonCode0 +
            ", settlementFlag0=" + settlementFlag0 +
            ", authorizationCharacteristicsIndicator0=" + authorizationCharacteristicsIndicator0 +
            ", authorizationCode0=" + authorizationCode0 +
            ", posTerminalCapability0=" + posTerminalCapability0 +
            ", reserved20=" + reserved20 +
            ", cardholderIdMethod0=" + cardholderIdMethod0 +
            ", collectionOnlyFlag0=" + collectionOnlyFlag0 +
            ", posEntryMode0=" + posEntryMode0 +
            ", centralProcessingDate0=" + centralProcessingDate0 +
            ", reimbursementAttribute0=" + reimbursementAttribute0 +
            ", transactionCodeQualifier1=" + transactionCodeQualifier1 +
            ", transactionComponentSequenceNumber1=" + transactionComponentSequenceNumber1 +
            ", businessFormatCode1=" + businessFormatCode1 +
            ", tokenAssuranceMethod1=" + tokenAssuranceMethod1 +
            ", rateTableId1=" + rateTableId1 +
            ", reserved11=" + reserved11 +
            ", reserved21=" + reserved21 +
            ", documentationIndicator1=" + documentationIndicator1 +
            ", memberMessageText1=" + memberMessageText1 +
            ", specialConditionIndicators1=" + specialConditionIndicators1 +
            ", feeProgramIndicator1=" + feeProgramIndicator1 +
            ", issuerCharge1=" + issuerCharge1 +
            ", persistentFxAppliedIndicator1=" + persistentFxAppliedIndicator1 +
            ", cardAcceptorId1=" + cardAcceptorId1 +
            ", terminalId1=" + terminalId1 +
            ", nationalReimbursementFee1=" + nationalReimbursementFee1 +
            ", mailOrPhoneOrElectronicCommerceAndPaymentIndicator1=" + mailOrPhoneOrElectronicCommerceAndPaymentIndicator1 +
            ", specialChargebackIndicator1=" + specialChargebackIndicator1 +
            ", conversionDate1=" + conversionDate1 +
            ", additionalTokenResponseInformation1=" + additionalTokenResponseInformation1 +
            ", reserved31=" + reserved31 +
            ", acceptanceTerminalIndicator1=" + acceptanceTerminalIndicator1 +
            ", prepaidCardIndicator1=" + prepaidCardIndicator1 +
            ", serviceDevelopmentField1=" + serviceDevelopmentField1 +
            ", avsResponseCode1=" + avsResponseCode1 +
            ", authorizationSourceCode1=" + authorizationSourceCode1 +
            ", purchaseIdentifierFormat1=" + purchaseIdentifierFormat1 +
            ", accountSelection1=" + accountSelection1 +
            ", installmentPaymentCount1=" + installmentPaymentCount1 +
            ", purchaseIdentifier1=" + purchaseIdentifier1 +
            ", cashback1=" + cashback1 +
            ", chipConditionCode1=" + chipConditionCode1 +
            ", posEnvironment1=" + posEnvironment1 +
            ", transactionCodeQualifier4Sd=" + transactionCodeQualifier4Sd +
            ", transactionComponentSequenceNumber4Sd=" + transactionComponentSequenceNumber4Sd +
            ", agentUniqueId4Sd=" + agentUniqueId4Sd +
            ", reserved14Sd=" + reserved14Sd +
            ", networkIdentificationCode4Sd=" + networkIdentificationCode4Sd +
            ", contactForInformation4Sd=" + contactForInformation4Sd +
            ", adjustmentProcessingIndicator4Sd=" + adjustmentProcessingIndicator4Sd +
            ", messageReasonCode4Sd=" + messageReasonCode4Sd +
            ", surchargeAmount4Sd=" + surchargeAmount4Sd +
            ", surchargeCreditDebitIndicator4Sd=" + surchargeCreditDebitIndicator4Sd +
            ", visaInternalUseOnly4Sd=" + visaInternalUseOnly4Sd +
            ", additionalTransactionFee1Amount4Sd=" + additionalTransactionFee1Amount4Sd +
            ", additionalTransactionFee2Amount4Sd=" + additionalTransactionFee2Amount4Sd +
            ", totalDiscountAmount4Sd=" + totalDiscountAmount4Sd +
            ", reserved24Sd=" + reserved24Sd +
            ", surchargeAmountInBillingCurrency4Sd=" + surchargeAmountInBillingCurrency4Sd +
            ", moneyTransferForeignExchangeFee4Sd=" + moneyTransferForeignExchangeFee4Sd +
            ", paymentAccountReference4Sd=" + paymentAccountReference4Sd +
            ", tokenRequestorId4Sd=" + tokenRequestorId4Sd +
            ", reserved34Sd=" + reserved34Sd +
            ", transactionCodeQualifier4Pd=" + transactionCodeQualifier4Pd +
            ", transactionComponentSequenceNumber4Pd=" + transactionComponentSequenceNumber4Pd +
            ", businessFormatCode4Pd=" + businessFormatCode4Pd +
            ", reserved14Pd=" + reserved14Pd +
            ", promotionType4Pd=" + promotionType4Pd +
            ", promotionCode4Pd=" + promotionCode4Pd +
            ", reserved24Pd=" + reserved24Pd +
            ", transactionCodeQualifier4Df=" + transactionCodeQualifier4Df +
            ", transactionComponentSequenceNumber4Df=" + transactionComponentSequenceNumber4Df +
            ", businessFormatCode4Df=" + businessFormatCode4Df +
            ", agentUniqueId4Df=" + agentUniqueId4Df +
            ", reserved14Df=" + reserved14Df +
            ", networkIdentificationCode4Df=" + networkIdentificationCode4Df +
            ", contactForInformation4Df=" + contactForInformation4Df +
            ", adjustmentProcessingIndicator4Df=" + adjustmentProcessingIndicator4Df +
            ", messageReasonCode4Df=" + messageReasonCode4Df +
            ", disputeCondition4Df=" + disputeCondition4Df +
            ", vrolFinancialId4Df=" + vrolFinancialId4Df +
            ", vrolCaseNumber4Df=" + vrolCaseNumber4Df +
            ", vrolBundleCaseNumber4Df=" + vrolBundleCaseNumber4Df +
            ", clientCaseNumber4Df=" + clientCaseNumber4Df +
            ", disputeStatus4Df=" + disputeStatus4Df +
            ", surchargeAmount4Df=" + surchargeAmount4Df +
            ", surchargeCreditDebitIndicator4Df=" + surchargeCreditDebitIndicator4Df +
            ", reserved24Df=" + reserved24Df +
            ", transactionCodeQualifier4Sp=" + transactionCodeQualifier4Sp +
            ", transactionComponentSequenceNumber4Sp=" + transactionComponentSequenceNumber4Sp +
            ", businessFormatCode4Sp=" + businessFormatCode4Sp +
            ", agentUniqueId4Sp=" + agentUniqueId4Sp +
            ", reserved14Sp=" + reserved14Sp +
            ", networkIdentificationCode4Sp=" + networkIdentificationCode4Sp +
            ", contactForInformation4Sp=" + contactForInformation4Sp +
            ", adjustmentProcessingIndicator4Sp=" + adjustmentProcessingIndicator4Sp +
            ", messageReasonCode4Sp=" + messageReasonCode4Sp +
            ", surchargeAmount4Sp=" + surchargeAmount4Sp +
            ", surchargeCreditDebitIndicator4Sp=" + surchargeCreditDebitIndicator4Sp +
            ", visaInternalUseOnly4Sp=" + visaInternalUseOnly4Sp +
            ", promotionType4Sp=" + promotionType4Sp +
            ", promotionCode4Sp=" + promotionCode4Sp +
            ", surchargeAmountInCardholderBillingCurrency4Sp=" + surchargeAmountInCardholderBillingCurrency4Sp +
            ", paymentAccountReference4Sp=" + paymentAccountReference4Sp +
            ", tokenRequestorId4Sp=" + tokenRequestorId4Sp +
            ", additionalTransactionFee1Amount4Sp=" + additionalTransactionFee1Amount4Sp +
            ", totalDiscountAmount4Sp=" + totalDiscountAmount4Sp +
            ", reserved24Sp=" + reserved24Sp +
            ", transactionCodeQualifier5=" + transactionCodeQualifier5 +
            ", transactionComponentSequenceNumber5=" + transactionComponentSequenceNumber5 +
            ", transactionIdentifier5=" + transactionIdentifier5 +
            ", authorizedAmount5=" + authorizedAmount5 +
            ", authorizationCurrencyCode5=" + authorizationCurrencyCode5 +
            ", authorizationResponseCode5=" + authorizationResponseCode5 +
            ", validationCode5=" + validationCode5 +
            ", excludedTransactionIdentifierReason5=" + excludedTransactionIdentifierReason5 +
            ", reserved15=" + reserved15 +
            ", reserved25=" + reserved25 +
            ", multipleClearingSequenceNumber5=" + multipleClearingSequenceNumber5 +
            ", multipleClearingSequenceCount5=" + multipleClearingSequenceCount5 +
            ", marketSpecificAuthorizationDataIndicator5=" + marketSpecificAuthorizationDataIndicator5 +
            ", totalAuthorizedAmount5=" + totalAuthorizedAmount5 +
            ", informationIndicator5=" + informationIndicator5 +
            ", merchantTelephoneNumber5=" + merchantTelephoneNumber5 +
            ", additionalDataIndicator5=" + additionalDataIndicator5 +
            ", merchantVolumeIndicator5=" + merchantVolumeIndicator5 +
            ", electronicCommerceGoodsIndicator5=" + electronicCommerceGoodsIndicator5 +
            ", merchantVerificationValue5=" + merchantVerificationValue5 +
            ", interchangeFeeAmount5=" + interchangeFeeAmount5 +
            ", interchangeFeeSign5=" + interchangeFeeSign5 +
            ", sourceCurrencyToBaseCurrencyExchangeRate5=" + sourceCurrencyToBaseCurrencyExchangeRate5 +
            ", baseCurrencyToDestinationCurrencyExchangeRate5=" + baseCurrencyToDestinationCurrencyExchangeRate5 +
            ", optionalIssuerIsaAmount5=" + optionalIssuerIsaAmount5 +
            ", productId5=" + productId5 +
            ", programId5=" + programId5 +
            ", dynamicCurrencyConversionIndicator5=" + dynamicCurrencyConversionIndicator5 +
            ", accountTypeIdentification5=" + accountTypeIdentification5 +
            ", spendQualifiedIndicator5=" + spendQualifiedIndicator5 +
            ", panToken5=" + panToken5 +
            ", reserved35=" + reserved35 +
            ", accountFundingSource5=" + accountFundingSource5 +
            ", cvv2ResultCode5=" + cvv2ResultCode5 +
            ", transactionCodeQualifier6=" + transactionCodeQualifier6 +
            ", transactionComponentSequenceNumber6=" + transactionComponentSequenceNumber6 +
            ", localTax6=" + localTax6 +
            ", localTaxIncluded6=" + localTaxIncluded6 +
            ", nationalTax6=" + nationalTax6 +
            ", nationalTaxIncluded6=" + nationalTaxIncluded6 +
            ", merchantVatRegistrationBusinessReferenceNumber6=" + merchantVatRegistrationBusinessReferenceNumber6 +
            ", customerVatRegisrationNumber6=" + customerVatRegisrationNumber6 +
            ", visaMerchantIdentifier6=" + visaMerchantIdentifier6 +
            ", reserved6=" + reserved6 +
            ", summaryCommodityCode6=" + summaryCommodityCode6 +
            ", otherTax6=" + otherTax6 +
            ", messageIdentifier6=" + messageIdentifier6 +
            ", timeOfPurchase6=" + timeOfPurchase6 +
            ", customerCodeReferenceIdentifierCri6=" + customerCodeReferenceIdentifierCri6 +
            ", nonFuelProductCode16=" + nonFuelProductCode16 +
            ", nonFuelProductCode26=" + nonFuelProductCode26 +
            ", nonFuelProductCode36=" + nonFuelProductCode36 +
            ", nonFuelProductCode46=" + nonFuelProductCode46 +
            ", nonFuelProductCode56=" + nonFuelProductCode56 +
            ", nonFuelProductCode66=" + nonFuelProductCode66 +
            ", nonFuelProductCode76=" + nonFuelProductCode76 +
            ", nonFuelProductCode86=" + nonFuelProductCode86 +
            ", merchantPostalCode6=" + merchantPostalCode6 +
            ", reserved26=" + reserved26 +
            ", transactionCodeQualifier7=" + transactionCodeQualifier7 +
            ", transactionComponentSequenceNumber7=" + transactionComponentSequenceNumber7 +
            ", transactionType7=" + transactionType7 +
            ", cardSequenceNumber7=" + cardSequenceNumber7 +
            ", terminalTransactionDate7=" + terminalTransactionDate7 +
            ", terminalCapabilityProfile7=" + terminalCapabilityProfile7 +
            ", terminalCountryCode7=" + terminalCountryCode7 +
            ", terminalSerialNumber7=" + terminalSerialNumber7 +
            ", unpredictableNumber7=" + unpredictableNumber7 +
            ", applicationTransactionCounter7=" + applicationTransactionCounter7 +
            ", applicationInterchangeProfile7=" + applicationInterchangeProfile7 +
            ", cryptogram7=" + cryptogram7 +
            ", issuerApplicationDataByte27=" + issuerApplicationDataByte27 +
            ", issuerApplicationDataByte37=" + issuerApplicationDataByte37 +
            ", terminalVerificationResults7=" + terminalVerificationResults7 +
            ", issuerApplicationDataByte47=" + issuerApplicationDataByte47 +
            ", cryptogramAmount7=" + cryptogramAmount7 +
            ", issuerApplicationDataByte87=" + issuerApplicationDataByte87 +
            ", issuerApplicationDataByte9167=" + issuerApplicationDataByte9167 +
            ", issuerApplicationDataByte17=" + issuerApplicationDataByte17 +
            ", issuerApplicationDataByte177=" + issuerApplicationDataByte177 +
            ", issuerApplicationDataByte18327=" + issuerApplicationDataByte18327 +
            ", formFactorIndicator7=" + formFactorIndicator7 +
            ", issuerScript1Results7=" + issuerScript1Results7 +
            ", transactionCodeQualifierDFs=" + transactionCodeQualifierDFs +
            ", transactionComponentSequenceNumberDFs=" + transactionComponentSequenceNumberDFs +
            ", businessFormatCodeDFs=" + businessFormatCodeDFs +
            ", chargingPowerOutputCapacityDFs=" + chargingPowerOutputCapacityDFs +
            ", chargingReasonCodeDFs=" + chargingReasonCodeDFs +
            ", totalTimePluggedInDFs=" + totalTimePluggedInDFs +
            ", totalChargingTimeDFs=" + totalChargingTimeDFs +
            ", startTimeOfChargeDFs=" + startTimeOfChargeDFs +
            ", finishTimeOfChargeDFs=" + finishTimeOfChargeDFs +
            ", estimatedKmMilesAddedDFs=" + estimatedKmMilesAddedDFs +
            ", carbonFootprintDFs=" + carbonFootprintDFs +
            ", estimatedVehicleKmMilesAvailableDFs=" + estimatedVehicleKmMilesAvailableDFs +
            ", maximumPowerDispensedDFs=" + maximumPowerDispensedDFs +
            ", connectorTypeDFs=" + connectorTypeDFs +
            ", discountMethodDFs=" + discountMethodDFs +
            ", discountAgentDFs=" + discountAgentDFs +
            ", discountPlanIdDFs=" + discountPlanIdDFs +
            ", clientIdDFs=" + clientIdDFs +
            ", reservedDFs=" + reservedDFs +
            ", transactionCodeQualifierDIp=" + transactionCodeQualifierDIp +
            ", transactionComponentSequenceNumberDIp=" + transactionComponentSequenceNumberDIp +
            ", businessFormatCodeDIp=" + businessFormatCodeDIp +
            ", instalmentPaymentTotalAmountDIp=" + instalmentPaymentTotalAmountDIp +
            ", instalmentPaymentCurrencyCodeDIp=" + instalmentPaymentCurrencyCodeDIp +
            ", numberOfInstallmentsDIp=" + numberOfInstallmentsDIp +
            ", amountOfEachInstalmentDIp=" + amountOfEachInstalmentDIp +
            ", instalmentPaymentNumberDIp=" + instalmentPaymentNumberDIp +
            ", frequencyOfInstallmentsDIp=" + frequencyOfInstallmentsDIp +
            ", planOwnerDIp=" + planOwnerDIp +
            ", planRegistrationSystemIdentifierDIp=" + planRegistrationSystemIdentifierDIp +
            ", reservedDIp=" + reservedDIp +
            ", transactionCodeQualifierDRp=" + transactionCodeQualifierDRp +
            ", transactionComponentSequenceNumberDRp=" + transactionComponentSequenceNumberDRp +
            ", businessFormatCodeDRp=" + businessFormatCodeDRp +
            ", recurringPaymentTypeDRp=" + recurringPaymentTypeDRp +
            ", paymentAmountIndicatorPerTransactionDRp=" + paymentAmountIndicatorPerTransactionDRp +
            ", numberOfRecurringPaymentDRp=" + numberOfRecurringPaymentDRp +
            ", frequencyOfRecurringPaymentDRp=" + frequencyOfRecurringPaymentDRp +
            ", registrationReferenceNumberDRp=" + registrationReferenceNumberDRp +
            ", maximumRecurringPaymentAmountDRp=" + maximumRecurringPaymentAmountDRp +
            ", validationIndicatorDRp=" + validationIndicatorDRp +
            ", reservedDRp=" + reservedDRp +
            ", transactionCodeQualifierDOc=" + transactionCodeQualifierDOc +
            ", transactionComponentSequenceNumberDOc=" + transactionComponentSequenceNumberDOc +
            ", businessFormatCodeDOc=" + businessFormatCodeDOc +
            ", recipientNameDOc=" + recipientNameDOc +
            ", purposeOfPaymentDOc=" + purposeOfPaymentDOc +
            ", preCurrencyConversionAmountDOc=" + preCurrencyConversionAmountDOc +
            ", preCurrencyConversionCurrencyCodeDOc=" + preCurrencyConversionCurrencyCodeDOc +
            ", acceptorLegalBusinessNameDOc=" + acceptorLegalBusinessNameDOc +
            ", paymentFacilitatorNameDOc=" + paymentFacilitatorNameDOc +
            ", customerReferenceCodeDOc=" + customerReferenceCodeDOc +
            ", identificationTypeCodeDOc=" + identificationTypeCodeDOc +
            ", identificationSubtypeDOc=" + identificationSubtypeDOc +
            ", identificationValueDOc=" + identificationValueDOc +
            ", identificationIssuingCountryCodeDOc=" + identificationIssuingCountryCodeDOc +
            ", reservedDOc=" + reservedDOc +
            ", transactionCodeQualifierE=" + transactionCodeQualifierE +
            ", transactionComponentSequenceNumberE=" + transactionComponentSequenceNumberE +
            ", businessFormatCodeE=" + businessFormatCodeE +
            ", vfcPaymentCredentialE=" + vfcPaymentCredentialE +
            ", accountRuleIdentifierE=" + accountRuleIdentifierE +
            ", reservedE=" + reservedE +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
        "}";
    }
}

package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 短信/邮件发送记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@TableName("vcc_message_send_record")
public class MessageSendRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 发送记录主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 请求来源，VCC,U+
     */
    @TableField("source_from")
    private String sourceFrom;

    /**
     * 机构号
     */
    @TableField("organization_no")
    private String organizationNo;

    /**
     * 发送类型；SMS;Email
     */
    @TableField("message_type")
    private String messageType;

    /**
     * 接收方
     */
    @TableField("recipient")
    private String recipient;

    /**
     * 模板编号
     */
    @TableField("template_no")
    private String templateNo;

    /**
     * 请求参数
     */
    @TableField("req_param")
    private String reqParam;

    /**
     * 发送状态；SUCCESS,FAILED
     */
    @TableField("send_status")
    private String sendStatus;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 发送状态
     */
    @TableField("send_time")
    private Date sendTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getSourceFrom() {
        return sourceFrom;
    }

    public void setSourceFrom(String sourceFrom) {
        this.sourceFrom = sourceFrom;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }
    public String getRecipient() {
        return recipient;
    }

    public void setRecipient(String recipient) {
        this.recipient = recipient;
    }
    public String getTemplateNo() {
        return templateNo;
    }

    public void setTemplateNo(String templateNo) {
        this.templateNo = templateNo;
    }
    public String getReqParam() {
        return reqParam;
    }

    public void setReqParam(String reqParam) {
        this.reqParam = reqParam;
    }
    public String getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(String sendStatus) {
        this.sendStatus = sendStatus;
    }
    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "MessageSendRecord{" +
            "id=" + id +
            ", sourceFrom=" + sourceFrom +
            ", organizationNo=" + organizationNo +
            ", messageType=" + messageType +
            ", recipient=" + recipient +
            ", templateNo=" + templateNo +
            ", reqParam=" + reqParam +
            ", sendStatus=" + sendStatus +
            ", errorMessage=" + errorMessage +
            ", sendTime=" + sendTime +
            ", updateTime=" + updateTime +
        "}";
    }
}

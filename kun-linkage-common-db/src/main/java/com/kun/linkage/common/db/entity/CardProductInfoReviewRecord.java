package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 卡产品信息审核记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@TableName("kl_card_product_info_review_record")
public class CardProductInfoReviewRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 审核id
     */
    @TableId(value = "review_id", type = IdType.ASSIGN_ID)
    private Long reviewId;

    /**
     * 操作类型:Add,Modify
     */
    private String operatorType;

    /**
     * 卡产品信息表对应id,修改时有值
     */
    private Long cardProductInfoId;

    /**
     * 卡产品编码
     */
    private String cardProductCode;

    /**
     * 卡片处理方
     */
    private String processor;

    /**
     * 卡产品名称
     */
    private String cardProductName;

    /**
     * 卡组
     */
    private String cardScheme;

    /**
     * 卡片币种
     */
    private String cardCurrencyCode;

    /**
     * BIN起始
     */
    private String binRangeLow;

    /**
     * BIN结束
     */
    private String binRangeHigh;

    /**
     * 特殊用法
     */
    private String specialUsage;

    /**
     * 状态
     */
    private String status;

    /**
     * 审核状态
     */
    private String reviewStatus;

    /**
     * 审核备注
     */
    private String reviewReason;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 提交人id
     */
    private String submitUserId;

    /**
     * 提交人名称
     */
    private String submitUserName;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 审核人id
     */
    private String reviewUserId;

    /**
     * 审核人名称
     */
    private String reviewUserName;

    public Long getReviewId() {
        return reviewId;
    }

    public void setReviewId(Long reviewId) {
        this.reviewId = reviewId;
    }
    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }
    public Long getCardProductInfoId() {
        return cardProductInfoId;
    }

    public void setCardProductInfoId(Long cardProductInfoId) {
        this.cardProductInfoId = cardProductInfoId;
    }
    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }
    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }

    public String getCardProductName() {
        return cardProductName;
    }

    public void setCardProductName(String cardProductName) {
        this.cardProductName = cardProductName;
    }

    public String getCardScheme() {
        return cardScheme;
    }

    public void setCardScheme(String cardScheme) {
        this.cardScheme = cardScheme;
    }
    public String getCardCurrencyCode() {
        return cardCurrencyCode;
    }

    public void setCardCurrencyCode(String cardCurrencyCode) {
        this.cardCurrencyCode = cardCurrencyCode;
    }
    public String getBinRangeLow() {
        return binRangeLow;
    }

    public void setBinRangeLow(String binRangeLow) {
        this.binRangeLow = binRangeLow;
    }
    public String getBinRangeHigh() {
        return binRangeHigh;
    }

    public void setBinRangeHigh(String binRangeHigh) {
        this.binRangeHigh = binRangeHigh;
    }
    public String getSpecialUsage() {
        return specialUsage;
    }

    public void setSpecialUsage(String specialUsage) {
        this.specialUsage = specialUsage;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }
    public String getReviewReason() {
        return reviewReason;
    }

    public void setReviewReason(String reviewReason) {
        this.reviewReason = reviewReason;
    }
    public LocalDateTime getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(LocalDateTime submitTime) {
        this.submitTime = submitTime;
    }
    public String getSubmitUserId() {
        return submitUserId;
    }

    public void setSubmitUserId(String submitUserId) {
        this.submitUserId = submitUserId;
    }
    public String getSubmitUserName() {
        return submitUserName;
    }

    public void setSubmitUserName(String submitUserName) {
        this.submitUserName = submitUserName;
    }
    public LocalDateTime getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(LocalDateTime reviewTime) {
        this.reviewTime = reviewTime;
    }
    public String getReviewUserId() {
        return reviewUserId;
    }

    public void setReviewUserId(String reviewUserId) {
        this.reviewUserId = reviewUserId;
    }
    public String getReviewUserName() {
        return reviewUserName;
    }

    public void setReviewUserName(String reviewUserName) {
        this.reviewUserName = reviewUserName;
    }

    @Override
    public String toString() {
        return "CardProductInfoReviewRecord{" +
            "reviewId=" + reviewId +
            ", operatorType=" + operatorType +
            ", cardProductInfoId=" + cardProductInfoId +
            ", cardProductCode=" + cardProductCode +
            ", processor=" + processor +
            ", cardProductName=" + cardProductName +
            ", cardScheme=" + cardScheme +
            ", cardCurrencyCode=" + cardCurrencyCode +
            ", binRangeLow=" + binRangeLow +
            ", binRangeHigh=" + binRangeHigh +
            ", specialUsage=" + specialUsage +
            ", status=" + status +
            ", reviewStatus=" + reviewStatus +
            ", reviewReason=" + reviewReason +
            ", submitTime=" + submitTime +
            ", submitUserId=" + submitUserId +
            ", submitUserName=" + submitUserName +
            ", reviewTime=" + reviewTime +
            ", reviewUserId=" + reviewUserId +
            ", reviewUserName=" + reviewUserName +
        "}";
    }
}

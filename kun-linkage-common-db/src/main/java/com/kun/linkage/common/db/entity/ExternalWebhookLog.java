package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * KL外部Webhook日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@TableName("kl_external_webhook_log")
public class ExternalWebhookLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("id")
    private String id;

    /**
     * 请求ID
     */
    @TableField("request_id")
    private String requestId;

    /**
     * Webhook URL
     */
    @TableField("webhook_url")
    private String webhookUrl;

    /**
     * 追踪ID
     */
    @TableField("trace_id")
    private String traceId;

    /**
     * Span ID
     */
    @TableField("span_id")
    private String spanId;

    /**
     * HTTP方法
     */
    @TableField("method")
    private String method;

    /**
     * 请求内容
     */
    @TableField("request")
    private String request;

    /**
     * 响应内容
     */
    @TableField("response")
    private String response;

    /**
     * HTTP状态码
     */
    @TableField("http_status")
    private Integer httpStatus;

    /**
     * 错误信息
     */
    @TableField("error_msg")
    private String errorMsg;

    /**
     * 请求耗时（毫秒）
     */
    @TableField("cost")
    private BigDecimal cost;

    /**
     * 状态，S-成功，F-失败, T-超时, UE-未知错误
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 更新时间
     */
    @TableField("update_date")
    private Date updateDate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    public String getWebhookUrl() {
        return webhookUrl;
    }

    public void setWebhookUrl(String webhookUrl) {
        this.webhookUrl = webhookUrl;
    }
    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }
    public String getSpanId() {
        return spanId;
    }

    public void setSpanId(String spanId) {
        this.spanId = spanId;
    }
    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }
    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }
    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }
    public Integer getHttpStatus() {
        return httpStatus;
    }

    public void setHttpStatus(Integer httpStatus) {
        this.httpStatus = httpStatus;
    }
    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "ExternalWebhookLog{" +
            "id=" + id +
            ", requestId=" + requestId +
            ", webhookUrl=" + webhookUrl +
            ", traceId=" + traceId +
            ", spanId=" + spanId +
            ", method=" + method +
            ", request=" + request +
            ", response=" + response +
            ", httpStatus=" + httpStatus +
            ", errorMsg=" + errorMsg +
            ", cost=" + cost +
            ", status=" + status +
            ", createDate=" + createDate +
            ", updateDate=" + updateDate +
        "}";
    }
}

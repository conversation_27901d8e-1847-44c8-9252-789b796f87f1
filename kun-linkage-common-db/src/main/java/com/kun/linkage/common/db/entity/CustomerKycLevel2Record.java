package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * kyc二级认证信息记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@TableName("kl_customer_kyc_level2_record")
public class CustomerKycLevel2Record implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键id
     */
    @TableId(value = "kyc_level2_id",type = IdType.ASSIGN_ID)
    private Long kycLevel2Id;

    /**
     * 用户id
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 机构号
     */
    @TableField("organization_no")
    private String organizationNo;

    /**
     * 案件号
     */
    @TableField("case_no")
    private String caseNo;

    /**
     * 地址证明图片上传ids
     */
    @TableField("address_proof_upload_ids")
    private String addressProofUploadIds;

    /**
     * 地址证明图片1
     */
    @TableField("address_proof_photo_1")
    private String addressProofPhoto1;

    /**
     * 地址证明图片2
     */
    @TableField("address_proof_photo_2")
    private String addressProofPhoto2;

    /**
     * 地址证明图片3
     */
    @TableField("address_proof_photo_3")
    private String addressProofPhoto3;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Long getKycLevel2Id() {
        return kycLevel2Id;
    }

    public void setKycLevel2Id(Long kycLevel2Id) {
        this.kycLevel2Id = kycLevel2Id;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }

    public String getAddressProofUploadIds() {
        return addressProofUploadIds;
    }

    public void setAddressProofUploadIds(String addressProofUploadIds) {
        this.addressProofUploadIds = addressProofUploadIds;
    }
    public String getAddressProofPhoto1() {
        return addressProofPhoto1;
    }

    public void setAddressProofPhoto1(String addressProofPhoto1) {
        this.addressProofPhoto1 = addressProofPhoto1;
    }
    public String getAddressProofPhoto2() {
        return addressProofPhoto2;
    }

    public void setAddressProofPhoto2(String addressProofPhoto2) {
        this.addressProofPhoto2 = addressProofPhoto2;
    }
    public String getAddressProofPhoto3() {
        return addressProofPhoto3;
    }

    public void setAddressProofPhoto3(String addressProofPhoto3) {
        this.addressProofPhoto3 = addressProofPhoto3;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CustomerKycLevel2Record{" +
            "kycLevel2Id=" + kycLevel2Id +
            ", customerId=" + customerId +
            ", organizationNo=" + organizationNo +
            ", caseNo=" + caseNo +
            ", addressProofUploadIds=" + addressProofUploadIds +
            ", addressProofPhoto1=" + addressProofPhoto1 +
            ", addressProofPhoto2=" + addressProofPhoto2 +
            ", addressProofPhoto3=" + addressProofPhoto3 +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
        "}";
    }
}

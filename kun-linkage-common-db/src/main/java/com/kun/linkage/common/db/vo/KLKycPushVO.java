package com.kun.linkage.common.db.vo;


import lombok.Data;

/**
 * title: <br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description: 描述 <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * @since 2025/5/30 17:39
 */
@Data
public class KLKycPushVO {

    private String customerNo; // 商户号

    private String CustomerName; // 商户名称

    private String kunCustomerNo; // Kun

    private String acsCustomerNo; // ACS

    private String registerRegion; // HK or 其他

    private String kycInfo; // KYC相关信息，包括产品列表、董事信息和企业信息

}

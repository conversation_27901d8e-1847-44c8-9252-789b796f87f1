package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 机构用户钱包操作记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@TableName("kl_organization_customer_wallet_operation_record")
public class OrganizationCustomerWalletOperationRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 机构号
     */
    private String organizationNo;

    /**
     * 客户号
     */
    private String customerId;

    /**
     * 请求流水号
     */
    private String requestNo;

    /**
     * 钱包通道
     */
    private String walletNetwork;

    /**
     * 链网络
     */
    private String chainNetwork;

    /**
     * 钱包地址
     */
    private String walletAddress;

    /**
     * 操作状态;SUCCESS:成功;FAIL:失败(该状态代表系统处理状态)
     */
    private String operationStatus;

    /**
     * 失败信息
     */
    private String failMessage;

    /**
     * 原请求参数(json)
     */
    private String requestParams;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后一次修改时间
     */
    private LocalDateTime lastModifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }
    public String getWalletNetwork() {
        return walletNetwork;
    }

    public void setWalletNetwork(String walletNetwork) {
        this.walletNetwork = walletNetwork;
    }
    public String getChainNetwork() {
        return chainNetwork;
    }

    public void setChainNetwork(String chainNetwork) {
        this.chainNetwork = chainNetwork;
    }
    public String getWalletAddress() {
        return walletAddress;
    }

    public void setWalletAddress(String walletAddress) {
        this.walletAddress = walletAddress;
    }
    public String getOperationStatus() {
        return operationStatus;
    }

    public void setOperationStatus(String operationStatus) {
        this.operationStatus = operationStatus;
    }
    public String getFailMessage() {
        return failMessage;
    }

    public void setFailMessage(String failMessage) {
        this.failMessage = failMessage;
    }
    public String getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(String requestParams) {
        this.requestParams = requestParams;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    @Override
    public String toString() {
        return "OrganizationCustomerWalletOperationRecord0{" +
            "id=" + id +
            ", operationType=" + operationType +
            ", organizationNo=" + organizationNo +
            ", customerId=" + customerId +
            ", requestNo=" + requestNo +
            ", walletNetwork=" + walletNetwork +
            ", chainNetwork=" + chainNetwork +
            ", walletAddress=" + walletAddress +
            ", operationStatus=" + operationStatus +
            ", failMessage=" + failMessage +
            ", requestParams=" + requestParams +
            ", createTime=" + createTime +
            ", lastModifyTime=" + lastModifyTime +
        "}";
    }
}

package com.kun.linkage.common.db.sharding.algorithm;

import org.apache.shardingsphere.sharding.api.sharding.standard.PreciseShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.RangeShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.StandardShardingAlgorithm;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.Properties;


public class StringDynamicYearMonthShardingAlgorithm implements StandardShardingAlgorithm<String> {
    private static DateTimeFormatter TABLE_SUFFIX_FORMATTER;
    private static DateTimeFormatter DATE_FORMATTER;
    private Properties props = new Properties();

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<String> shardingValue) {
        LocalDate localDate = LocalDate.parse(shardingValue.getValue(), DATE_FORMATTER);
        String targetTable = shardingValue.getLogicTableName() + "_" + localDate.format(TABLE_SUFFIX_FORMATTER);
        if (availableTargetNames.contains(targetTable)) {
            return targetTable;
        }
        // 表不存在
        throw new RuntimeException("目标表 " + targetTable + " 不存在");
    }

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, RangeShardingValue<String> shardingValue) {
        Collection<String> result = new LinkedHashSet<>();
        YearMonth start = YearMonth.parse(shardingValue.getValueRange().lowerEndpoint(), DATE_FORMATTER);
        YearMonth end = YearMonth.parse(shardingValue.getValueRange().upperEndpoint(), DATE_FORMATTER);
        YearMonth current = start;
        while (current.isBefore(end) || current.equals(end)) {
            String suffix = current.format(TABLE_SUFFIX_FORMATTER);
            String targetTable = shardingValue.getLogicTableName() + "_" + suffix;
            if (availableTargetNames.contains(targetTable)) {
                result.add(targetTable);
            }
            current = current.plusMonths(1);
        }
        if (result.isEmpty()) {
            throw new RuntimeException("未找到目标表");
        }
        return result;
    }

    @Override
    public Properties getProps() {
        return props;
    }

    @Override
    public void init(Properties props) {
        this.props = props;
        String tableSuffixPattern = props.getProperty("sharding-suffix-pattern", "yyyyMM");
        TABLE_SUFFIX_FORMATTER = DateTimeFormatter.ofPattern(tableSuffixPattern);
        String dateFormat = props.getProperty("date-format", "yyyyMMdd");
        DATE_FORMATTER = DateTimeFormatter.ofPattern(dateFormat);
    }

    @Override
    public String getType() {
        // 自定义算法类型名称
        return "STRING_DYNAMIC_YEAR_MONTH";
    }
}

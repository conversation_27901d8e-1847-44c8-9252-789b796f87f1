package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 报表记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@TableName("common_report_record")
public class ReportRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId("id")
    private String id;

    /**
     * 报表数据日期(月报表为YYYYMM,日报表为YYYYMMDD)
     */
    @TableField("report_data_date")
    private String reportDataDate;

    /**
     * 报表类型
     */
    @TableField("report_type")
    private String reportType;

    /**
     * 报表文件名称
     */
    @TableField("report_file_name")
    private String reportFileName;

    /**
     * 报表文件全路径
     */
    @TableField("report_file_full_path")
    private String reportFileFullPath;

    /**
     * 报表文件生成状态
     */
    @TableField("report_generate_status")
    private String reportGenerateStatus;

    /**
     * 报表文件生成日期(YYYYMMDD)
     */
    @TableField("report_generate_date")
    private String reportGenerateDate;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 最后一次修改时间
     */
    @TableField("last_modify_time")
    private LocalDateTime lastModifyTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getReportDataDate() {
        return reportDataDate;
    }

    public void setReportDataDate(String reportDataDate) {
        this.reportDataDate = reportDataDate;
    }
    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }
    public String getReportFileName() {
        return reportFileName;
    }

    public void setReportFileName(String reportFileName) {
        this.reportFileName = reportFileName;
    }
    public String getReportFileFullPath() {
        return reportFileFullPath;
    }

    public void setReportFileFullPath(String reportFileFullPath) {
        this.reportFileFullPath = reportFileFullPath;
    }
    public String getReportGenerateStatus() {
        return reportGenerateStatus;
    }

    public void setReportGenerateStatus(String reportGenerateStatus) {
        this.reportGenerateStatus = reportGenerateStatus;
    }
    public String getReportGenerateDate() {
        return reportGenerateDate;
    }

    public void setReportGenerateDate(String reportGenerateDate) {
        this.reportGenerateDate = reportGenerateDate;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    @Override
    public String toString() {
        return "ReportRecord{" +
            "id=" + id +
            ", reportDataDate=" + reportDataDate +
            ", reportType=" + reportType +
            ", reportFileName=" + reportFileName +
            ", reportFileFullPath=" + reportFileFullPath +
            ", reportGenerateStatus=" + reportGenerateStatus +
            ", reportGenerateDate=" + reportGenerateDate +
            ", createTime=" + createTime +
            ", lastModifyTime=" + lastModifyTime +
        "}";
    }
}

package com.kun.linkage.common.db.sharding.algorithm;

import org.apache.shardingsphere.sharding.api.sharding.standard.PreciseShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.RangeShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.StandardShardingAlgorithm;

import java.time.LocalDate;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.Properties;

public class QuarterShardingAlgorithm implements StandardShardingAlgorithm<LocalDate> {

    private Properties props = new Properties();

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<LocalDate> shardingValue) {
        // 获取清算日期
        LocalDate localDate = shardingValue.getValue();
        if (localDate == null) {
            throw new RuntimeException("日期不能为null");
        }

        // 提取年份和月份
        int year = localDate.getYear();
        int month = localDate.getMonthValue();

        // 根据月份获取季度
        String quarter = getQuarterByMonth(month);

        // 构造目标表名：逻辑表名_年份_季度（如：kc_clearing_info_2025_q3）
        String targetTable = shardingValue.getLogicTableName() + "_" + year + "_" + quarter;

        if (availableTargetNames.contains(targetTable)) {
            return targetTable;
        }

        // 表不存在时抛出异常
        throw new RuntimeException("目标表 " + targetTable + " 不存在");
    }

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, RangeShardingValue<LocalDate> shardingValue) {
        Collection<String> result = new LinkedHashSet<>();
        // 获取日期范围
        LocalDate start = shardingValue.getValueRange().lowerEndpoint();
        LocalDate end = shardingValue.getValueRange().upperEndpoint();

        if (start == null || end == null) {
            throw new RuntimeException("无效的日期范围，开始或结束日期为null");
        }


        // 提取开始年份和季度
        int startYear = start.getYear();
        int startMonth = start.getMonthValue();
        String startQuarter = getQuarterByMonth(startMonth);

        // 提取结束年份和季度
        int endYear = end.getYear();
        int endMonth = end.getMonthValue();
        String endQuarter = getQuarterByMonth(endMonth);

        // 遍历季度范围
        int currentYear = startYear;
        String currentQuarter = startQuarter;

        while (true) {
            String suffix = currentYear + "_" + currentQuarter;
            String targetTable = shardingValue.getLogicTableName() + "_" + suffix;
            if (availableTargetNames.contains(targetTable)) {
                result.add(targetTable);
            }

            // 检查是否到达结束季度
            if (currentYear == endYear && currentQuarter.equals(endQuarter)) {
                break;
            }

            // 移动到下一个季度
            currentQuarter = getNextQuarter(currentQuarter);
            if (currentQuarter.equals("q1")) {
                currentYear++;
            }
        }

        if (result.isEmpty()) {
            throw new RuntimeException("未找到目标表");
        }

        return result;
    }

    // 根据月份获取季度（返回格式：q1, q2, q3, q4）
    private String getQuarterByMonth(int month) {
        if (month >= 1 && month <= 3) {
            return "q1";
        } else if (month >= 4 && month <= 6) {
            return "q2";
        } else if (month >= 7 && month <= 9) {
            return "q3";
        } else if (month >= 10 && month <= 12) {
            return "q4";
        }
        return "q1"; // 默认返回第一季度
    }

    // 获取下一个季度
    private String getNextQuarter(String currentQuarter) {
        switch (currentQuarter) {
            case "q1":
                return "q2";
            case "q2":
                return "q3";
            case "q3":
                return "q4";
            case "q4":
                return "q1";
            default:
                throw new RuntimeException("无效的季度: " + currentQuarter);
        }
    }

    @Override
    public Properties getProps() {
        return props;
    }

    @Override
    public void init(Properties props) {
        this.props = props;
    }

    @Override
    public String getType() {
        return "LOCALDATE_QUARTERLY_PARTITION";
    }
}


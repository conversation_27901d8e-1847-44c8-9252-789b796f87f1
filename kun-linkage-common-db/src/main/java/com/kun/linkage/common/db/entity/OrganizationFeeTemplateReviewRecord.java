package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 机构费率模版审核记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@TableName("kl_organization_fee_template_review_record")
public class OrganizationFeeTemplateReviewRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 审核id
     */
    @TableId(value = "review_id", type = IdType.ASSIGN_ID)
    private String reviewId;

    /**
     * 操作类型:ADD,MODIFY
     */
    @TableField("operator_type")
    private String operatorType;

    /**
     * 模版号,修改时有值
     */
    @TableField("template_no")
    private String templateNo;

    /**
     * 模版名称
     */
    @TableField("template_name")
    private String templateName;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 审核状态
     */
    @TableField("review_status")
    private String reviewStatus;

    /**
     * 审核备注
     */
    @TableField("review_reason")
    private String reviewReason;

    /**
     * 提交时间
     */
    @TableField("submit_time")
    private LocalDateTime submitTime;

    /**
     * 提交人id
     */
    @TableField("submit_user_id")
    private String submitUserId;

    /**
     * 提交人名称
     */
    @TableField("submit_user_name")
    private String submitUserName;

    /**
     * 审核时间
     */
    @TableField("review_time")
    private LocalDateTime reviewTime;

    /**
     * 审核人id
     */
    @TableField("review_user_id")
    private String reviewUserId;

    /**
     * 审核人名称
     */
    @TableField("review_user_name")
    private String reviewUserName;

    public String getReviewId() {
        return reviewId;
    }

    public void setReviewId(String reviewId) {
        this.reviewId = reviewId;
    }
    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }
    public String getTemplateNo() {
        return templateNo;
    }

    public void setTemplateNo(String templateNo) {
        this.templateNo = templateNo;
    }
    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }
    public String getReviewReason() {
        return reviewReason;
    }

    public void setReviewReason(String reviewReason) {
        this.reviewReason = reviewReason;
    }
    public LocalDateTime getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(LocalDateTime submitTime) {
        this.submitTime = submitTime;
    }
    public String getSubmitUserId() {
        return submitUserId;
    }

    public void setSubmitUserId(String submitUserId) {
        this.submitUserId = submitUserId;
    }
    public String getSubmitUserName() {
        return submitUserName;
    }

    public void setSubmitUserName(String submitUserName) {
        this.submitUserName = submitUserName;
    }
    public LocalDateTime getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(LocalDateTime reviewTime) {
        this.reviewTime = reviewTime;
    }
    public String getReviewUserId() {
        return reviewUserId;
    }

    public void setReviewUserId(String reviewUserId) {
        this.reviewUserId = reviewUserId;
    }
    public String getReviewUserName() {
        return reviewUserName;
    }

    public void setReviewUserName(String reviewUserName) {
        this.reviewUserName = reviewUserName;
    }

    @Override
    public String toString() {
        return "OrganizationFeeTemplateReviewRecord{" +
            "reviewId=" + reviewId +
            ", operatorType=" + operatorType +
            ", templateNo=" + templateNo +
            ", templateName=" + templateName +
            ", status=" + status +
            ", reviewStatus=" + reviewStatus +
            ", reviewReason=" + reviewReason +
            ", submitTime=" + submitTime +
            ", submitUserId=" + submitUserId +
            ", submitUserName=" + submitUserName +
            ", reviewTime=" + reviewTime +
            ", reviewUserId=" + reviewUserId +
            ", reviewUserName=" + reviewUserName +
        "}";
    }
}

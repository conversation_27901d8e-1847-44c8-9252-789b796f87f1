package com.kun.linkage.common.db.vo;

import java.io.Serializable;
import java.util.List;

public class EnterpriseInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 同步ID
     */
    private long syncId;
    /*** ======= 1 Begin =======  ***/
    /**
     * 企业中文名称
     */
    private String enterpriseNameCHS;
    /**
     * 企业英文名称
     */
    private String enterpriseEN;
    /**
     * 注册地区
     */
    private String registerRegion; // 注册地区
    /**
     * 企业类型
     */
    private String enterpriseType; // 企业类型
    /*** ======= 1 End =======  ***/
    /*** ======= 2 Begin =======  ***/
    /**
     * 公司注册证书编号
     */
    private String businessRegistrationNo; // 公司注册证书编号
    /**
     * 成立时间
     */
    private String establishTime; // 成立时间
    /**
     * 企业域名
     */
    private String enterpriseDomain; // 企业域名
    /**
     * 营业地址
     */
    private String mainBusinessAddress; // 营业地址
    /*** ======= 2 End =======  ***/
    /*** ======= 3 Begin =======  ***/
    /**
     * 注册地址
     */
    private String registerAddress; // 注册地址
    /**
     * 行业登记证编号
     */
    private String incorporationCertificateNo; // 行业登记证编号
    /**
     * 所属行业
     */
    private String industry; // 所属行业
    /**
     * 子行业
     */
    private String subIndustry; // 子行业
    /**
     * 行业从业年限
     */
    private String industryTenure; // 行业从业年限
    /*** ======= 3 End =======  ***/
    /*** ======= 4 Begin =======  ***/
    /**
     * 行业详情
     */
    private String industryDetail; // 行业详情
    /**
     * 初始资金来源
     */
    private String initialFundingSource; // 初始资金来源
    /**
     * 财富来源
     */
    private String wealthSource; // 财富来源
    /**
     * 持续资金来源
     */
    private String continuousFundingSource; // 持续资金来源
    /*** ======= 4 End =======  ***/
    /*** ======= 5 Begin =======  ***/
    /**
     * 上一年销售额
     */
    private String salesVolumeLastyear; // 上一年销售额
    /**
     * 开户目的
     */
    private String openAccountPurpose; // 开户目的

    /**
     * 过去五年内是否变更过企业名称
     */
    private String isChangeEnterpriseNameInFiveYears; // 过去五年内是否变更过企业名称
    /**
     * 曾用企业名称
     */
    private String usedEnterpriseName; // 曾用企业名称
    /**
     * 注册国家
     */
    private String registerCountry; // 注册国家
    /**
     * 认证材料列表
     */
    private String authenticMaterials; // 认证材料列表
    // TODO:不明意义的字段
    /**
     * 经营地区列表
     */
    private List<String> businessRegion; // 经营地区列表
    /**
     * 股权结构列表
     */
    private List<String> equityStructure; // 股权结构列表

    /**
     * 是否长期有效，1:长期有效，2 ，非长期有效
     */
    private String longTime; // 是否长期有效，1:长期有效，2 ，非长期有效

    /**
     * 企业联系电话
     */
    private String phone; // 企业联系电话
    /**
     * 员工数量
     */
    private String employeeNum; // 员工数量
    /**
     * 认证类型描述
     */
    private String authTypeDesc; // 认证类型描述
    /**
     * 中间层股东信息
     */
    private String middleTierShareholders; // 中间层股东信息
    /**
     * 中间层股东详细信息
     */
    private String middleTierShareholdersInfo; // 中间层股东详细信息
    /**
     * 实体补充信息
     */
    private String entitySupplementary; // 实体补充信息

    /*** ======= 法人信息 Begin =======  ***/
    /**
     * 法人代表信息
     */
    private String managerGender; // 法人代表性别
    /**
     * 法人代表身份证号
     */
    private String managerIdCard; // 法人代表身份证号
    /**
     * 法人代表英文姓名
     */
    private String managerNameEN; // 法人代表英文姓名
    /**
     * 法人代表国籍
     */
    private String managerCountry; // 法人代表国籍
    /**
     * 法人代表中文姓名
     */
    private String managerNameCHS; // 法人代表中文姓名
    /**
     * 法人代表姓氏
     */
    private String managerSurname; // 法人代表姓氏
    /**
     * 法人代表认证类型
     */
    private String managerAuthType; // 法人代表认证类型
    /**
     * 法人代表认证类型描述
     */
    private String managerAuthTypeDesc; // 法人代表认证类型描述
    /**
     * 法人代表出生日期
     */
    private String managerBirthday; // 法人代表出生日期
    /**
     * 法人代表中文姓氏
     */
    private String managerSurnameCHS; // 法人代表中文姓氏
    /**
     * 法人代表联系邮箱
     */
    private String managerContactsEmail; // 法人代表联系邮箱
    /**
     * 法人代表证书有效期
     */
    private String managerCertificateTerm; // 法人代表证书有效期
    /**
     * 法人代表居住地址
     */
    private String managerResidenceAddress; // 法人代表居住地址
    /**
     * 法人代表居住国家
     */
    private String managerResidenceCountry; // 法人代表居住国家
    /**
     * 法人代表持股比例
     */
    private String managerShareholdingRatio; // 法人代表持股比例
    /**
     * 法人代表证书有效期开始
     */
    private String managerCertificateTermStart; // 法人代表证书有效期开始
    /**
     * 法人代表证书有效期结束
     */
    private String managerCertificateTermEnd; // 法人代表证书有效期结束
    /**
     * 法人身份验证
     */
    private IdentityVerification managerIdentityVerification; // 法人身份验证
    /**
     * 法人身份验证状态
     */
    private String managerIdentityVerificationStatus = "Success"; // 法人身份验证状态
    /*** ======= 法人信息 End =======  ***/
    /*** ======= 文件列表 Begin =======  ***/
    /**
     * 关联规则文档路径列表
     */
    private List<FileInfoVO> associationRules; // 关联规则文档路径列表
    /**
     * 相关文档路径列表
     */
    private List<FileInfoVO> nnc1; // 相关文档路径列表
    /**
     * 公司成立证明文档路径列表
     */
    private List<FileInfoVO> incorporationCertificate; // 公司成立证明文档路径列表
    /**
     * 授权函
     */
    private List<FileInfoVO> authorizationLetter; // 授权函
    /**
     * 商业注册信息列表
     */
    private List<FileInfoVO> businessRegistration; // 商业注册信息列表
    /**
     * 现任职位列表
     */
    private List<FileInfoVO> incumbency; // 现任职位列表

    /*** ======= 文件列表 End =======  ***/

    public long getSyncId() {
        return syncId;
    }

    public void setSyncId(long syncId) {
        this.syncId = syncId;
    }

    public String getEnterpriseNameCHS() {
        return enterpriseNameCHS;
    }

    public void setEnterpriseNameCHS(String enterpriseNameCHS) {
        this.enterpriseNameCHS = enterpriseNameCHS;
    }

    public String getEnterpriseEN() {
        return enterpriseEN;
    }

    public void setEnterpriseEN(String enterpriseEN) {
        this.enterpriseEN = enterpriseEN;
    }

    public String getRegisterRegion() {
        return registerRegion;
    }

    public void setRegisterRegion(String registerRegion) {
        this.registerRegion = registerRegion;
    }

    public String getEnterpriseType() {
        return enterpriseType;
    }

    public void setEnterpriseType(String enterpriseType) {
        this.enterpriseType = enterpriseType;
    }

    public String getBusinessRegistrationNo() {
        return businessRegistrationNo;
    }

    public void setBusinessRegistrationNo(String businessRegistrationNo) {
        this.businessRegistrationNo = businessRegistrationNo;
    }

    public String getEstablishTime() {
        return establishTime;
    }

    public void setEstablishTime(String establishTime) {
        this.establishTime = establishTime;
    }

    public String getEnterpriseDomain() {
        return enterpriseDomain;
    }

    public void setEnterpriseDomain(String enterpriseDomain) {
        this.enterpriseDomain = enterpriseDomain;
    }

    public String getMainBusinessAddress() {
        return mainBusinessAddress;
    }

    public void setMainBusinessAddress(String mainBusinessAddress) {
        this.mainBusinessAddress = mainBusinessAddress;
    }

    public String getRegisterAddress() {
        return registerAddress;
    }

    public void setRegisterAddress(String registerAddress) {
        this.registerAddress = registerAddress;
    }

    public String getIncorporationCertificateNo() {
        return incorporationCertificateNo;
    }

    public void setIncorporationCertificateNo(String incorporationCertificateNo) {
        this.incorporationCertificateNo = incorporationCertificateNo;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getSubIndustry() {
        return subIndustry;
    }

    public void setSubIndustry(String subIndustry) {
        this.subIndustry = subIndustry;
    }

    public String getIndustryTenure() {
        return industryTenure;
    }

    public void setIndustryTenure(String industryTenure) {
        this.industryTenure = industryTenure;
    }

    public String getIndustryDetail() {
        return industryDetail;
    }

    public void setIndustryDetail(String industryDetail) {
        this.industryDetail = industryDetail;
    }

    public String getInitialFundingSource() {
        return initialFundingSource;
    }

    public void setInitialFundingSource(String initialFundingSource) {
        this.initialFundingSource = initialFundingSource;
    }

    public String getWealthSource() {
        return wealthSource;
    }

    public void setWealthSource(String wealthSource) {
        this.wealthSource = wealthSource;
    }

    public String getContinuousFundingSource() {
        return continuousFundingSource;
    }

    public void setContinuousFundingSource(String continuousFundingSource) {
        this.continuousFundingSource = continuousFundingSource;
    }

    public String getSalesVolumeLastyear() {
        return salesVolumeLastyear;
    }

    public void setSalesVolumeLastyear(String salesVolumeLastyear) {
        this.salesVolumeLastyear = salesVolumeLastyear;
    }

    public String getOpenAccountPurpose() {
        return openAccountPurpose;
    }

    public void setOpenAccountPurpose(String openAccountPurpose) {
        this.openAccountPurpose = openAccountPurpose;
    }

    public String getIsChangeEnterpriseNameInFiveYears() {
        return isChangeEnterpriseNameInFiveYears;
    }

    public void setIsChangeEnterpriseNameInFiveYears(String isChangeEnterpriseNameInFiveYears) {
        this.isChangeEnterpriseNameInFiveYears = isChangeEnterpriseNameInFiveYears;
    }

    public String getUsedEnterpriseName() {
        return usedEnterpriseName;
    }

    public void setUsedEnterpriseName(String usedEnterpriseName) {
        this.usedEnterpriseName = usedEnterpriseName;
    }

    public String getRegisterCountry() {
        return registerCountry;
    }

    public void setRegisterCountry(String registerCountry) {
        this.registerCountry = registerCountry;
    }

    public String getAuthenticMaterials() {
        return authenticMaterials;
    }

    public void setAuthenticMaterials(String authenticMaterials) {
        this.authenticMaterials = authenticMaterials;
    }

    public List<String> getBusinessRegion() {
        return businessRegion;
    }

    public void setBusinessRegion(List<String> businessRegion) {
        this.businessRegion = businessRegion;
    }

    public List<String> getEquityStructure() {
        return equityStructure;
    }

    public void setEquityStructure(List<String> equityStructure) {
        this.equityStructure = equityStructure;
    }

    public String getLongTime() {
        return longTime;
    }

    public void setLongTime(String longTime) {
        this.longTime = longTime;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmployeeNum() {
        return employeeNum;
    }

    public void setEmployeeNum(String employeeNum) {
        this.employeeNum = employeeNum;
    }

    public String getAuthTypeDesc() {
        return authTypeDesc;
    }

    public void setAuthTypeDesc(String authTypeDesc) {
        this.authTypeDesc = authTypeDesc;
    }

    public String getMiddleTierShareholders() {
        return middleTierShareholders;
    }

    public void setMiddleTierShareholders(String middleTierShareholders) {
        this.middleTierShareholders = middleTierShareholders;
    }

    public String getMiddleTierShareholdersInfo() {
        return middleTierShareholdersInfo;
    }

    public void setMiddleTierShareholdersInfo(String middleTierShareholdersInfo) {
        this.middleTierShareholdersInfo = middleTierShareholdersInfo;
    }

    public String getEntitySupplementary() {
        return entitySupplementary;
    }

    public void setEntitySupplementary(String entitySupplementary) {
        this.entitySupplementary = entitySupplementary;
    }

    public String getManagerGender() {
        return managerGender;
    }

    public void setManagerGender(String managerGender) {
        this.managerGender = managerGender;
    }

    public String getManagerIdCard() {
        return managerIdCard;
    }

    public void setManagerIdCard(String managerIdCard) {
        this.managerIdCard = managerIdCard;
    }

    public String getManagerNameEN() {
        return managerNameEN;
    }

    public void setManagerNameEN(String managerNameEN) {
        this.managerNameEN = managerNameEN;
    }

    public String getManagerCountry() {
        return managerCountry;
    }

    public void setManagerCountry(String managerCountry) {
        this.managerCountry = managerCountry;
    }

    public String getManagerNameCHS() {
        return managerNameCHS;
    }

    public void setManagerNameCHS(String managerNameCHS) {
        this.managerNameCHS = managerNameCHS;
    }

    public String getManagerSurname() {
        return managerSurname;
    }

    public void setManagerSurname(String managerSurname) {
        this.managerSurname = managerSurname;
    }

    public String getManagerAuthType() {
        return managerAuthType;
    }

    public void setManagerAuthType(String managerAuthType) {
        this.managerAuthType = managerAuthType;
    }

    public String getManagerAuthTypeDesc() {
        return managerAuthTypeDesc;
    }

    public void setManagerAuthTypeDesc(String managerAuthTypeDesc) {
        this.managerAuthTypeDesc = managerAuthTypeDesc;
    }

    public String getManagerBirthday() {
        return managerBirthday;
    }

    public void setManagerBirthday(String managerBirthday) {
        this.managerBirthday = managerBirthday;
    }

    public String getManagerSurnameCHS() {
        return managerSurnameCHS;
    }

    public void setManagerSurnameCHS(String managerSurnameCHS) {
        this.managerSurnameCHS = managerSurnameCHS;
    }

    public String getManagerContactsEmail() {
        return managerContactsEmail;
    }

    public void setManagerContactsEmail(String managerContactsEmail) {
        this.managerContactsEmail = managerContactsEmail;
    }

    public String getManagerCertificateTerm() {
        return managerCertificateTerm;
    }

    public void setManagerCertificateTerm(String managerCertificateTerm) {
        this.managerCertificateTerm = managerCertificateTerm;
    }

    public String getManagerResidenceAddress() {
        return managerResidenceAddress;
    }

    public void setManagerResidenceAddress(String managerResidenceAddress) {
        this.managerResidenceAddress = managerResidenceAddress;
    }

    public String getManagerResidenceCountry() {
        return managerResidenceCountry;
    }

    public void setManagerResidenceCountry(String managerResidenceCountry) {
        this.managerResidenceCountry = managerResidenceCountry;
    }

    public String getManagerShareholdingRatio() {
        return managerShareholdingRatio;
    }

    public void setManagerShareholdingRatio(String managerShareholdingRatio) {
        this.managerShareholdingRatio = managerShareholdingRatio;
    }

    public String getManagerCertificateTermStart() {
        return managerCertificateTermStart;
    }

    public void setManagerCertificateTermStart(String managerCertificateTermStart) {
        this.managerCertificateTermStart = managerCertificateTermStart;
    }

    public String getManagerCertificateTermEnd() {
        return managerCertificateTermEnd;
    }

    public void setManagerCertificateTermEnd(String managerCertificateTermEnd) {
        this.managerCertificateTermEnd = managerCertificateTermEnd;
    }

    public IdentityVerification getManagerIdentityVerification() {
        return managerIdentityVerification;
    }

    public void setManagerIdentityVerification(IdentityVerification managerIdentityVerification) {
        this.managerIdentityVerification = managerIdentityVerification;
    }

    public String getManagerIdentityVerificationStatus() {
        return managerIdentityVerificationStatus;
    }

    public void setManagerIdentityVerificationStatus(String managerIdentityVerificationStatus) {
        this.managerIdentityVerificationStatus = managerIdentityVerificationStatus;
    }

    public List<FileInfoVO> getAssociationRules() {
        return associationRules;
    }

    public void setAssociationRules(List<FileInfoVO> associationRules) {
        this.associationRules = associationRules;
    }

    public List<FileInfoVO> getNnc1() {
        return nnc1;
    }

    public void setNnc1(List<FileInfoVO> nnc1) {
        this.nnc1 = nnc1;
    }

    public List<FileInfoVO> getIncorporationCertificate() {
        return incorporationCertificate;
    }

    public void setIncorporationCertificate(List<FileInfoVO> incorporationCertificate) {
        this.incorporationCertificate = incorporationCertificate;
    }

    public List<FileInfoVO> getAuthorizationLetter() {
        return authorizationLetter;
    }

    public void setAuthorizationLetter(List<FileInfoVO> authorizationLetter) {
        this.authorizationLetter = authorizationLetter;
    }

    public List<FileInfoVO> getBusinessRegistration() {
        return businessRegistration;
    }

    public void setBusinessRegistration(List<FileInfoVO> businessRegistration) {
        this.businessRegistration = businessRegistration;
    }

    public List<FileInfoVO> getIncumbency() {
        return incumbency;
    }

    public void setIncumbency(List<FileInfoVO> incumbency) {
        this.incumbency = incumbency;
    }
}

package com.kun.linkage.common.db.vo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class KunKycInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long authId;
    /**
     * 董事信息列表
     */
    private List<PersonInfoVO> directorInfo = new ArrayList<>();
    /**
     * 企业信息
     */
    private EnterpriseInfoVO enterpriseInfo = new EnterpriseInfoVO();
    /**
     * 股东信息列表
     */
    private List<PersonInfoVO> shareholdersInfo = new ArrayList<>();

    public Long getAuthId() {
        return authId;
    }

    public void setAuthId(Long authId) {
        this.authId = authId;
    }

    public List<PersonInfoVO> getDirectorInfo() {
        return directorInfo;
    }

    public void setDirectorInfo(List<PersonInfoVO> directorInfo) {
        this.directorInfo = directorInfo;
    }

    public EnterpriseInfoVO getEnterpriseInfo() {
        return enterpriseInfo;
    }

    public void setEnterpriseInfo(EnterpriseInfoVO enterpriseInfo) {
        this.enterpriseInfo = enterpriseInfo;
    }

    public List<PersonInfoVO> getShareholdersInfo() {
        return shareholdersInfo;
    }

    public void setShareholdersInfo(List<PersonInfoVO> shareholdersInfo) {
        this.shareholdersInfo = shareholdersInfo;
    }
}

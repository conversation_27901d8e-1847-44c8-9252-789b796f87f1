package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 业务账户属性配置审核记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@TableName("kl_business_account_attribute_review_record")
public class BusinessAccountAttributeReviewRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 审核id
     */
    @TableId(value = "review_id", type = IdType.ASSIGN_ID)
    private Long reviewId;

    /**
     * 操作类型:Add,Modify
     */
    private String operatorType;

    /**
     * 业务账户属性配置表id,修改时有值
     */
    private Long businessAccountAttributeId;

    /**
     * 1:organization;2:Customer of Organization
     */
    private Integer belong;

    /**
     * 预留
     */
    private String type;

    /**
     * 方向;C: Credit;D: Debit
     */
    private String direction;

    /**
     * 0:not allow;1: allow
     */
    private Integer topupFlag;

    /**
     * 0:not allow;1: allow
     */
    private Integer withdrawalFlag;

    /**
     * 0:not allow;1: allow
     */
    private Integer transferOutFlag;

    /**
     * 审核状态
     */
    private String reviewStatus;

    /**
     * 审核备注
     */
    private String reviewReason;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 提交人id
     */
    private String submitUserId;

    /**
     * 提交人名称
     */
    private String submitUserName;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 审核人id
     */
    private String reviewUserId;

    /**
     * 审核人名称
     */
    private String reviewUserName;

    public Long getReviewId() {
        return reviewId;
    }

    public void setReviewId(Long reviewId) {
        this.reviewId = reviewId;
    }
    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }
    public Long getBusinessAccountAttributeId() {
        return businessAccountAttributeId;
    }

    public void setBusinessAccountAttributeId(Long businessAccountAttributeId) {
        this.businessAccountAttributeId = businessAccountAttributeId;
    }
    public Integer getBelong() {
        return belong;
    }

    public void setBelong(Integer belong) {
        this.belong = belong;
    }
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }
    public Integer getTopupFlag() {
        return topupFlag;
    }

    public void setTopupFlag(Integer topupFlag) {
        this.topupFlag = topupFlag;
    }
    public Integer getWithdrawalFlag() {
        return withdrawalFlag;
    }

    public void setWithdrawalFlag(Integer withdrawalFlag) {
        this.withdrawalFlag = withdrawalFlag;
    }
    public Integer getTransferOutFlag() {
        return transferOutFlag;
    }

    public void setTransferOutFlag(Integer transferOutFlag) {
        this.transferOutFlag = transferOutFlag;
    }
    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }
    public String getReviewReason() {
        return reviewReason;
    }

    public void setReviewReason(String reviewReason) {
        this.reviewReason = reviewReason;
    }
    public LocalDateTime getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(LocalDateTime submitTime) {
        this.submitTime = submitTime;
    }
    public String getSubmitUserId() {
        return submitUserId;
    }

    public void setSubmitUserId(String submitUserId) {
        this.submitUserId = submitUserId;
    }
    public String getSubmitUserName() {
        return submitUserName;
    }

    public void setSubmitUserName(String submitUserName) {
        this.submitUserName = submitUserName;
    }
    public LocalDateTime getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(LocalDateTime reviewTime) {
        this.reviewTime = reviewTime;
    }
    public String getReviewUserId() {
        return reviewUserId;
    }

    public void setReviewUserId(String reviewUserId) {
        this.reviewUserId = reviewUserId;
    }
    public String getReviewUserName() {
        return reviewUserName;
    }

    public void setReviewUserName(String reviewUserName) {
        this.reviewUserName = reviewUserName;
    }

    @Override
    public String toString() {
        return "BusinessAccountAttributeReviewRecord{" +
            "reviewId=" + reviewId +
            ", operatorType=" + operatorType +
            ", businessAccountAttributeId=" + businessAccountAttributeId +
            ", belong=" + belong +
            ", type=" + type +
            ", direction=" + direction +
            ", topupFlag=" + topupFlag +
            ", withdrawalFlag=" + withdrawalFlag +
            ", transferOutFlag=" + transferOutFlag +
            ", reviewStatus=" + reviewStatus +
            ", reviewReason=" + reviewReason +
            ", submitTime=" + submitTime +
            ", submitUserId=" + submitUserId +
            ", submitUserName=" + submitUserName +
            ", reviewTime=" + reviewTime +
            ", reviewUserId=" + reviewUserId +
            ", reviewUserName=" + reviewUserName +
        "}";
    }
}

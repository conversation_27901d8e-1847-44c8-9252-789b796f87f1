package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 机构用户限额累计表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@TableName("kl_organization_customer_limit_usage")
public class OrganizationCustomerLimitUsage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 机构号
     */
    @TableField("organization_no")
    private String organizationNo;

    /**
     * 客户号
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 日累计限额
     */
    @TableField("daily_limit_used_amount")
    private BigDecimal dailyLimitUsedAmount;

    /**
     * 币种码
     */
    @TableField("currency_code")
    private String currencyCode;

    /**
     * 最后一次使用日期
     */
    @TableField("last_used_date")
    private String lastUsedDate;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 最后一次修改时间
     */
    @TableField("last_modify_time")
    private LocalDateTime lastModifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    public BigDecimal getDailyLimitUsedAmount() {
        return dailyLimitUsedAmount;
    }

    public void setDailyLimitUsedAmount(BigDecimal dailyLimitUsedAmount) {
        this.dailyLimitUsedAmount = dailyLimitUsedAmount;
    }
    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }
    public String getLastUsedDate() {
        return lastUsedDate;
    }

    public void setLastUsedDate(String lastUsedDate) {
        this.lastUsedDate = lastUsedDate;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    @Override
    public String toString() {
        return "OrganizationCustomerLimitUsage{" +
            "id=" + id +
            ", organizationNo=" + organizationNo +
            ", customerId=" + customerId +
            ", dailyLimitUsedAmount=" + dailyLimitUsedAmount +
            ", currencyCode=" + currencyCode +
            ", lastUsedDate=" + lastUsedDate +
            ", createTime=" + createTime +
            ", lastModifyTime=" + lastModifyTime +
        "}";
    }
}

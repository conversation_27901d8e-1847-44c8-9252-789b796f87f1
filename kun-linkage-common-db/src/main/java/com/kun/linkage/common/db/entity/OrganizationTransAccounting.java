package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 商户交易记账明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@TableName("kl_organization_trans_accounting_202507")
public class OrganizationTransAccounting implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("id")
    private String id;

    /**
     * 机构号
     */
    @TableField("organization_no")
    private String organizationNo;

    /**
     * 客户号
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 记账请求流水号
     */
    @TableField("request_no")
    private String requestNo;

    /**
     * 卡id
     */
    @TableField("card_id")
    private String cardId;

    /**
     * 交易时间
     */
    @TableField("trans_time")
    private Date transTime;

    /**
     * 交易金额(卡片币种)
     */
    @TableField("trans_amount")
    private BigDecimal transAmount;

    /**
     * 交易币种(卡片币种)
     */
    @TableField("trans_currency_code")
    private String transCurrencyCode;

    /**
     * 交易币种精度(卡片币种)
     */
    @TableField("trans_currency_precision")
    private Integer transCurrencyPrecision;

    /**
     * 交易流水号
     */
    @TableField("trans_id")
    private String transId;

    /**
     * 换汇汇率
     */
    @TableField("fx_rate")
    private BigDecimal fxRate;

    /**
     * 扣除币种
     */
    @TableField("deduct_currency_code")
    private String deductCurrencyCode;

    /**
     * 扣除处理方(KUN和PAYX)
     */
    @TableField("deduct_processor")
    private String deductProcessor;

    /**
     * 扣除币种精度
     */
    @TableField("deduct_currency_precision")
    private Integer deductCurrencyPrecision;

    /**
     * 扣除本金金额
     */
    @TableField("deduct_amount")
    private BigDecimal deductAmount;

    /**
     * 扣除总金额
     */
    @TableField("deduct_total_amount")
    private BigDecimal deductTotalAmount;

    /**
     * 记账状态:SUCCESS,FAIL,PENDING
     */
    @TableField("bookkeep_status")
    private String bookkeepStatus;

    /**
     * 失败信息
     */
    @TableField("fail_message")
    private String failMessage;

    /**
     * 记账冲正次数
     */
    @TableField("bookkeep_reversal_count")
    private Integer bookkeepReversalCount;

    /**
     * 记账冲正状态:SUCCESS,FAIL,PENDING
     */
    @TableField("bookkeep_reversal_status")
    private String bookkeepReversalStatus;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最后一次修改时间
     */
    @TableField("last_modify_time")
    private Date lastModifyTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }
    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }
    public Date getTransTime() {
        return transTime;
    }

    public void setTransTime(Date transTime) {
        this.transTime = transTime;
    }
    public BigDecimal getTransAmount() {
        return transAmount;
    }

    public void setTransAmount(BigDecimal transAmount) {
        this.transAmount = transAmount;
    }
    public String getTransCurrencyCode() {
        return transCurrencyCode;
    }

    public void setTransCurrencyCode(String transCurrencyCode) {
        this.transCurrencyCode = transCurrencyCode;
    }
    public Integer getTransCurrencyPrecision() {
        return transCurrencyPrecision;
    }

    public void setTransCurrencyPrecision(Integer transCurrencyPrecision) {
        this.transCurrencyPrecision = transCurrencyPrecision;
    }
    public String getTransId() {
        return transId;
    }

    public void setTransId(String transId) {
        this.transId = transId;
    }
    public BigDecimal getFxRate() {
        return fxRate;
    }

    public void setFxRate(BigDecimal fxRate) {
        this.fxRate = fxRate;
    }
    public String getDeductCurrencyCode() {
        return deductCurrencyCode;
    }

    public void setDeductCurrencyCode(String deductCurrencyCode) {
        this.deductCurrencyCode = deductCurrencyCode;
    }
    public String getDeductProcessor() {
        return deductProcessor;
    }

    public void setDeductProcessor(String deductProcessor) {
        this.deductProcessor = deductProcessor;
    }
    public Integer getDeductCurrencyPrecision() {
        return deductCurrencyPrecision;
    }

    public void setDeductCurrencyPrecision(Integer deductCurrencyPrecision) {
        this.deductCurrencyPrecision = deductCurrencyPrecision;
    }
    public BigDecimal getDeductAmount() {
        return deductAmount;
    }

    public void setDeductAmount(BigDecimal deductAmount) {
        this.deductAmount = deductAmount;
    }
    public BigDecimal getDeductTotalAmount() {
        return deductTotalAmount;
    }

    public void setDeductTotalAmount(BigDecimal deductTotalAmount) {
        this.deductTotalAmount = deductTotalAmount;
    }
    public String getBookkeepStatus() {
        return bookkeepStatus;
    }

    public void setBookkeepStatus(String bookkeepStatus) {
        this.bookkeepStatus = bookkeepStatus;
    }
    public String getFailMessage() {
        return failMessage;
    }

    public void setFailMessage(String failMessage) {
        this.failMessage = failMessage;
    }
    public Integer getBookkeepReversalCount() {
        return bookkeepReversalCount;
    }

    public void setBookkeepReversalCount(Integer bookkeepReversalCount) {
        this.bookkeepReversalCount = bookkeepReversalCount;
    }
    public String getBookkeepReversalStatus() {
        return bookkeepReversalStatus;
    }

    public void setBookkeepReversalStatus(String bookkeepReversalStatus) {
        this.bookkeepReversalStatus = bookkeepReversalStatus;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    @Override
    public String toString() {
        return "OrganizationTransAccounting{" +
            "id=" + id +
            ", organizationNo=" + organizationNo +
            ", customerId=" + customerId +
            ", requestNo=" + requestNo +
            ", cardId=" + cardId +
            ", transTime=" + transTime +
            ", transAmount=" + transAmount +
            ", transCurrencyCode=" + transCurrencyCode +
            ", transCurrencyPrecision=" + transCurrencyPrecision +
            ", transId=" + transId +
            ", fxRate=" + fxRate +
            ", deductCurrencyCode=" + deductCurrencyCode +
            ", deductProcessor=" + deductProcessor +
            ", deductCurrencyPrecision=" + deductCurrencyPrecision +
            ", deductAmount=" + deductAmount +
            ", deductTotalAmount=" + deductTotalAmount +
            ", bookkeepStatus=" + bookkeepStatus +
            ", failMessage=" + failMessage +
            ", bookkeepReversalCount=" + bookkeepReversalCount +
            ", bookkeepReversalStatus=" + bookkeepReversalStatus +
            ", createTime=" + createTime +
            ", lastModifyTime=" + lastModifyTime +
        "}";
    }
}

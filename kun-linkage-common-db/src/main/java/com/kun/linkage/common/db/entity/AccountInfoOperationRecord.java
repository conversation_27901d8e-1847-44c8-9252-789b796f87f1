package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 基础账户信息操作记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@TableName("kl_account_info_operation_record")
public class AccountInfoOperationRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId("id")
    private Long id;

    /**
     * 操作类型:ADD/MODIFY
     */
    @TableField("operation_type")
    private String operationType;

    /**
     * 业务系统:KL/VCC
     */
    @TableField("business_system")
    private String businessSystem;

    /**
     * 业务机构号(分表键)
     */
    @TableField("business_organization_no")
    private String businessOrganizationNo;

    /**
     * 请求流水号
     */
    @TableField("request_no")
    private String requestNo;

    /**
     * 账户号
     */
    @TableField("account_no")
    private String accountNo;

    /**
     * 请求参数(json)
     */
    @TableField("request_params")
    private String requestParams;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }
    public String getBusinessSystem() {
        return businessSystem;
    }

    public void setBusinessSystem(String businessSystem) {
        this.businessSystem = businessSystem;
    }
    public String getBusinessOrganizationNo() {
        return businessOrganizationNo;
    }

    public void setBusinessOrganizationNo(String businessOrganizationNo) {
        this.businessOrganizationNo = businessOrganizationNo;
    }
    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }
    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }
    public String getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(String requestParams) {
        this.requestParams = requestParams;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "AccountInfoOperationRecord{" +
            "id=" + id +
            ", operationType=" + operationType +
            ", businessSystem=" + businessSystem +
            ", businessOrganizationNo=" + businessOrganizationNo +
            ", requestNo=" + requestNo +
            ", accountNo=" + accountNo +
            ", requestParams=" + requestParams +
            ", createTime=" + createTime +
        "}";
    }
}

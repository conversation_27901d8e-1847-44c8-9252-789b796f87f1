package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@TableName("vcc_boss_role_permission")
public class VccBossRolePermission implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long bossRoleId;

    private Long permissionId;

    private Integer status;

    @TableId
    private Long id;

    private Date createDate;

    private Date updateDate;

    public Long getBossRoleId() {
        return bossRoleId;
    }

    public void setBossRoleId(Long bossRoleId) {
        this.bossRoleId = bossRoleId;
    }
    public Long getPermissionId() {
        return permissionId;
    }

    public void setPermissionId(Long permissionId) {
        this.permissionId = permissionId;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "BossRolePermission{" +
            "bossRoleId=" + bossRoleId +
            ", permissionId=" + permissionId +
            ", status=" + status +
            ", id=" + id +
            ", createDate=" + createDate +
            ", updateDate=" + updateDate +
        "}";
    }
}

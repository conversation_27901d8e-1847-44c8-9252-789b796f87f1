package com.kun.linkage.common.db.sharding.algorithm;

import org.apache.shardingsphere.sharding.api.sharding.standard.PreciseShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.RangeShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.StandardShardingAlgorithm;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.Properties;

public class LocalDateTimeDynamicYearMonthShardingAlgorithm implements StandardShardingAlgorithm<LocalDateTime> {
    private static DateTimeFormatter FORMATTER;
    private Properties props = new Properties();

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<LocalDateTime> shardingValue) {
        LocalDateTime time = shardingValue.getValue();
        String suffix = time.format(FORMATTER);
        String targetTable = shardingValue.getLogicTableName() + "_" + suffix;
        if (availableTargetNames.contains(targetTable)) {
            return targetTable;
        }
        // 表不存在
        throw new RuntimeException("目标表 " + targetTable + " 不存在");
    }

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, RangeShardingValue<LocalDateTime> shardingValue) {
        Collection<String> result = new LinkedHashSet<>();
        YearMonth start = YearMonth.from(shardingValue.getValueRange().lowerEndpoint());
        YearMonth end = YearMonth.from(shardingValue.getValueRange().upperEndpoint());
        // 遍历范围内的每个月
        YearMonth current = start;
        while (current.isBefore(end) || current.equals(end)) {
            String suffix = current.format(FORMATTER);
            String targetTable = shardingValue.getLogicTableName() + "_" + suffix;
            if (availableTargetNames.contains(targetTable)) {
                result.add(targetTable);
            }
            current = current.plusMonths(1);
        }
        if (result.isEmpty()) {
            throw new RuntimeException("未找到目标表");
        }
        return result;
    }

    @Override
    public Properties getProps() {
        return props;
    }

    @Override
    public void init(Properties props) {
        String pattern = props.getProperty("sharding-suffix-pattern", "yyyyMM");
        FORMATTER = DateTimeFormatter.ofPattern(pattern);
        this.props = props;
    }

    @Override
    public String getType() {
        // 自定义算法类型名称
        return "LOCAL_DATE_TIME_DYNAMIC_YEAR_MONTH";
    }
}

package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 卡充值明细表(按季度分表)
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@TableName("kl_card_recharge_detail")
public class CardRechargeDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 机构号
     */
    @TableField("organization_no")
    private String organizationNo;

    /**
     * 业务类型
     */
    @TableField("business_type")
    private String businessType;

    /**
     * 客户号
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 请求流水号
     */
    @TableField("request_no")
    private String requestNo;

    /**
     * 卡id
     */
    @TableField("card_id")
    private String cardId;

    /**
     * 充值日期时间
     */
    @TableField("recharge_datetime")
    private LocalDateTime rechargeDatetime;

    /**
     * 充值金额(卡片币种)
     */
    @TableField("recharge_amount")
    private BigDecimal rechargeAmount;

    /**
     * 充值币种(卡片币种)
     */
    @TableField("recharge_currency_code")
    private String rechargeCurrencyCode;

    /**
     * 充值币种精度(卡片币种)
     */
    @TableField("recharge_currency_precision")
    private Integer rechargeCurrencyPrecision;

    /**
     * 用户上账记账请求单号(账户中的业务系统流水号)
     */
    @TableField("recharge_bookkeep_request_no")
    private String rechargeBookkeepRequestNo;

    /**
     * 用户上账记账状态:0:未记账或已冲账或记账明确失败;1:已记账;2:无需记账;3:未知
     */
    @TableField("recharge_bookkeep_status")
    private Integer rechargeBookkeepStatus;

    /**
     * 换汇汇率
     */
    @TableField("fx_rate")
    private BigDecimal fxRate;

    /**
     * 扣除币种
     */
    @TableField("deduct_currency_code")
    private String deductCurrencyCode;

    /**
     * 扣除处理方(KUN和PAYX)
     */
    @TableField("deduct_processor")
    private String deductProcessor;

    /**
     * 扣除币种精度
     */
    @TableField("deduct_currency_precision")
    private Integer deductCurrencyPrecision;

    /**
     * 扣除本金金额
     */
    @TableField("deduct_principal_amount")
    private BigDecimal deductPrincipalAmount;

    /**
     * 扣除本金记账请求单号(kun或payx中的requestNo)
     */
    @TableField("deduct_principal_bookkeep_request_no")
    private String deductPrincipalBookkeepRequestNo;

    /**
     * 扣除本金记账状态:0:未记账或已冲账或记账明确失败;1:已记账;2:无需记账;3:未知
     */
    @TableField("deduct_principal_bookkeep_status")
    private Integer deductPrincipalBookkeepStatus;

    /**
     * 扣除充值手续费金额
     */
    @TableField("deduct_recharge_fee_amount")
    private BigDecimal deductRechargeFeeAmount;

    /**
     * 扣除充值手续费记账请求单号(kun或payx中的requestNo)
     */
    @TableField("deduct_recharge_fee_bookkeep_request_no")
    private String deductRechargeFeeBookkeepRequestNo;

    /**
     * 扣除充值手续费记账状态:0:未记账或已冲账或记账明确失败;1:已记账;2:无需记账;3:未知
     */
    @TableField("deduct_recharge_fee_bookkeep_status")
    private Integer deductRechargeFeeBookkeepStatus;

    /**
     * 扣除充值手续费费用明细记录id
     */
    @TableField("deduct_recharge_fee_detail_id")
    private String deductRechargeFeeDetailId;

    /**
     * 扣除充值承兑费金额
     */
    @TableField("deduct_acceptance_fee_amount")
    private BigDecimal deductAcceptanceFeeAmount;

    /**
     * 扣除充值承兑费记账请求单号(kun或payx中的requestNo)
     */
    @TableField("deduct_acceptance_fee_bookkeep_request_no")
    private String deductAcceptanceFeeBookkeepRequestNo;

    /**
     * 扣除充值承兑费记账状态:0:未记账或已冲账或记账明确失败;1:已记账;2:无需记账;3:未知
     */
    @TableField("deduct_acceptance_fee_bookkeep_status")
    private Integer deductAcceptanceFeeBookkeepStatus;

    /**
     * 扣除充值承兑费费用明细记录id
     */
    @TableField("deduct_acceptance_fee_detail_id")
    private String deductAcceptanceFeeDetailId;

    /**
     * 扣除总金额
     */
    @TableField("deduct_total_amount")
    private BigDecimal deductTotalAmount;

    /**
     * 充值状态:SUCCESS,FAIL,PENDING
     */
    @TableField("recharge_status")
    private String rechargeStatus;

    /**
     * 失败信息
     */
    @TableField("fail_message")
    private String failMessage;

    /**
     * 记账冲正次数
     */
    @TableField("bookkeep_reversal_count")
    private Integer bookkeepReversalCount;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 最后一次修改时间
     */
    @TableField("last_modify_time")
    private LocalDateTime lastModifyTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }
    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }
    public LocalDateTime getRechargeDatetime() {
        return rechargeDatetime;
    }

    public void setRechargeDatetime(LocalDateTime rechargeDatetime) {
        this.rechargeDatetime = rechargeDatetime;
    }
    public BigDecimal getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(BigDecimal rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }
    public String getRechargeCurrencyCode() {
        return rechargeCurrencyCode;
    }

    public void setRechargeCurrencyCode(String rechargeCurrencyCode) {
        this.rechargeCurrencyCode = rechargeCurrencyCode;
    }
    public Integer getRechargeCurrencyPrecision() {
        return rechargeCurrencyPrecision;
    }

    public void setRechargeCurrencyPrecision(Integer rechargeCurrencyPrecision) {
        this.rechargeCurrencyPrecision = rechargeCurrencyPrecision;
    }
    public String getRechargeBookkeepRequestNo() {
        return rechargeBookkeepRequestNo;
    }

    public void setRechargeBookkeepRequestNo(String rechargeBookkeepRequestNo) {
        this.rechargeBookkeepRequestNo = rechargeBookkeepRequestNo;
    }
    public Integer getRechargeBookkeepStatus() {
        return rechargeBookkeepStatus;
    }

    public void setRechargeBookkeepStatus(Integer rechargeBookkeepStatus) {
        this.rechargeBookkeepStatus = rechargeBookkeepStatus;
    }
    public BigDecimal getFxRate() {
        return fxRate;
    }

    public void setFxRate(BigDecimal fxRate) {
        this.fxRate = fxRate;
    }
    public String getDeductCurrencyCode() {
        return deductCurrencyCode;
    }

    public void setDeductCurrencyCode(String deductCurrencyCode) {
        this.deductCurrencyCode = deductCurrencyCode;
    }
    public String getDeductProcessor() {
        return deductProcessor;
    }

    public void setDeductProcessor(String deductProcessor) {
        this.deductProcessor = deductProcessor;
    }
    public Integer getDeductCurrencyPrecision() {
        return deductCurrencyPrecision;
    }

    public void setDeductCurrencyPrecision(Integer deductCurrencyPrecision) {
        this.deductCurrencyPrecision = deductCurrencyPrecision;
    }
    public BigDecimal getDeductPrincipalAmount() {
        return deductPrincipalAmount;
    }

    public void setDeductPrincipalAmount(BigDecimal deductPrincipalAmount) {
        this.deductPrincipalAmount = deductPrincipalAmount;
    }
    public String getDeductPrincipalBookkeepRequestNo() {
        return deductPrincipalBookkeepRequestNo;
    }

    public void setDeductPrincipalBookkeepRequestNo(String deductPrincipalBookkeepRequestNo) {
        this.deductPrincipalBookkeepRequestNo = deductPrincipalBookkeepRequestNo;
    }
    public Integer getDeductPrincipalBookkeepStatus() {
        return deductPrincipalBookkeepStatus;
    }

    public void setDeductPrincipalBookkeepStatus(Integer deductPrincipalBookkeepStatus) {
        this.deductPrincipalBookkeepStatus = deductPrincipalBookkeepStatus;
    }
    public BigDecimal getDeductRechargeFeeAmount() {
        return deductRechargeFeeAmount;
    }

    public void setDeductRechargeFeeAmount(BigDecimal deductRechargeFeeAmount) {
        this.deductRechargeFeeAmount = deductRechargeFeeAmount;
    }
    public String getDeductRechargeFeeBookkeepRequestNo() {
        return deductRechargeFeeBookkeepRequestNo;
    }

    public void setDeductRechargeFeeBookkeepRequestNo(String deductRechargeFeeBookkeepRequestNo) {
        this.deductRechargeFeeBookkeepRequestNo = deductRechargeFeeBookkeepRequestNo;
    }
    public Integer getDeductRechargeFeeBookkeepStatus() {
        return deductRechargeFeeBookkeepStatus;
    }

    public void setDeductRechargeFeeBookkeepStatus(Integer deductRechargeFeeBookkeepStatus) {
        this.deductRechargeFeeBookkeepStatus = deductRechargeFeeBookkeepStatus;
    }
    public String getDeductRechargeFeeDetailId() {
        return deductRechargeFeeDetailId;
    }

    public void setDeductRechargeFeeDetailId(String deductRechargeFeeDetailId) {
        this.deductRechargeFeeDetailId = deductRechargeFeeDetailId;
    }
    public BigDecimal getDeductAcceptanceFeeAmount() {
        return deductAcceptanceFeeAmount;
    }

    public void setDeductAcceptanceFeeAmount(BigDecimal deductAcceptanceFeeAmount) {
        this.deductAcceptanceFeeAmount = deductAcceptanceFeeAmount;
    }
    public String getDeductAcceptanceFeeBookkeepRequestNo() {
        return deductAcceptanceFeeBookkeepRequestNo;
    }

    public void setDeductAcceptanceFeeBookkeepRequestNo(String deductAcceptanceFeeBookkeepRequestNo) {
        this.deductAcceptanceFeeBookkeepRequestNo = deductAcceptanceFeeBookkeepRequestNo;
    }
    public Integer getDeductAcceptanceFeeBookkeepStatus() {
        return deductAcceptanceFeeBookkeepStatus;
    }

    public void setDeductAcceptanceFeeBookkeepStatus(Integer deductAcceptanceFeeBookkeepStatus) {
        this.deductAcceptanceFeeBookkeepStatus = deductAcceptanceFeeBookkeepStatus;
    }
    public String getDeductAcceptanceFeeDetailId() {
        return deductAcceptanceFeeDetailId;
    }

    public void setDeductAcceptanceFeeDetailId(String deductAcceptanceFeeDetailId) {
        this.deductAcceptanceFeeDetailId = deductAcceptanceFeeDetailId;
    }
    public BigDecimal getDeductTotalAmount() {
        return deductTotalAmount;
    }

    public void setDeductTotalAmount(BigDecimal deductTotalAmount) {
        this.deductTotalAmount = deductTotalAmount;
    }
    public String getRechargeStatus() {
        return rechargeStatus;
    }

    public void setRechargeStatus(String rechargeStatus) {
        this.rechargeStatus = rechargeStatus;
    }
    public String getFailMessage() {
        return failMessage;
    }

    public void setFailMessage(String failMessage) {
        this.failMessage = failMessage;
    }
    public Integer getBookkeepReversalCount() {
        return bookkeepReversalCount;
    }

    public void setBookkeepReversalCount(Integer bookkeepReversalCount) {
        this.bookkeepReversalCount = bookkeepReversalCount;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    @Override
    public String toString() {
        return "CardRechargeDetail{" +
            "id=" + id +
            ", organizationNo=" + organizationNo +
            ", businessType=" + businessType +
            ", customerId=" + customerId +
            ", requestNo=" + requestNo +
            ", cardId=" + cardId +
            ", rechargeDatetime=" + rechargeDatetime +
            ", rechargeAmount=" + rechargeAmount +
            ", rechargeCurrencyCode=" + rechargeCurrencyCode +
            ", rechargeCurrencyPrecision=" + rechargeCurrencyPrecision +
            ", rechargeBookkeepRequestNo=" + rechargeBookkeepRequestNo +
            ", rechargeBookkeepStatus=" + rechargeBookkeepStatus +
            ", fxRate=" + fxRate +
            ", deductCurrencyCode=" + deductCurrencyCode +
            ", deductProcessor=" + deductProcessor +
            ", deductCurrencyPrecision=" + deductCurrencyPrecision +
            ", deductPrincipalAmount=" + deductPrincipalAmount +
            ", deductPrincipalBookkeepRequestNo=" + deductPrincipalBookkeepRequestNo +
            ", deductPrincipalBookkeepStatus=" + deductPrincipalBookkeepStatus +
            ", deductRechargeFeeAmount=" + deductRechargeFeeAmount +
            ", deductRechargeFeeBookkeepRequestNo=" + deductRechargeFeeBookkeepRequestNo +
            ", deductRechargeFeeBookkeepStatus=" + deductRechargeFeeBookkeepStatus +
            ", deductRechargeFeeDetailId=" + deductRechargeFeeDetailId +
            ", deductAcceptanceFeeAmount=" + deductAcceptanceFeeAmount +
            ", deductAcceptanceFeeBookkeepRequestNo=" + deductAcceptanceFeeBookkeepRequestNo +
            ", deductAcceptanceFeeBookkeepStatus=" + deductAcceptanceFeeBookkeepStatus +
            ", deductAcceptanceFeeDetailId=" + deductAcceptanceFeeDetailId +
            ", deductTotalAmount=" + deductTotalAmount +
            ", rechargeStatus=" + rechargeStatus +
            ", failMessage=" + failMessage +
            ", bookkeepReversalCount=" + bookkeepReversalCount +
            ", createTime=" + createTime +
            ", lastModifyTime=" + lastModifyTime +
        "}";
    }
}

package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 机构卡产品信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
@TableName("kl_organization_application_card")
public class OrganizationApplicationCard implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 机构号
     */
    private String organizationNo;

    /**
     * 卡产品
     */
    private String cardProductCode;

    /**
     * 卡片处理方
     */
    private String processor;

    /**
     * 自动激活标记
     */
    private Integer autoActivationFlag;

    /**
     * 状态
     */
    @TableField("`status`")
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 最后一次修改时间
     */
    private LocalDateTime lastModifyTime;

    /**
     * 最后一次修改人id
     */
    private String lastModifyUserId;

    /**
     * 最后一次修改人名称
     */
    private String lastModifyUserName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public String getCardProductCode() {
        return cardProductCode;
    }

    public Integer getAutoActivationFlag() {
        return autoActivationFlag;
    }

    public void setAutoActivationFlag(Integer autoActivationFlag) {
        this.autoActivationFlag = autoActivationFlag;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }

    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }
    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }
    public String getLastModifyUserId() {
        return lastModifyUserId;
    }

    public void setLastModifyUserId(String lastModifyUserId) {
        this.lastModifyUserId = lastModifyUserId;
    }
    public String getLastModifyUserName() {
        return lastModifyUserName;
    }

    public void setLastModifyUserName(String lastModifyUserName) {
        this.lastModifyUserName = lastModifyUserName;
    }

    @Override
    public String toString() {
        return "OrganizationApplicationCard{" +
            "id=" + id +
            ", organizationNo=" + organizationNo +
            ", cardProductCode=" + cardProductCode +
            ", status=" + status +
            ", createTime=" + createTime +
            ", createUserId=" + createUserId +
            ", createUserName=" + createUserName +
            ", lastModifyTime=" + lastModifyTime +
            ", lastModifyUserId=" + lastModifyUserId +
            ", lastModifyUserName=" + lastModifyUserName +
        "}";
    }
}

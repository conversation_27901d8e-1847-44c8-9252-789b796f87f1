package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 业务账户属性配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@TableName("kl_business_account_attribute")
public class BusinessAccountAttribute implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 1:organization;2:Customer of Organization
     */
    private Integer belong;

    /**
     * 预留
     */
    private String type;

    /**
     * 方向;C: Credit;D: Debit
     */
    private String direction;

    /**
     * 0:not allow;1: allow
     */
    private Integer topupFlag;

    /**
     * 0:not allow;1: allow
     */
    private Integer withdrawalFlag;

    /**
     * 0:not allow;1: allow
     */
    private Integer transferOutFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 最后一次修改时间
     */
    private LocalDateTime lastModifyTime;

    /**
     * 最后一次修改人id
     */
    private String lastModifyUserId;

    /**
     * 最后一次修改人名称
     */
    private String lastModifyUserName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public Integer getBelong() {
        return belong;
    }

    public void setBelong(Integer belong) {
        this.belong = belong;
    }
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }
    public Integer getTopupFlag() {
        return topupFlag;
    }

    public void setTopupFlag(Integer topupFlag) {
        this.topupFlag = topupFlag;
    }
    public Integer getWithdrawalFlag() {
        return withdrawalFlag;
    }

    public void setWithdrawalFlag(Integer withdrawalFlag) {
        this.withdrawalFlag = withdrawalFlag;
    }
    public Integer getTransferOutFlag() {
        return transferOutFlag;
    }

    public void setTransferOutFlag(Integer transferOutFlag) {
        this.transferOutFlag = transferOutFlag;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }
    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }
    public String getLastModifyUserId() {
        return lastModifyUserId;
    }

    public void setLastModifyUserId(String lastModifyUserId) {
        this.lastModifyUserId = lastModifyUserId;
    }
    public String getLastModifyUserName() {
        return lastModifyUserName;
    }

    public void setLastModifyUserName(String lastModifyUserName) {
        this.lastModifyUserName = lastModifyUserName;
    }

    @Override
    public String toString() {
        return "BusinessAccountAttribute{" +
            "id=" + id +
            ", belong=" + belong +
            ", type=" + type +
            ", direction=" + direction +
            ", topupFlag=" + topupFlag +
            ", withdrawalFlag=" + withdrawalFlag +
            ", transferOutFlag=" + transferOutFlag +
            ", createTime=" + createTime +
            ", createUserId=" + createUserId +
            ", createUserName=" + createUserName +
            ", lastModifyTime=" + lastModifyTime +
            ", lastModifyUserId=" + lastModifyUserId +
            ", lastModifyUserName=" + lastModifyUserName +
        "}";
    }
}

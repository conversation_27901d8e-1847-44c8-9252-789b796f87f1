package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * Visa baseTC33数据
 * </p>
 *
 */
@TableName("kc_visa_base_33_data")
public class VisaBase33Data implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 清分文件
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 交易代码
     */
    @TableField("transaction_code")
    private String transactionCode;

    /**
     * 交易代码限定符_0
     */
    @TableField("transaction_code_qualifier_0")
    private String transactionCodeQualifier0;

    /**
     * 交易组件序列号_0
     */
    @TableField("transaction_component_sequence_number_0")
    private String transactionComponentSequenceNumber0;

    /**
     * 目标标识符_0
     */
    @TableField("destination_identifier_0")
    private String destinationIdentifier0;

    /**
     * 源标识符_0
     */
    @TableField("source_identifier_0")
    private String sourceIdentifier0;

    /**
     * VCR记录标识符_0
     */
    @TableField("vcr_record_identifier_0")
    private String vcrRecordIdentifier0;

    /**
     * 争议状态_0
     */
    @TableField("dispute_status_0")
    private String disputeStatus0;

    /**
     * 争议交易代码_0
     */
    @TableField("dispute_transaction_code_0")
    private String disputeTransactionCode0;

    /**
     * 争议交易代码限定符_0
     */
    @TableField("dispute_transaction_code_qualifier_0")
    private String disputeTransactionCodeQualifier0;

    /**
     * 发起方接收方指示符_0
     */
    @TableField("originator_recipient_indicator_0")
    private String originatorRecipientIndicator0;

    /**
     * 账户号码_0
     */
    @TableField("account_number_0")
    private String accountNumber0;

    /**
     * 账户号码扩展_0
     */
    @TableField("account_number_extension_0")
    private String accountNumberExtension0;

    /**
     * 收单行参考号_0
     */
    @TableField("acquirer_reference_number_0")
    private String acquirerReferenceNumber0;

    /**
     * 购买日期_0
     */
    @TableField("purchase_date_0")
    private String purchaseDate0;

    /**
     * 源金额_0
     */
    @TableField("source_amount_0")
    private String sourceAmount0;

    /**
     * 源货币代码_0
     */
    @TableField("source_currency_code_0")
    private String sourceCurrencyCode0;

    /**
     * 商户名称_0
     */
    @TableField("merchant_name_0")
    private String merchantName0;

    /**
     * 商户城市_0
     */
    @TableField("merchant_city_0")
    private String merchantCity0;

    /**
     * 商户国家代码_0
     */
    @TableField("merchant_country_code_0")
    private String merchantCountryCode0;

    /**
     * 商户类别代码_0
     */
    @TableField("merchant_category_code_0")
    private String merchantCategoryCode0;

    /**
     * 商户州/省代码_0
     */
    @TableField("merchant_state_province_code_0")
    private String merchantStateProvinceCode0;

    /**
     * 商户邮政编码_0
     */
    @TableField("merchant_zip_code_0")
    private String merchantZipCode0;

    /**
     * 请求的支付服务_0
     */
    @TableField("requested_payment_service_0")
    private String requestedPaymentService0;

    /**
     * 授权代码_0
     */
    @TableField("authorization_code_0")
    private String authorizationCode0;

    /**
     * POS输入模式_0
     */
    @TableField("pos_entry_mode_0")
    private String posEntryMode0;

    /**
     * 中央处理日期_0
     */
    @TableField("central_processing_date_0")
    private String centralProcessingDate0;

    /**
     * 收卡人ID_0
     */
    @TableField("card_acceptor_id_0")
    private String cardAcceptorId0;

    /**
     * 报销属性_0
     */
    @TableField("reimbursement_attribute_0")
    private String reimbursementAttribute0;

    /**
     * 交易代码限定符_1
     */
    @TableField("transaction_code_qualifier_1")
    private String transactionCodeQualifier1;

    /**
     * 交易组件序列号_1
     */
    @TableField("transaction_component_sequence_number_1")
    private String transactionComponentSequenceNumber1;

    /**
     * 网络识别代码_1
     */
    @TableField("network_identification_code_1")
    private String networkIdentificationCode1;

    /**
     * 争议条件_1
     */
    @TableField("dispute_condition_1")
    private String disputeCondition1;

    /**
     * VRQL财务ID_1
     */
    @TableField("vrql_financial_id_1")
    private String vrqlFinancialId1;

    /**
     * VRQL案例号_1
     */
    @TableField("vrql_case_number_1")
    private String vrqlCaseNumber1;

    /**
     * VRQL捆绑案例号_1
     */
    @TableField("vrql_bundle_case_number_1")
    private String vrqlBundleCaseNumber1;

    /**
     * 客户案例号_1
     */
    @TableField("client_case_number_1")
    private String clientCaseNumber1;

    /**
     * 保留字段1_1
     */
    @TableField("reserved1_1")
    private String reserved11;

    /**
     * 多次清算序列号_1
     */
    @TableField("multiple_clearing_sequence_number_1")
    private String multipleClearingSequenceNumber1;

    /**
     * 多次清算序列计数_1
     */
    @TableField("multiple_clearing_sequence_count_1")
    private String multipleClearingSequenceCount1;

    /**
     * 产品ID_1
     */
    @TableField("product_id_1")
    private String productId1;

    /**
     * 消费合格指示符_1
     */
    @TableField("spend_qualified_indicator_1")
    private String spendQualifiedIndicator1;

    /**
     * 争议财务原因代码_1
     */
    @TableField("dispute_financial_reason_code_1")
    private String disputeFinancialReasonCode1;

    /**
     * 结算标志_1
     */
    @TableField("settlement_flag_1")
    private String settlementFlag1;

    /**
     * 使用代码_1
     */
    @TableField("usage_code_1")
    private String usageCode1;

    /**
     * 交易标识符_1
     */
    @TableField("transaction_identifier_1")
    private String transactionIdentifier1;

    /**
     * 收单行商业ID_1
     */
    @TableField("acquirers_business_id_1")
    private String acquirersBusinessId1;

    /**
     * 原始交易金额_1
     */
    @TableField("original_transaction_amount_1")
    private String originalTransactionAmount1;

    /**
     * 原始交易货币代码_1
     */
    @TableField("original_transaction_currency_code_1")
    private String originalTransactionCurrencyCode1;

    /**
     * 特殊退单指示符_1
     */
    @TableField("special_chargeback_indicator_1")
    private String specialChargebackIndicator1;

    /**
     * 目标/源结算金额_1
     */
    @TableField("destination_source_settlement_amount_1")
    private String destinationSourceSettlementAmount1;

    /**
     * 目标/源结算货币_1
     */
    @TableField("destination_source_settlement_currency_1")
    private String destinationSourceSettlementCurrency1;

    /**
     * 源结算金额符号_1
     */
    @TableField("source_settlement_amount_sign_1")
    private String sourceSettlementAmountSign1;

    /**
     * 费率表ID_1
     */
    @TableField("rate_table_id_1")
    private String rateTableId1;

    /**
     * 保留字段2_1
     */
    @TableField("reserved2_1")
    private String reserved21;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    public String getTransactionCode() {
        return transactionCode;
    }

    public void setTransactionCode(String transactionCode) {
        this.transactionCode = transactionCode;
    }
    public String getTransactionCodeQualifier0() {
        return transactionCodeQualifier0;
    }

    public void setTransactionCodeQualifier0(String transactionCodeQualifier0) {
        this.transactionCodeQualifier0 = transactionCodeQualifier0;
    }
    public String getTransactionComponentSequenceNumber0() {
        return transactionComponentSequenceNumber0;
    }

    public void setTransactionComponentSequenceNumber0(String transactionComponentSequenceNumber0) {
        this.transactionComponentSequenceNumber0 = transactionComponentSequenceNumber0;
    }
    public String getDestinationIdentifier0() {
        return destinationIdentifier0;
    }

    public void setDestinationIdentifier0(String destinationIdentifier0) {
        this.destinationIdentifier0 = destinationIdentifier0;
    }
    public String getSourceIdentifier0() {
        return sourceIdentifier0;
    }

    public void setSourceIdentifier0(String sourceIdentifier0) {
        this.sourceIdentifier0 = sourceIdentifier0;
    }
    public String getVcrRecordIdentifier0() {
        return vcrRecordIdentifier0;
    }

    public void setVcrRecordIdentifier0(String vcrRecordIdentifier0) {
        this.vcrRecordIdentifier0 = vcrRecordIdentifier0;
    }
    public String getDisputeStatus0() {
        return disputeStatus0;
    }

    public void setDisputeStatus0(String disputeStatus0) {
        this.disputeStatus0 = disputeStatus0;
    }
    public String getDisputeTransactionCode0() {
        return disputeTransactionCode0;
    }

    public void setDisputeTransactionCode0(String disputeTransactionCode0) {
        this.disputeTransactionCode0 = disputeTransactionCode0;
    }
    public String getDisputeTransactionCodeQualifier0() {
        return disputeTransactionCodeQualifier0;
    }

    public void setDisputeTransactionCodeQualifier0(String disputeTransactionCodeQualifier0) {
        this.disputeTransactionCodeQualifier0 = disputeTransactionCodeQualifier0;
    }
    public String getOriginatorRecipientIndicator0() {
        return originatorRecipientIndicator0;
    }

    public void setOriginatorRecipientIndicator0(String originatorRecipientIndicator0) {
        this.originatorRecipientIndicator0 = originatorRecipientIndicator0;
    }
    public String getAccountNumber0() {
        return accountNumber0;
    }

    public void setAccountNumber0(String accountNumber0) {
        this.accountNumber0 = accountNumber0;
    }
    public String getAccountNumberExtension0() {
        return accountNumberExtension0;
    }

    public void setAccountNumberExtension0(String accountNumberExtension0) {
        this.accountNumberExtension0 = accountNumberExtension0;
    }
    public String getAcquirerReferenceNumber0() {
        return acquirerReferenceNumber0;
    }

    public void setAcquirerReferenceNumber0(String acquirerReferenceNumber0) {
        this.acquirerReferenceNumber0 = acquirerReferenceNumber0;
    }
    public String getPurchaseDate0() {
        return purchaseDate0;
    }

    public void setPurchaseDate0(String purchaseDate0) {
        this.purchaseDate0 = purchaseDate0;
    }
    public String getSourceAmount0() {
        return sourceAmount0;
    }

    public void setSourceAmount0(String sourceAmount0) {
        this.sourceAmount0 = sourceAmount0;
    }
    public String getSourceCurrencyCode0() {
        return sourceCurrencyCode0;
    }

    public void setSourceCurrencyCode0(String sourceCurrencyCode0) {
        this.sourceCurrencyCode0 = sourceCurrencyCode0;
    }
    public String getMerchantName0() {
        return merchantName0;
    }

    public void setMerchantName0(String merchantName0) {
        this.merchantName0 = merchantName0;
    }
    public String getMerchantCity0() {
        return merchantCity0;
    }

    public void setMerchantCity0(String merchantCity0) {
        this.merchantCity0 = merchantCity0;
    }
    public String getMerchantCountryCode0() {
        return merchantCountryCode0;
    }

    public void setMerchantCountryCode0(String merchantCountryCode0) {
        this.merchantCountryCode0 = merchantCountryCode0;
    }
    public String getMerchantCategoryCode0() {
        return merchantCategoryCode0;
    }

    public void setMerchantCategoryCode0(String merchantCategoryCode0) {
        this.merchantCategoryCode0 = merchantCategoryCode0;
    }
    public String getMerchantStateProvinceCode0() {
        return merchantStateProvinceCode0;
    }

    public void setMerchantStateProvinceCode0(String merchantStateProvinceCode0) {
        this.merchantStateProvinceCode0 = merchantStateProvinceCode0;
    }
    public String getMerchantZipCode0() {
        return merchantZipCode0;
    }

    public void setMerchantZipCode0(String merchantZipCode0) {
        this.merchantZipCode0 = merchantZipCode0;
    }
    public String getRequestedPaymentService0() {
        return requestedPaymentService0;
    }

    public void setRequestedPaymentService0(String requestedPaymentService0) {
        this.requestedPaymentService0 = requestedPaymentService0;
    }
    public String getAuthorizationCode0() {
        return authorizationCode0;
    }

    public void setAuthorizationCode0(String authorizationCode0) {
        this.authorizationCode0 = authorizationCode0;
    }
    public String getPosEntryMode0() {
        return posEntryMode0;
    }

    public void setPosEntryMode0(String posEntryMode0) {
        this.posEntryMode0 = posEntryMode0;
    }
    public String getCentralProcessingDate0() {
        return centralProcessingDate0;
    }

    public void setCentralProcessingDate0(String centralProcessingDate0) {
        this.centralProcessingDate0 = centralProcessingDate0;
    }
    public String getCardAcceptorId0() {
        return cardAcceptorId0;
    }

    public void setCardAcceptorId0(String cardAcceptorId0) {
        this.cardAcceptorId0 = cardAcceptorId0;
    }
    public String getReimbursementAttribute0() {
        return reimbursementAttribute0;
    }

    public void setReimbursementAttribute0(String reimbursementAttribute0) {
        this.reimbursementAttribute0 = reimbursementAttribute0;
    }
    public String getTransactionCodeQualifier1() {
        return transactionCodeQualifier1;
    }

    public void setTransactionCodeQualifier1(String transactionCodeQualifier1) {
        this.transactionCodeQualifier1 = transactionCodeQualifier1;
    }
    public String getTransactionComponentSequenceNumber1() {
        return transactionComponentSequenceNumber1;
    }

    public void setTransactionComponentSequenceNumber1(String transactionComponentSequenceNumber1) {
        this.transactionComponentSequenceNumber1 = transactionComponentSequenceNumber1;
    }
    public String getNetworkIdentificationCode1() {
        return networkIdentificationCode1;
    }

    public void setNetworkIdentificationCode1(String networkIdentificationCode1) {
        this.networkIdentificationCode1 = networkIdentificationCode1;
    }
    public String getDisputeCondition1() {
        return disputeCondition1;
    }

    public void setDisputeCondition1(String disputeCondition1) {
        this.disputeCondition1 = disputeCondition1;
    }
    public String getVrqlFinancialId1() {
        return vrqlFinancialId1;
    }

    public void setVrqlFinancialId1(String vrqlFinancialId1) {
        this.vrqlFinancialId1 = vrqlFinancialId1;
    }
    public String getVrqlCaseNumber1() {
        return vrqlCaseNumber1;
    }

    public void setVrqlCaseNumber1(String vrqlCaseNumber1) {
        this.vrqlCaseNumber1 = vrqlCaseNumber1;
    }
    public String getVrqlBundleCaseNumber1() {
        return vrqlBundleCaseNumber1;
    }

    public void setVrqlBundleCaseNumber1(String vrqlBundleCaseNumber1) {
        this.vrqlBundleCaseNumber1 = vrqlBundleCaseNumber1;
    }
    public String getClientCaseNumber1() {
        return clientCaseNumber1;
    }

    public void setClientCaseNumber1(String clientCaseNumber1) {
        this.clientCaseNumber1 = clientCaseNumber1;
    }
    public String getReserved11() {
        return reserved11;
    }

    public void setReserved11(String reserved11) {
        this.reserved11 = reserved11;
    }
    public String getMultipleClearingSequenceNumber1() {
        return multipleClearingSequenceNumber1;
    }

    public void setMultipleClearingSequenceNumber1(String multipleClearingSequenceNumber1) {
        this.multipleClearingSequenceNumber1 = multipleClearingSequenceNumber1;
    }
    public String getMultipleClearingSequenceCount1() {
        return multipleClearingSequenceCount1;
    }

    public void setMultipleClearingSequenceCount1(String multipleClearingSequenceCount1) {
        this.multipleClearingSequenceCount1 = multipleClearingSequenceCount1;
    }
    public String getProductId1() {
        return productId1;
    }

    public void setProductId1(String productId1) {
        this.productId1 = productId1;
    }
    public String getSpendQualifiedIndicator1() {
        return spendQualifiedIndicator1;
    }

    public void setSpendQualifiedIndicator1(String spendQualifiedIndicator1) {
        this.spendQualifiedIndicator1 = spendQualifiedIndicator1;
    }
    public String getDisputeFinancialReasonCode1() {
        return disputeFinancialReasonCode1;
    }

    public void setDisputeFinancialReasonCode1(String disputeFinancialReasonCode1) {
        this.disputeFinancialReasonCode1 = disputeFinancialReasonCode1;
    }
    public String getSettlementFlag1() {
        return settlementFlag1;
    }

    public void setSettlementFlag1(String settlementFlag1) {
        this.settlementFlag1 = settlementFlag1;
    }
    public String getUsageCode1() {
        return usageCode1;
    }

    public void setUsageCode1(String usageCode1) {
        this.usageCode1 = usageCode1;
    }
    public String getTransactionIdentifier1() {
        return transactionIdentifier1;
    }

    public void setTransactionIdentifier1(String transactionIdentifier1) {
        this.transactionIdentifier1 = transactionIdentifier1;
    }
    public String getAcquirersBusinessId1() {
        return acquirersBusinessId1;
    }

    public void setAcquirersBusinessId1(String acquirersBusinessId1) {
        this.acquirersBusinessId1 = acquirersBusinessId1;
    }
    public String getOriginalTransactionAmount1() {
        return originalTransactionAmount1;
    }

    public void setOriginalTransactionAmount1(String originalTransactionAmount1) {
        this.originalTransactionAmount1 = originalTransactionAmount1;
    }
    public String getOriginalTransactionCurrencyCode1() {
        return originalTransactionCurrencyCode1;
    }

    public void setOriginalTransactionCurrencyCode1(String originalTransactionCurrencyCode1) {
        this.originalTransactionCurrencyCode1 = originalTransactionCurrencyCode1;
    }
    public String getSpecialChargebackIndicator1() {
        return specialChargebackIndicator1;
    }

    public void setSpecialChargebackIndicator1(String specialChargebackIndicator1) {
        this.specialChargebackIndicator1 = specialChargebackIndicator1;
    }
    public String getDestinationSourceSettlementAmount1() {
        return destinationSourceSettlementAmount1;
    }

    public void setDestinationSourceSettlementAmount1(String destinationSourceSettlementAmount1) {
        this.destinationSourceSettlementAmount1 = destinationSourceSettlementAmount1;
    }
    public String getDestinationSourceSettlementCurrency1() {
        return destinationSourceSettlementCurrency1;
    }

    public void setDestinationSourceSettlementCurrency1(String destinationSourceSettlementCurrency1) {
        this.destinationSourceSettlementCurrency1 = destinationSourceSettlementCurrency1;
    }
    public String getSourceSettlementAmountSign1() {
        return sourceSettlementAmountSign1;
    }

    public void setSourceSettlementAmountSign1(String sourceSettlementAmountSign1) {
        this.sourceSettlementAmountSign1 = sourceSettlementAmountSign1;
    }
    public String getRateTableId1() {
        return rateTableId1;
    }

    public void setRateTableId1(String rateTableId1) {
        this.rateTableId1 = rateTableId1;
    }
    public String getReserved21() {
        return reserved21;
    }

    public void setReserved21(String reserved21) {
        this.reserved21 = reserved21;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "VisaBase33Data{" +
            "id=" + id +
            ", fileName=" + fileName +
            ", transactionCode=" + transactionCode +
            ", transactionCodeQualifier0=" + transactionCodeQualifier0 +
            ", transactionComponentSequenceNumber0=" + transactionComponentSequenceNumber0 +
            ", destinationIdentifier0=" + destinationIdentifier0 +
            ", sourceIdentifier0=" + sourceIdentifier0 +
            ", vcrRecordIdentifier0=" + vcrRecordIdentifier0 +
            ", disputeStatus0=" + disputeStatus0 +
            ", disputeTransactionCode0=" + disputeTransactionCode0 +
            ", disputeTransactionCodeQualifier0=" + disputeTransactionCodeQualifier0 +
            ", originatorRecipientIndicator0=" + originatorRecipientIndicator0 +
            ", accountNumber0=" + accountNumber0 +
            ", accountNumberExtension0=" + accountNumberExtension0 +
            ", acquirerReferenceNumber0=" + acquirerReferenceNumber0 +
            ", purchaseDate0=" + purchaseDate0 +
            ", sourceAmount0=" + sourceAmount0 +
            ", sourceCurrencyCode0=" + sourceCurrencyCode0 +
            ", merchantName0=" + merchantName0 +
            ", merchantCity0=" + merchantCity0 +
            ", merchantCountryCode0=" + merchantCountryCode0 +
            ", merchantCategoryCode0=" + merchantCategoryCode0 +
            ", merchantStateProvinceCode0=" + merchantStateProvinceCode0 +
            ", merchantZipCode0=" + merchantZipCode0 +
            ", requestedPaymentService0=" + requestedPaymentService0 +
            ", authorizationCode0=" + authorizationCode0 +
            ", posEntryMode0=" + posEntryMode0 +
            ", centralProcessingDate0=" + centralProcessingDate0 +
            ", cardAcceptorId0=" + cardAcceptorId0 +
            ", reimbursementAttribute0=" + reimbursementAttribute0 +
            ", transactionCodeQualifier1=" + transactionCodeQualifier1 +
            ", transactionComponentSequenceNumber1=" + transactionComponentSequenceNumber1 +
            ", networkIdentificationCode1=" + networkIdentificationCode1 +
            ", disputeCondition1=" + disputeCondition1 +
            ", vrqlFinancialId1=" + vrqlFinancialId1 +
            ", vrqlCaseNumber1=" + vrqlCaseNumber1 +
            ", vrqlBundleCaseNumber1=" + vrqlBundleCaseNumber1 +
            ", clientCaseNumber1=" + clientCaseNumber1 +
            ", reserved11=" + reserved11 +
            ", multipleClearingSequenceNumber1=" + multipleClearingSequenceNumber1 +
            ", multipleClearingSequenceCount1=" + multipleClearingSequenceCount1 +
            ", productId1=" + productId1 +
            ", spendQualifiedIndicator1=" + spendQualifiedIndicator1 +
            ", disputeFinancialReasonCode1=" + disputeFinancialReasonCode1 +
            ", settlementFlag1=" + settlementFlag1 +
            ", usageCode1=" + usageCode1 +
            ", transactionIdentifier1=" + transactionIdentifier1 +
            ", acquirersBusinessId1=" + acquirersBusinessId1 +
            ", originalTransactionAmount1=" + originalTransactionAmount1 +
            ", originalTransactionCurrencyCode1=" + originalTransactionCurrencyCode1 +
            ", specialChargebackIndicator1=" + specialChargebackIndicator1 +
            ", destinationSourceSettlementAmount1=" + destinationSourceSettlementAmount1 +
            ", destinationSourceSettlementCurrency1=" + destinationSourceSettlementCurrency1 +
            ", sourceSettlementAmountSign1=" + sourceSettlementAmountSign1 +
            ", rateTableId1=" + rateTableId1 +
            ", reserved21=" + reserved21 +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
        "}";
    }
}

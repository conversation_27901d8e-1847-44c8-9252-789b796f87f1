package com.kun.linkage.common.db.config;

import com.baomidou.mybatisplus.generator.config.ITypeConvert;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;

public class CustomTypeConvert implements ITypeConvert {
    @Override
    public DbColumnType processTypeConvert(GlobalConfig globalConfig, String fieldType) {
        String t = fieldType.toLowerCase();
        if (t.contains("tinyint")) {
            return DbColumnType.INTEGER;
        }
        if (t.contains("smallint")) {
            return DbColumnType.INTEGER;
        }
        if (t.contains("mediumint")) {
            return DbColumnType.INTEGER;
        }
        if (t.contains("int")) {
            return DbColumnType.INTEGER;
        }
        if (t.contains("bigint")) {
            return DbColumnType.LONG;
        }
        if (t.contains("float")) {
            return DbColumnType.FLOAT;
        }
        if (t.contains("double")) {
            return DbColumnType.DOUBLE;
        }
        if (t.contains("decimal")) {
            return DbColumnType.BIG_DECIMAL;
        }
        if (t.contains("char") || t.contains("text") || t.contains("json") || t.contains("enum")) {
            return DbColumnType.STRING;
        }
        if (t.contains("datetime") || t.contains("timestamp")) {
            return DbColumnType.DATE;
        }
        if (t.contains("date")) {
            return DbColumnType.DATE;
        }
        if (t.contains("time")) {
            return DbColumnType.TIME;
        }
        if (t.contains("bit")) {
            return DbColumnType.BOOLEAN;
        }
        if (t.contains("blob")) {
            return DbColumnType.BYTE_ARRAY;
        }
        return DbColumnType.STRING;
    }
} 
package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@TableName("vcc_sms_template")
public class SmsTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * template_no
     */
    private String templateNo;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板类型；3DS;交易动账通知
     */
    private String templateType;

    /**
     * 模板语言; 默认 'EN'，支持 'EN', 'CN'
     */
    private String templateLanguage;

    /**
     * 模板内容
     */
    private String templateContent;
    /**
     * 模板内容key
     */
    private String templateContentKey;

    /**
     * 模板状态;ACTIVE;INACTIVE
     */
    private String status;
    /**
     * 是否是模板类短信;0:否;1:是
     */
    private Integer templateSmsFlag;

    /**
     * 创建用户id
     */
    private Long createUserId;

    /**
     * 更新用户id
     */
    private Long updateUserId;

    private Long id;

    private LocalDateTime createDate;

    private LocalDateTime updateDate;

    public String getTemplateNo() {
        return templateNo;
    }

    public void setTemplateNo(String templateNo) {
        this.templateNo = templateNo;
    }
    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getTemplateType() {
        return templateType;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }
    public String getTemplateLanguage() {
        return templateLanguage;
    }

    public void setTemplateLanguage(String templateLanguage) {
        this.templateLanguage = templateLanguage;
    }
    public String getTemplateContent() {
        return templateContent;
    }

    public void setTemplateContent(String templateContent) {
        this.templateContent = templateContent;
    }

    public String getTemplateContentKey() {
        return templateContentKey;
    }

    public void setTemplateContentKey(String templateContentKey) {
        this.templateContentKey = templateContentKey;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getTemplateSmsFlag() {
        return templateSmsFlag;
    }

    public void setTemplateSmsFlag(Integer templateSmsFlag) {
        this.templateSmsFlag = templateSmsFlag;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }
    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }
    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "VccSmsTemplate{" +
            "templateNo=" + templateNo +
            ", templateName=" + templateName +
            ", templateType=" + templateType +
            ", templateLanguage=" + templateLanguage +
            ", templateContent=" + templateContent +
            ", templateContentKey=" + templateContentKey +
            ", status=" + status +
            ", createUserId=" + createUserId +
            ", updateUserId=" + updateUserId +
            ", id=" + id +
            ", createDate=" + createDate +
            ", updateDate=" + updateDate +
        "}";
    }
}

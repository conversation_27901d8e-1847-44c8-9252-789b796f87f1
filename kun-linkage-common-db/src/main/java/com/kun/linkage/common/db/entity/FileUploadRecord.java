package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 文件上传记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@TableName("kl_file_upload_record")
public class FileUploadRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件唯一标识
     */
    @TableId(value = "file_id",type = IdType.ASSIGN_ID)
    private String fileId;

    /**
     * 客户ID
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 机构号
     */
    @TableField("organization_no")
    private String organizationNo;

    /**
     * 原始文件名
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 文件类型（如PDF、JPG）
     */
    @TableField("file_type")
    private String fileType;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 文件存储路径
     */
    @TableField("storage_path")
    private String storagePath;

    /**
     * 文件保留期限（天）
     */
    @TableField("retention_period")
    private Integer retentionPeriod;

    /**
     * 文件过期时间
     */
    @TableField("expires_at")
    private LocalDateTime expiresAt;

    /**
     * 上传时间
     */
    @TableField("upload_time")
    private LocalDateTime uploadTime;

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getStoragePath() {
        return storagePath;
    }

    public void setStoragePath(String storagePath) {
        this.storagePath = storagePath;
    }

    public Integer getRetentionPeriod() {
        return retentionPeriod;
    }

    public void setRetentionPeriod(Integer retentionPeriod) {
        this.retentionPeriod = retentionPeriod;
    }

    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }

    public LocalDateTime getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(LocalDateTime uploadTime) {
        this.uploadTime = uploadTime;
    }

    @Override
    public String toString() {
        return "FileUploadRecord{" +
            "fileId=" + fileId +
            ", customerId=" + customerId +
            ", organizationNo=" + organizationNo +
            ", fileName=" + fileName +
            ", fileType=" + fileType +
            ", fileSize=" + fileSize +
            ", storagePath=" + storagePath +
            ", retentionPeriod=" + retentionPeriod +
            ", expiresAt=" + expiresAt +
            ", uploadTime=" + uploadTime +
        "}";
    }
}

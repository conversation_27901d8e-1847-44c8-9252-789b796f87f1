package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 机构费用明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@TableName("kl_organization_fee_detail")
public class OrganizationFeeDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId("id")
    private String id;

    /**
     * 机构号
     */
    @TableField("organization_no")
    private String organizationNo;

    /**
     * 卡产品编码
     */
    @TableField("card_product_code")
    private String cardProductCode;

    /**
     * 手续费计算日期时间
     */
    @TableField("calculate_datetime")
    private LocalDateTime calculateDatetime;

    /**
     * 交易日期时间
     */
    @TableField("transaction_datetime")
    private LocalDateTime transactionDatetime;

    /**
     * 关联交易id
     */
    @TableField("related_transaction_id")
    private String relatedTransactionId;

    /**
     * 手续费类型
     */
    @TableField("fee_type")
    private String feeType;

    /**
     * 手续费收取方式
     */
    @TableField("fee_collection_method")
    private String feeCollectionMethod;

    /**
     * 交易金额
     */
    @TableField("transaction_amount")
    private BigDecimal transactionAmount;

    /**
     * 交易币种
     */
    @TableField("transaction_currency_code")
    private String transactionCurrencyCode;

    /**
     * 交易币种精度
     */
    @TableField("transaction_currency_precision")
    private Integer transactionCurrencyPrecision;

    /**
     * 手续费金额(币种同交易币种)
     */
    @TableField("fee_amount")
    private BigDecimal feeAmount;

    /**
     * 换汇汇率
     */
    @TableField("fx_rate")
    private BigDecimal fxRate;

    /**
     * 扣除处理方(KUN和PAYX)
     */
    @TableField("deduct_processor")
    private String deductProcessor;

    /**
     * 扣除币种
     */
    @TableField("deduct_currency_code")
    private String deductCurrencyCode;

    /**
     * 扣除币种精度
     */
    @TableField("deduct_currency_precision")
    private Integer deductCurrencyPrecision;

    /**
     * 扣除的费用金额
     */
    @TableField("deduct_fee_amount")
    private BigDecimal deductFeeAmount;

    /**
     * 扣除时的请求流水号(kun或payx中的requestNo)
     */
    @TableField("deduct_request_no")
    private String deductRequestNo;

    /**
     * 送到kun/payx的remark数据，后续用来对账使用
     */
    @TableField("remark")
    private String remark;

    /**
     * 手续费收取状态;0:未收;1:已收;
     */
    @TableField("fee_collection_status")
    private Integer feeCollectionStatus;

    /**
     * 快照-计费维度
     */
    @TableField("snapshot_billing_dimension")
    private String snapshotBillingDimension;

    /**
     * 快照-金额区间:最小金额
     */
    @TableField("snapshot_min_amount")
    private BigDecimal snapshotMinAmount;

    /**
     * 快照-金额区间:最大金额
     */
    @TableField("snapshot_max_amount")
    private BigDecimal snapshotMaxAmount;

    /**
     * 快照-比例
     */
    @TableField("snapshot_proportion_rate")
    private BigDecimal snapshotProportionRate;

    /**
     * 快照-比例的保底金额
     */
    @TableField("snapshot_proportion_min_amount")
    private BigDecimal snapshotProportionMinAmount;

    /**
     * 快照-比例的封顶金额
     */
    @TableField("snapshot_proportion_max_amount")
    private BigDecimal snapshotProportionMaxAmount;

    /**
     * 快照-固定值
     */
    @TableField("snapshot_fixed_amount")
    private BigDecimal snapshotFixedAmount;

    /**
     * 调用次数
     */
    @TableField("call_count")
    private Integer callCount;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 最后一次修改时间
     */
    @TableField("last_modify_time")
    private LocalDateTime lastModifyTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }
    public LocalDateTime getCalculateDatetime() {
        return calculateDatetime;
    }

    public void setCalculateDatetime(LocalDateTime calculateDatetime) {
        this.calculateDatetime = calculateDatetime;
    }
    public LocalDateTime getTransactionDatetime() {
        return transactionDatetime;
    }

    public void setTransactionDatetime(LocalDateTime transactionDatetime) {
        this.transactionDatetime = transactionDatetime;
    }
    public String getRelatedTransactionId() {
        return relatedTransactionId;
    }

    public void setRelatedTransactionId(String relatedTransactionId) {
        this.relatedTransactionId = relatedTransactionId;
    }
    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }
    public String getFeeCollectionMethod() {
        return feeCollectionMethod;
    }

    public void setFeeCollectionMethod(String feeCollectionMethod) {
        this.feeCollectionMethod = feeCollectionMethod;
    }
    public BigDecimal getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(BigDecimal transactionAmount) {
        this.transactionAmount = transactionAmount;
    }
    public String getTransactionCurrencyCode() {
        return transactionCurrencyCode;
    }

    public void setTransactionCurrencyCode(String transactionCurrencyCode) {
        this.transactionCurrencyCode = transactionCurrencyCode;
    }
    public Integer getTransactionCurrencyPrecision() {
        return transactionCurrencyPrecision;
    }

    public void setTransactionCurrencyPrecision(Integer transactionCurrencyPrecision) {
        this.transactionCurrencyPrecision = transactionCurrencyPrecision;
    }
    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }
    public BigDecimal getFxRate() {
        return fxRate;
    }

    public void setFxRate(BigDecimal fxRate) {
        this.fxRate = fxRate;
    }
    public String getDeductProcessor() {
        return deductProcessor;
    }

    public void setDeductProcessor(String deductProcessor) {
        this.deductProcessor = deductProcessor;
    }
    public String getDeductCurrencyCode() {
        return deductCurrencyCode;
    }

    public void setDeductCurrencyCode(String deductCurrencyCode) {
        this.deductCurrencyCode = deductCurrencyCode;
    }
    public Integer getDeductCurrencyPrecision() {
        return deductCurrencyPrecision;
    }

    public void setDeductCurrencyPrecision(Integer deductCurrencyPrecision) {
        this.deductCurrencyPrecision = deductCurrencyPrecision;
    }
    public BigDecimal getDeductFeeAmount() {
        return deductFeeAmount;
    }

    public void setDeductFeeAmount(BigDecimal deductFeeAmount) {
        this.deductFeeAmount = deductFeeAmount;
    }
    public String getDeductRequestNo() {
        return deductRequestNo;
    }

    public void setDeductRequestNo(String deductRequestNo) {
        this.deductRequestNo = deductRequestNo;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public Integer getFeeCollectionStatus() {
        return feeCollectionStatus;
    }

    public void setFeeCollectionStatus(Integer feeCollectionStatus) {
        this.feeCollectionStatus = feeCollectionStatus;
    }
    public String getSnapshotBillingDimension() {
        return snapshotBillingDimension;
    }

    public void setSnapshotBillingDimension(String snapshotBillingDimension) {
        this.snapshotBillingDimension = snapshotBillingDimension;
    }
    public BigDecimal getSnapshotMinAmount() {
        return snapshotMinAmount;
    }

    public void setSnapshotMinAmount(BigDecimal snapshotMinAmount) {
        this.snapshotMinAmount = snapshotMinAmount;
    }
    public BigDecimal getSnapshotMaxAmount() {
        return snapshotMaxAmount;
    }

    public void setSnapshotMaxAmount(BigDecimal snapshotMaxAmount) {
        this.snapshotMaxAmount = snapshotMaxAmount;
    }
    public BigDecimal getSnapshotProportionRate() {
        return snapshotProportionRate;
    }

    public void setSnapshotProportionRate(BigDecimal snapshotProportionRate) {
        this.snapshotProportionRate = snapshotProportionRate;
    }
    public BigDecimal getSnapshotProportionMinAmount() {
        return snapshotProportionMinAmount;
    }

    public void setSnapshotProportionMinAmount(BigDecimal snapshotProportionMinAmount) {
        this.snapshotProportionMinAmount = snapshotProportionMinAmount;
    }
    public BigDecimal getSnapshotProportionMaxAmount() {
        return snapshotProportionMaxAmount;
    }

    public void setSnapshotProportionMaxAmount(BigDecimal snapshotProportionMaxAmount) {
        this.snapshotProportionMaxAmount = snapshotProportionMaxAmount;
    }
    public BigDecimal getSnapshotFixedAmount() {
        return snapshotFixedAmount;
    }

    public void setSnapshotFixedAmount(BigDecimal snapshotFixedAmount) {
        this.snapshotFixedAmount = snapshotFixedAmount;
    }

    public Integer getCallCount() {
        return callCount;
    }

    public void setCallCount(Integer callCount) {
        this.callCount = callCount;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    @Override
    public String toString() {
        return "OrganizationFeeDetail{" +
            "id=" + id +
            ", organizationNo=" + organizationNo +
            ", cardProductCode=" + cardProductCode +
            ", calculateDatetime=" + calculateDatetime +
            ", transactionDatetime=" + transactionDatetime +
            ", relatedTransactionId=" + relatedTransactionId +
            ", feeType=" + feeType +
            ", feeCollectionMethod=" + feeCollectionMethod +
            ", transactionAmount=" + transactionAmount +
            ", transactionCurrencyCode=" + transactionCurrencyCode +
            ", transactionCurrencyPrecision=" + transactionCurrencyPrecision +
            ", feeAmount=" + feeAmount +
            ", fxRate=" + fxRate +
            ", deductProcessor=" + deductProcessor +
            ", deductCurrencyCode=" + deductCurrencyCode +
            ", deductCurrencyPrecision=" + deductCurrencyPrecision +
            ", deductFeeAmount=" + deductFeeAmount +
            ", deductRequestNo=" + deductRequestNo +
            ", remark=" + remark +
            ", feeCollectionStatus=" + feeCollectionStatus +
            ", snapshotBillingDimension=" + snapshotBillingDimension +
            ", snapshotMinAmount=" + snapshotMinAmount +
            ", snapshotMaxAmount=" + snapshotMaxAmount +
            ", snapshotProportionRate=" + snapshotProportionRate +
            ", snapshotProportionMinAmount=" + snapshotProportionMinAmount +
            ", snapshotProportionMaxAmount=" + snapshotProportionMaxAmount +
            ", snapshotFixedAmount=" + snapshotFixedAmount +
            ", callCount=" + callCount +
            ", createTime=" + createTime +
            ", lastModifyTime=" + lastModifyTime +
        "}";
    }
}

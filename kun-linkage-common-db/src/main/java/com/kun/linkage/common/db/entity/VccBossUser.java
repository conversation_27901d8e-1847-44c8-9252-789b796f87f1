package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@TableName("vcc_boss_user")
public class VccBossUser implements Serializable {

    private static final long serialVersionUID = 1L;

    private String username;

    private String account;

    private String password;

    private Integer status;

    @TableId
    private Long id;

    private Date createDate;

    private Date updateDate;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "BossUser{" +
            "username=" + username +
            ", account=" + account +
            ", password=" + password +
            ", status=" + status +
            ", id=" + id +
            ", createDate=" + createDate +
            ", updateDate=" + updateDate +
        "}";
    }
}

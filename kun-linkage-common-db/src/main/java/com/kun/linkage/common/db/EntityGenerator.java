package com.kun.linkage.common.db;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.kun.linkage.common.db.config.CustomTypeConvert;

public class EntityGenerator {
    public static void main(String[] args) {
        // 获取当前Git用户的用户名
        String gitUser = "";
        try {
            Process process = Runtime.getRuntime().exec("git config user.name");
            java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.InputStreamReader(process.getInputStream()));
            gitUser = reader.readLine();
            reader.close();
        } catch (Exception e) {
            gitUser = "default_author";
        }
        String finalGitUser = gitUser;

        // 创建数据源配置
        DataSourceConfig.Builder dataSourceConfigBuilder = new DataSourceConfig
                .Builder("********************************************************************************",
                        "kcard_dev", "kcard_devABC123!")
                .typeConvert(new CustomTypeConvert());

        FastAutoGenerator.create(dataSourceConfigBuilder)
                .globalConfig(builder -> {
                    builder.author(finalGitUser)
                            .outputDir(System.getProperty("user.dir") +
                                    "/kun-linkage-common/kun-linkage-common-db/src/main/java")
                            .fileOverride()// 覆盖已存在的文件
                            .disableOpenDir() // 禁止打开输出目录
                            // 设置生成的实体类日期类型
                            .dateType(DateType.ONLY_DATE);
                })
                .packageConfig(builder -> {
                    builder.parent("com.kun.linkage.common.db")
                            .entity("entity")
                            .mapper("mapper");
                })
                .strategyConfig(builder -> {
                    builder.addInclude("kl_organization_trans_accounting_202507")
                            // 过滤表前缀（如果表名有KL前缀）
                            .addTablePrefix("KL_","kl_", "common_")
                            .addTableSuffix("_202507") // 过滤表后缀（如果表名有_202505后缀）
                        .entityBuilder()// 实体类配置
                        .enableTableFieldAnnotation() // 生成字段注解
//                        .formatFileName("KL%s") // 实体类文件名格式
                        .fileOverride() // 覆盖已存在的文件
//                        .mapperBuilder().formatMapperFileName("KL%sMapper") // Mapper文件名格式
                    ;
                })
                .templateConfig(builder -> {
                    // 设置 Controller模板为空，即不生成对应的代码
                    builder.controller("")
                            .service("")
                            .serviceImpl("")
                            .xml("");
                })
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();
    }
}

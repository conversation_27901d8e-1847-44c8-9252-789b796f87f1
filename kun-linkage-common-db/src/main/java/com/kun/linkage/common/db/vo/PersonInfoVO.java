package com.kun.linkage.common.db.vo;

import java.io.Serializable;

public class PersonInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;
    private Integer index;
    private String gender;
    private String idCard;
    private String nameEN;
    private String country;
    private String nameCHS;
    private String surname;
    private String authType;
    private String birthday;
    private Boolean asDirector;
    private String surnameCHS;
    private String authTypeDesc;
    private String contactsEmail;
    private String certificateTerm;
    private String certificateTermStart;
    private String certificateTermEnd;
    private String longTime;
    private boolean asAccountManager;
    private String residenceAddress;
    private String residenceCountry;
    private String shareholdingRatio;
    private IdentityVerification identityVerification;

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getNameEN() {
        return nameEN;
    }

    public void setNameEN(String nameEN) {
        this.nameEN = nameEN;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getNameCHS() {
        return nameCHS;
    }

    public void setNameCHS(String nameCHS) {
        this.nameCHS = nameCHS;
    }

    public String getSurname() {
        return surname;
    }

    public void setSurname(String surname) {
        this.surname = surname;
    }

    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public Boolean getAsDirector() {
        return asDirector;
    }

    public void setAsDirector(Boolean asDirector) {
        this.asDirector = asDirector;
    }

    public String getSurnameCHS() {
        return surnameCHS;
    }

    public void setSurnameCHS(String surnameCHS) {
        this.surnameCHS = surnameCHS;
    }

    public String getAuthTypeDesc() {
        return authTypeDesc;
    }

    public void setAuthTypeDesc(String authTypeDesc) {
        this.authTypeDesc = authTypeDesc;
    }

    public String getContactsEmail() {
        return contactsEmail;
    }

    public void setContactsEmail(String contactsEmail) {
        this.contactsEmail = contactsEmail;
    }

    public String getCertificateTerm() {
        return certificateTerm;
    }

    public void setCertificateTerm(String certificateTerm) {
        this.certificateTerm = certificateTerm;
    }

    public String getCertificateTermStart() {
        return certificateTermStart;
    }

    public void setCertificateTermStart(String certificateTermStart) {
        this.certificateTermStart = certificateTermStart;
    }

    public String getCertificateTermEnd() {
        return certificateTermEnd;
    }

    public void setCertificateTermEnd(String certificateTermEnd) {
        this.certificateTermEnd = certificateTermEnd;
    }

    public String getLongTime() {
        return longTime;
    }

    public void setLongTime(String longTime) {
        this.longTime = longTime;
    }

    public boolean isAsAccountManager() {
        return asAccountManager;
    }

    public void setAsAccountManager(boolean asAccountManager) {
        this.asAccountManager = asAccountManager;
    }

    public String getResidenceAddress() {
        return residenceAddress;
    }

    public void setResidenceAddress(String residenceAddress) {
        this.residenceAddress = residenceAddress;
    }

    public String getResidenceCountry() {
        return residenceCountry;
    }

    public void setResidenceCountry(String residenceCountry) {
        this.residenceCountry = residenceCountry;
    }

    public String getShareholdingRatio() {
        return shareholdingRatio;
    }

    public void setShareholdingRatio(String shareholdingRatio) {
        this.shareholdingRatio = shareholdingRatio;
    }

    public IdentityVerification getIdentityVerification() {
        return identityVerification;
    }

    public void setIdentityVerification(IdentityVerification identityVerification) {
        this.identityVerification = identityVerification;
    }
}

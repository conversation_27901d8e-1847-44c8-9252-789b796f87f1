package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 机构基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@TableName("kl_organization_basic_info")
public class OrganizationBasicInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("organization_no")
    private String organizationNo;

    /**
     * 机构名称
     */
    @TableField("organization_name")
    private String organizationName;

    /**
     * 业务类型
     */
    @TableField("business_type")
    private String businessType;

    /**
     * 国家字母码
     */
    @TableField("country_code")
    private String countryCode;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * key
     */
    @TableField("`key`")
    private String key;

    /**
     * 敏感信息加解密key
     */
    @TableField("sensitive_key")
    private String sensitiveKey;

    /**
     * kun对应的商户号
     */
    @TableField("kun_mid")
    private String kunMid;

    /**
     * payx对应的商户号
     */
    @TableField("payx_mid")
    private String payxMid;

    @TableField("mode")
    private String mode;

    /**
     * 是否校验机构账户标记;0否;1是
     */
    @TableField("check_organization_account_flag")
    private Integer checkOrganizationAccountFlag;

    /**
     * 是否校验机构下用户账户标记;0否;1是
     */
    @TableField("check_customer_account_flag")
    private Integer checkCustomerAccountFlag;

    /**
     * 是否个人客户kyc报送;0否;1:是
     */
    @TableField("is_kyc_reported")
    private Integer isKycReported;

    /**
     * 是否个人客户kyc校验;0否;1:是
     */
    @TableField("is_kyc_verified")
    private Integer isKycVerified;

    /**
     * 第三方授权标记
     */
    @TableField("third_party_authorization_flag")
    private Integer thirdPartyAuthorizationFlag;

    /**
     * MPC租户ID
     */
    @TableField("mpc_tenant_id")
    private String mpcTenantId;

    /**
     * MPC集团号
     */
    @TableField("mpc_group_code")
    private String mpcGroupCode;

    /**
     * MPC TOKEN
     */
    @TableField("mpc_token")
    private String mpcToken;

    /**
     * 资金池币种
     */
    @TableField("pool_currency_code")
    private String poolCurrencyCode;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 创建人名称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 最后一次修改时间
     */
    @TableField("last_modify_time")
    private LocalDateTime lastModifyTime;

    /**
     * 最后一次修改人id
     */
    @TableField("last_modify_user_id")
    private String lastModifyUserId;

    /**
     * 最后一次修改人名称
     */
    @TableField("last_modify_user_name")
    private String lastModifyUserName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }
    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
    public String getSensitiveKey() {
        return sensitiveKey;
    }

    public void setSensitiveKey(String sensitiveKey) {
        this.sensitiveKey = sensitiveKey;
    }
    public String getKunMid() {
        return kunMid;
    }

    public void setKunMid(String kunMid) {
        this.kunMid = kunMid;
    }
    public String getPayxMid() {
        return payxMid;
    }

    public void setPayxMid(String payxMid) {
        this.payxMid = payxMid;
    }
    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }
    public Integer getCheckOrganizationAccountFlag() {
        return checkOrganizationAccountFlag;
    }

    public void setCheckOrganizationAccountFlag(Integer checkOrganizationAccountFlag) {
        this.checkOrganizationAccountFlag = checkOrganizationAccountFlag;
    }
    public Integer getCheckCustomerAccountFlag() {
        return checkCustomerAccountFlag;
    }

    public void setCheckCustomerAccountFlag(Integer checkCustomerAccountFlag) {
        this.checkCustomerAccountFlag = checkCustomerAccountFlag;
    }
    public Integer getIsKycReported() {
        return isKycReported;
    }

    public void setIsKycReported(Integer isKycReported) {
        this.isKycReported = isKycReported;
    }
    public Integer getIsKycVerified() {
        return isKycVerified;
    }

    public void setIsKycVerified(Integer isKycVerified) {
        this.isKycVerified = isKycVerified;
    }
    public Integer getThirdPartyAuthorizationFlag() {
        return thirdPartyAuthorizationFlag;
    }

    public void setThirdPartyAuthorizationFlag(Integer thirdPartyAuthorizationFlag) {
        this.thirdPartyAuthorizationFlag = thirdPartyAuthorizationFlag;
    }
    public String getMpcTenantId() {
        return mpcTenantId;
    }

    public void setMpcTenantId(String mpcTenantId) {
        this.mpcTenantId = mpcTenantId;
    }
    public String getMpcGroupCode() {
        return mpcGroupCode;
    }

    public void setMpcGroupCode(String mpcGroupCode) {
        this.mpcGroupCode = mpcGroupCode;
    }
    public String getMpcToken() {
        return mpcToken;
    }

    public void setMpcToken(String mpcToken) {
        this.mpcToken = mpcToken;
    }
    public String getPoolCurrencyCode() {
        return poolCurrencyCode;
    }

    public void setPoolCurrencyCode(String poolCurrencyCode) {
        this.poolCurrencyCode = poolCurrencyCode;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }
    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }
    public String getLastModifyUserId() {
        return lastModifyUserId;
    }

    public void setLastModifyUserId(String lastModifyUserId) {
        this.lastModifyUserId = lastModifyUserId;
    }
    public String getLastModifyUserName() {
        return lastModifyUserName;
    }

    public void setLastModifyUserName(String lastModifyUserName) {
        this.lastModifyUserName = lastModifyUserName;
    }

    @Override
    public String toString() {
        return "OrganizationBasicInfo{" +
            "id=" + id +
            ", organizationNo=" + organizationNo +
            ", organizationName=" + organizationName +
            ", businessType=" + businessType +
            ", countryCode=" + countryCode +
            ", status=" + status +
            ", key=" + key +
            ", sensitiveKey=" + sensitiveKey +
            ", kunMid=" + kunMid +
            ", payxMid=" + payxMid +
            ", mode=" + mode +
            ", checkOrganizationAccountFlag=" + checkOrganizationAccountFlag +
            ", checkCustomerAccountFlag=" + checkCustomerAccountFlag +
            ", isKycReported=" + isKycReported +
            ", isKycVerified=" + isKycVerified +
            ", thirdPartyAuthorizationFlag=" + thirdPartyAuthorizationFlag +
            ", mpcTenantId=" + mpcTenantId +
            ", mpcGroupCode=" + mpcGroupCode +
            ", mpcToken=" + mpcToken +
            ", poolCurrencyCode=" + poolCurrencyCode +
            ", createTime=" + createTime +
            ", createUserId=" + createUserId +
            ", createUserName=" + createUserName +
            ", lastModifyTime=" + lastModifyTime +
            ", lastModifyUserId=" + lastModifyUserId +
            ", lastModifyUserName=" + lastModifyUserName +
        "}";
    }
}

package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@TableName("vcc_permission")
public class VccBossPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    private String code;

    private String name;

    @TableId
    private Long id;

    private Date createDate;

    private Date updateDate;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "Permission{" +
            "code=" + code +
            ", name=" + name +
            ", id=" + id +
            ", createDate=" + createDate +
            ", updateDate=" + updateDate +
        "}";
    }
}

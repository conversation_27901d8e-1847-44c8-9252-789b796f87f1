package com.kun.linkage.common.db.config;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.mapper.MapperScannerConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class MapperConfig {

    @Bean
    public static MapperScannerConfigurer mapperScannerConfigurer() {
        log.info("==============MapperScannerConfigurer==============");
        MapperScannerConfigurer configurer = new MapperScannerConfigurer();
        configurer.setBasePackage("com.kun.linkage.**.mapper");
        return configurer;
    }
}

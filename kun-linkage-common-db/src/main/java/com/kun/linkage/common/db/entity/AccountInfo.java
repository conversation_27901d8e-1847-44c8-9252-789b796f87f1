package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 基础账户信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@TableName("kl_account_info")
public class AccountInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账户号
     */
    @TableId
    private String accountNo;

    /**
     * 业务系统:KL/VCC
     */
    private String businessSystem;

    /**
     * 账户类型001:法币基本账户/002:数币基本账户/003:信用法币账户
     */
    private String accountType;

    /**
     * 币种码
     */
    private String currencyCode;

    /**
     * 币种精度
     */
    private Integer currencyPrecision;

    /**
     * 账户方向C/D
     */
    private String direction;

    /**
     * 账户状态
     */
    private String status;

    /**
     * 最小余额
     */
    private BigDecimal minimumAmount;

    /**
     * 总余额
     */
    private BigDecimal totalBalanceAmount;

    /**
     * 冻结金额
     */
    private BigDecimal frozenAmount;

    /**
     * 可用余额
     */
    private BigDecimal availableAmount;

    /**
     * 业务机构号(主要用于页面查询使用)
     */
    private String businessOrganizationNo;

    /**
     * 业务客户号(主要用于页面查询使用)
     */
    private String businessCustomerId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后一次修改时间
     */
    private LocalDateTime lastModifyTime;

    public String getBusinessSystem() {
        return businessSystem;
    }

    public void setBusinessSystem(String businessSystem) {
        this.businessSystem = businessSystem;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }
    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public Integer getCurrencyPrecision() {
        return currencyPrecision;
    }

    public void setCurrencyPrecision(Integer currencyPrecision) {
        this.currencyPrecision = currencyPrecision;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public BigDecimal getMinimumAmount() {
        return minimumAmount;
    }

    public void setMinimumAmount(BigDecimal minimumAmount) {
        this.minimumAmount = minimumAmount;
    }
    public BigDecimal getTotalBalanceAmount() {
        return totalBalanceAmount;
    }

    public void setTotalBalanceAmount(BigDecimal totalBalanceAmount) {
        this.totalBalanceAmount = totalBalanceAmount;
    }
    public BigDecimal getFrozenAmount() {
        return frozenAmount;
    }

    public void setFrozenAmount(BigDecimal frozenAmount) {
        this.frozenAmount = frozenAmount;
    }
    public BigDecimal getAvailableAmount() {
        return availableAmount;
    }

    public void setAvailableAmount(BigDecimal availableAmount) {
        this.availableAmount = availableAmount;
    }

    public String getBusinessOrganizationNo() {
        return businessOrganizationNo;
    }

    public void setBusinessOrganizationNo(String businessOrganizationNo) {
        this.businessOrganizationNo = businessOrganizationNo;
    }

    public String getBusinessCustomerId() {
        return businessCustomerId;
    }

    public void setBusinessCustomerId(String businessCustomerId) {
        this.businessCustomerId = businessCustomerId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    @Override
    public String toString() {
        return "AccountInfo{" +
                "accountNo='" + accountNo + '\'' +
                ", businessSystem='" + businessSystem + '\'' +
                ", accountType='" + accountType + '\'' +
                ", currencyCode='" + currencyCode + '\'' +
                ", direction='" + direction + '\'' +
                ", status='" + status + '\'' +
                ", minimumAmount=" + minimumAmount +
                ", totalBalanceAmount=" + totalBalanceAmount +
                ", frozenAmount=" + frozenAmount +
                ", availableAmount=" + availableAmount +
                ", businessOrganizationNo='" + businessOrganizationNo + '\'' +
                ", businessCustomerId='" + businessCustomerId + '\'' +
                ", createTime=" + createTime +
                ", lastModifyTime=" + lastModifyTime +
                '}';
    }
}

package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 机构用户卡信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@TableName("kl_organization_customer_card_info")
public class OrganizationCustomerCardInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 机构号
     */
    @TableField("organization_no")
    private String organizationNo;

    /**
     * 客户号
     */
    @TableField("customer_id")
    private String customerId;

    @TableField("card_scheme")
    private String cardScheme;

    /**
     * 卡产品编码
     */
    @TableField("card_product_code")
    private String cardProductCode;

    /**
     * 卡id
     */
    @TableField("card_id")
    private String cardId;

    /**
     * 加密卡号
     */
    @TableField("card_no")
    private String cardNo;

    /**
     * 掩码卡号
     */
    @TableField("masked_card_no")
    private String maskedCardNo;

    /**
     * 卡币种
     */
    @TableField("currency_code")
    private String currencyCode;

    /**
     * 卡片处理方
     */
    @TableField("processor")
    private String processor;

    /**
     * 卡片状态
     */
    @TableField("card_status")
    private String cardStatus;

    /**
     * 激活状态
     */
    @TableField("card_active_status")
    private String cardActiveStatus;

    /**
     * 手机区号
     */
    @TableField("mobile_phone_area")
    private String mobilePhoneArea;

    /**
     * 手机号
     */
    @TableField("mobile_phone")
    private String mobilePhone;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 持卡人名
     */
    @TableField("cardholder_first_name")
    private String cardholderFirstName;

    /**
     * 持卡人姓
     */
    @TableField("cardholder_last_name")
    private String cardholderLastName;

    /**
     * 开卡手续费费用明细记录id
     */
    @TableField("open_card_fee_detail_id")
    private String openCardFeeDetailId;

    /**
     * 销卡手续费费用明细记录id
     */
    @TableField("cancel_card_fee_detail_id")
    private String cancelCardFeeDetailId;

    /**
     * 销卡承兑费费用明细记录id
     */
    @TableField("cancel_card_acceptance_fee_detail_id")
    private String cancelCardAcceptanceFeeDetailId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 最后一次修改时间
     */
    @TableField("last_modify_time")
    private LocalDateTime lastModifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    public String getCardScheme() {
        return cardScheme;
    }

    public void setCardScheme(String cardScheme) {
        this.cardScheme = cardScheme;
    }
    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }
    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }
    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }
    public String getMaskedCardNo() {
        return maskedCardNo;
    }

    public void setMaskedCardNo(String maskedCardNo) {
        this.maskedCardNo = maskedCardNo;
    }
    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }
    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }
    public String getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(String cardStatus) {
        this.cardStatus = cardStatus;
    }
    public String getCardActiveStatus() {
        return cardActiveStatus;
    }

    public void setCardActiveStatus(String cardActiveStatus) {
        this.cardActiveStatus = cardActiveStatus;
    }
    public String getMobilePhoneArea() {
        return mobilePhoneArea;
    }

    public void setMobilePhoneArea(String mobilePhoneArea) {
        this.mobilePhoneArea = mobilePhoneArea;
    }
    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public String getCardholderFirstName() {
        return cardholderFirstName;
    }

    public void setCardholderFirstName(String cardholderFirstName) {
        this.cardholderFirstName = cardholderFirstName;
    }
    public String getCardholderLastName() {
        return cardholderLastName;
    }

    public void setCardholderLastName(String cardholderLastName) {
        this.cardholderLastName = cardholderLastName;
    }
    public String getOpenCardFeeDetailId() {
        return openCardFeeDetailId;
    }

    public void setOpenCardFeeDetailId(String openCardFeeDetailId) {
        this.openCardFeeDetailId = openCardFeeDetailId;
    }
    public String getCancelCardFeeDetailId() {
        return cancelCardFeeDetailId;
    }

    public void setCancelCardFeeDetailId(String cancelCardFeeDetailId) {
        this.cancelCardFeeDetailId = cancelCardFeeDetailId;
    }
    public String getCancelCardAcceptanceFeeDetailId() {
        return cancelCardAcceptanceFeeDetailId;
    }

    public void setCancelCardAcceptanceFeeDetailId(String cancelCardAcceptanceFeeDetailId) {
        this.cancelCardAcceptanceFeeDetailId = cancelCardAcceptanceFeeDetailId;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    @Override
    public String toString() {
        return "OrganizationCustomerCardInfo{" +
            "id=" + id +
            ", organizationNo=" + organizationNo +
            ", customerId=" + customerId +
            ", cardScheme=" + cardScheme +
            ", cardProductCode=" + cardProductCode +
            ", cardId=" + cardId +
            ", cardNo=" + cardNo +
            ", maskedCardNo=" + maskedCardNo +
            ", currencyCode=" + currencyCode +
            ", processor=" + processor +
            ", cardStatus=" + cardStatus +
            ", cardActiveStatus=" + cardActiveStatus +
            ", mobilePhoneArea=" + mobilePhoneArea +
            ", mobilePhone=" + mobilePhone +
            ", email=" + email +
            ", cardholderFirstName=" + cardholderFirstName +
            ", cardholderLastName=" + cardholderLastName +
            ", openCardFeeDetailId=" + openCardFeeDetailId +
            ", cancelCardFeeDetailId=" + cancelCardFeeDetailId +
            ", cancelCardAcceptanceFeeDetailId=" + cancelCardAcceptanceFeeDetailId +
            ", createTime=" + createTime +
            ", lastModifyTime=" + lastModifyTime +
        "}";
    }
}

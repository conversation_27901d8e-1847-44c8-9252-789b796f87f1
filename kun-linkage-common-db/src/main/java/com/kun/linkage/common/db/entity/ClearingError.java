package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 清算异常表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-25
 */
@TableName("kc_clearing_error")
public class ClearingError implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 清算表主键id
     */
    private Long clearingId;

    /**
     * 清算流水号
     */
    private String clearingNo;

    /**
     * 通道来源;YW:yeewallex；
     */
    private String channelSource;

    /**
     * 来源系统:VCC;KL
     */
    private String system;

    /**
     * 清算日期；yyyymmdd
     */
    private LocalDate clearingDate;

    /**
     * 关联授权流水表的主键id
     */
    private Long authId;

    /**
     * 05:消费;06:退货；07：取现;25:消费查询;26:退货撤销；27：取现撤销
     */
    private String transCode;

    /**
     * 商户号
     */
    private String customerMerId;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户国家代码
     */
    private String merchantCountryCode;

    /**
     * 交易时间
     */
    private String transactionDate;

    /**
     * 交易币种
     */
    private String transactionCurrencyCode;

    /**
     * 交易金额
     */
    private BigDecimal transactionAmount;

    /**
     * 参考号;F37
     */
    private String referenceNo;

    /**
     * 审计追踪:F11
     */
    private String traceAuditNo;

    /**
     * 卡id
     */
    private String cardId;

    /**
     * kcard系统卡id
     */
    private String kcardId;

    /**
     * 脱敏卡号
     */
    private String maskedCardNo;

    /**
     * 持卡人币种
     */
    private String cardholderCurrencyCode;

    /**
     * 持卡人金额
     */
    private BigDecimal cardholderAmount;

    /**
     * 收单参考号
     */
    private String acqArn;

    /**
     * F38:清分文件授权码；
     */
    private String authCode;

    /**
     * 系统授权号
     */
    private String systemAuthCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getClearingId() {
        return clearingId;
    }

    public void setClearingId(Long clearingId) {
        this.clearingId = clearingId;
    }
    public String getClearingNo() {
        return clearingNo;
    }

    public void setClearingNo(String clearingNo) {
        this.clearingNo = clearingNo;
    }
    public String getChannelSource() {
        return channelSource;
    }

    public void setChannelSource(String channelSource) {
        this.channelSource = channelSource;
    }
    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public LocalDate getClearingDate() {
        return clearingDate;
    }

    public void setClearingDate(LocalDate clearingDate) {
        this.clearingDate = clearingDate;
    }

    public Long getAuthId() {
        return authId;
    }

    public void setAuthId(Long authId) {
        this.authId = authId;
    }
    public String getTransCode() {
        return transCode;
    }

    public void setTransCode(String transCode) {
        this.transCode = transCode;
    }
    public String getCustomerMerId() {
        return customerMerId;
    }

    public void setCustomerMerId(String customerMerId) {
        this.customerMerId = customerMerId;
    }
    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }
    public String getMerchantCountryCode() {
        return merchantCountryCode;
    }

    public void setMerchantCountryCode(String merchantCountryCode) {
        this.merchantCountryCode = merchantCountryCode;
    }
    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }
    public String getTransactionCurrencyCode() {
        return transactionCurrencyCode;
    }

    public void setTransactionCurrencyCode(String transactionCurrencyCode) {
        this.transactionCurrencyCode = transactionCurrencyCode;
    }
    public BigDecimal getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(BigDecimal transactionAmount) {
        this.transactionAmount = transactionAmount;
    }
    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }
    public String getTraceAuditNo() {
        return traceAuditNo;
    }

    public void setTraceAuditNo(String traceAuditNo) {
        this.traceAuditNo = traceAuditNo;
    }
    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }
    public String getKcardId() {
        return kcardId;
    }

    public void setKcardId(String kcardId) {
        this.kcardId = kcardId;
    }

    public String getMaskedCardNo() {
        return maskedCardNo;
    }

    public void setMaskedCardNo(String maskedCardNo) {
        this.maskedCardNo = maskedCardNo;
    }

    public String getCardholderCurrencyCode() {
        return cardholderCurrencyCode;
    }

    public void setCardholderCurrencyCode(String cardholderCurrencyCode) {
        this.cardholderCurrencyCode = cardholderCurrencyCode;
    }
    public BigDecimal getCardholderAmount() {
        return cardholderAmount;
    }

    public void setCardholderAmount(BigDecimal cardholderAmount) {
        this.cardholderAmount = cardholderAmount;
    }
    public String getAcqArn() {
        return acqArn;
    }

    public void setAcqArn(String acqArn) {
        this.acqArn = acqArn;
    }
    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }
    public String getSystemAuthCode() {
        return systemAuthCode;
    }

    public void setSystemAuthCode(String systemAuthCode) {
        this.systemAuthCode = systemAuthCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

}

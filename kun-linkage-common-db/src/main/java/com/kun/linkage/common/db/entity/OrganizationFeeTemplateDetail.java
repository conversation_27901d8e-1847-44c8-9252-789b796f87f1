package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 机构费率模版明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@TableName("kl_organization_fee_template_detail")
public class OrganizationFeeTemplateDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模版明细id
     */
    @TableId(value = "template_detail_id", type = IdType.ASSIGN_ID)
    private String templateDetailId;

    /**
     * 所属模版号
     */
    @TableField("template_no")
    private String templateNo;

    /**
     * 手续费类型
     */
    @TableField("fee_type")
    private String feeType;

    /**
     * 计费维度
     */
    @TableField("billing_dimension")
    private String billingDimension;

    /**
     * 收取方式
     */
    @TableField("collection_method")
    private String collectionMethod;

    /**
     * 币种
     */
    @TableField("currency_code")
    private String currencyCode;

    /**
     * 金额区间:最小金额
     */
    @TableField("min_amount")
    private BigDecimal minAmount;

    /**
     * 金额区间:最大金额
     */
    @TableField("max_amount")
    private BigDecimal maxAmount;

    /**
     * 比例
     */
    @TableField("proportion_rate")
    private BigDecimal proportionRate;

    /**
     * 比例的保底金额
     */
    @TableField("proportion_min_amount")
    private BigDecimal proportionMinAmount;

    /**
     * 比例的封顶金额
     */
    @TableField("proportion_max_amount")
    private BigDecimal proportionMaxAmount;

    /**
     * 固定值
     */
    @TableField("fixed_amount")
    private BigDecimal fixedAmount;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 创建人名称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 最后一次修改时间
     */
    @TableField("last_modify_time")
    private LocalDateTime lastModifyTime;

    /**
     * 最后一次修改人id
     */
    @TableField("last_modify_user_id")
    private String lastModifyUserId;

    /**
     * 最后一次修改人名称
     */
    @TableField("last_modify_user_name")
    private String lastModifyUserName;

    public String getTemplateDetailId() {
        return templateDetailId;
    }

    public void setTemplateDetailId(String templateDetailId) {
        this.templateDetailId = templateDetailId;
    }
    public String getTemplateNo() {
        return templateNo;
    }

    public void setTemplateNo(String templateNo) {
        this.templateNo = templateNo;
    }
    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }
    public String getBillingDimension() {
        return billingDimension;
    }

    public void setBillingDimension(String billingDimension) {
        this.billingDimension = billingDimension;
    }
    public String getCollectionMethod() {
        return collectionMethod;
    }

    public void setCollectionMethod(String collectionMethod) {
        this.collectionMethod = collectionMethod;
    }
    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }
    public BigDecimal getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(BigDecimal minAmount) {
        this.minAmount = minAmount;
    }
    public BigDecimal getMaxAmount() {
        return maxAmount;
    }

    public void setMaxAmount(BigDecimal maxAmount) {
        this.maxAmount = maxAmount;
    }
    public BigDecimal getProportionRate() {
        return proportionRate;
    }

    public void setProportionRate(BigDecimal proportionRate) {
        this.proportionRate = proportionRate;
    }
    public BigDecimal getProportionMinAmount() {
        return proportionMinAmount;
    }

    public void setProportionMinAmount(BigDecimal proportionMinAmount) {
        this.proportionMinAmount = proportionMinAmount;
    }
    public BigDecimal getProportionMaxAmount() {
        return proportionMaxAmount;
    }

    public void setProportionMaxAmount(BigDecimal proportionMaxAmount) {
        this.proportionMaxAmount = proportionMaxAmount;
    }
    public BigDecimal getFixedAmount() {
        return fixedAmount;
    }

    public void setFixedAmount(BigDecimal fixedAmount) {
        this.fixedAmount = fixedAmount;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }
    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }
    public String getLastModifyUserId() {
        return lastModifyUserId;
    }

    public void setLastModifyUserId(String lastModifyUserId) {
        this.lastModifyUserId = lastModifyUserId;
    }
    public String getLastModifyUserName() {
        return lastModifyUserName;
    }

    public void setLastModifyUserName(String lastModifyUserName) {
        this.lastModifyUserName = lastModifyUserName;
    }

    @Override
    public String toString() {
        return "OrganizationFeeTemplateDetail{" +
            "templateDetailId=" + templateDetailId +
            ", templateNo=" + templateNo +
            ", feeType=" + feeType +
            ", billingDimension=" + billingDimension +
            ", collectionMethod=" + collectionMethod +
            ", currencyCode=" + currencyCode +
            ", minAmount=" + minAmount +
            ", maxAmount=" + maxAmount +
            ", proportionRate=" + proportionRate +
            ", proportionMinAmount=" + proportionMinAmount +
            ", proportionMaxAmount=" + proportionMaxAmount +
            ", fixedAmount=" + fixedAmount +
            ", createTime=" + createTime +
            ", createUserId=" + createUserId +
            ", createUserName=" + createUserName +
            ", lastModifyTime=" + lastModifyTime +
            ", lastModifyUserId=" + lastModifyUserId +
            ", lastModifyUserName=" + lastModifyUserName +
        "}";
    }
}

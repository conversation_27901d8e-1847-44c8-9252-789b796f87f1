package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 机构限额配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@TableName("kl_organization_limit_config")
public class OrganizationLimitConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("organization_no")
    private String organizationNo;

    /**
     * 客户等级
     */
    @TableField("customer_level")
    private Integer customerLevel;

    /**
     * 单笔限额
     */
    @TableField("single_transaction_limit_amount")
    private BigDecimal singleTransactionLimitAmount;

    /**
     * 日限额
     */
    @TableField("daily_limit_amount")
    private BigDecimal dailyLimitAmount;

    /**
     * 币种码
     */
    @TableField("currency_code")
    private String currencyCode;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 创建人名称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 最后一次修改时间
     */
    @TableField("last_modify_time")
    private LocalDateTime lastModifyTime;

    /**
     * 最后一次修改人id
     */
    @TableField("last_modify_user_id")
    private String lastModifyUserId;

    /**
     * 最后一次修改人名称
     */
    @TableField("last_modify_user_name")
    private String lastModifyUserName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public Integer getCustomerLevel() {
        return customerLevel;
    }

    public void setCustomerLevel(Integer customerLevel) {
        this.customerLevel = customerLevel;
    }
    public BigDecimal getSingleTransactionLimitAmount() {
        return singleTransactionLimitAmount;
    }

    public void setSingleTransactionLimitAmount(BigDecimal singleTransactionLimitAmount) {
        this.singleTransactionLimitAmount = singleTransactionLimitAmount;
    }
    public BigDecimal getDailyLimitAmount() {
        return dailyLimitAmount;
    }

    public void setDailyLimitAmount(BigDecimal dailyLimitAmount) {
        this.dailyLimitAmount = dailyLimitAmount;
    }
    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }
    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }
    public String getLastModifyUserId() {
        return lastModifyUserId;
    }

    public void setLastModifyUserId(String lastModifyUserId) {
        this.lastModifyUserId = lastModifyUserId;
    }
    public String getLastModifyUserName() {
        return lastModifyUserName;
    }

    public void setLastModifyUserName(String lastModifyUserName) {
        this.lastModifyUserName = lastModifyUserName;
    }

    @Override
    public String toString() {
        return "OrganizationLimitConfig{" +
            "id=" + id +
            ", organizationNo=" + organizationNo +
            ", customerLevel=" + customerLevel +
            ", singleTransactionLimitAmount=" + singleTransactionLimitAmount +
            ", dailyLimitAmount=" + dailyLimitAmount +
            ", currencyCode=" + currencyCode +
            ", status=" + status +
            ", createTime=" + createTime +
            ", createUserId=" + createUserId +
            ", createUserName=" + createUserName +
            ", lastModifyTime=" + lastModifyTime +
            ", lastModifyUserId=" + lastModifyUserId +
            ", lastModifyUserName=" + lastModifyUserName +
        "}";
    }
}

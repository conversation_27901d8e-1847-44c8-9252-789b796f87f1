package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 机构用户卡操作记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@TableName("kl_organization_customer_card_operation_record")
public class OrganizationCustomerCardOperationRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 机构号
     */
    private String organizationNo;

    /**
     * 请求流水号
     */
    private String requestNo;

    /**
     * 客户号
     */
    private String customerId;

    /**
     * 卡产品编码
     */
    private String cardProductCode;

    /**
     * 卡id
     */
    private String cardId;

    /**
     * 加密卡号
     */
    private String cardNo;

    /**
     * 掩码卡号
     */
    private String maskedCardNo;

    /**
     * 操作状态;PPROCESSING:处理中;SUCCESS:成功;FAIL:失败
     */
    private String operationStatus;

    /**
     * 失败信息
     */
    private String failMessage;

    /**
     * 原请求参数(json)
     */
    private String requestParams;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后一次修改时间
     */
    private LocalDateTime lastModifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }
    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }
    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }
    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }
    public String getMaskedCardNo() {
        return maskedCardNo;
    }

    public void setMaskedCardNo(String maskedCardNo) {
        this.maskedCardNo = maskedCardNo;
    }
    public String getOperationStatus() {
        return operationStatus;
    }

    public void setOperationStatus(String operationStatus) {
        this.operationStatus = operationStatus;
    }
    public String getFailMessage() {
        return failMessage;
    }

    public void setFailMessage(String failMessage) {
        this.failMessage = failMessage;
    }
    public String getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(String requestParams) {
        this.requestParams = requestParams;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    @Override
    public String toString() {
        return "OrganizationCustomerCardOperationRecord{" +
            "id=" + id +
            ", operationType=" + operationType +
            ", organizationNo=" + organizationNo +
            ", requestNo=" + requestNo +
            ", customerId=" + customerId +
            ", cardProductCode=" + cardProductCode +
            ", cardId=" + cardId +
            ", cardNo=" + cardNo +
            ", maskedCardNo=" + maskedCardNo +
            ", operationStatus=" + operationStatus +
            ", failMessage=" + failMessage +
            ", requestParams=" + requestParams +
            ", createTime=" + createTime +
            ", lastModifyTime=" + lastModifyTime +
        "}";
    }
}

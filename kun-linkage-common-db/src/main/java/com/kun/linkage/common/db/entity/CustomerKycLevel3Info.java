package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * kyc三级认证信息记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@TableName("kl_customer_kyc_level3_info")
public class CustomerKycLevel3Info implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键id
     */
    @TableId(value = "kyc_level3_id", type = IdType.ASSIGN_ID)
    private Long kycLevel3Id;

    /**
     * 用户id
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 机构号
     */
    @TableField("organization_no")
    private String organizationNo;

    /**
     * 案件号
     */
    @TableField("case_no")
    private String caseNo;

    /**
     * 雇佣状态；枚举
     */
    @TableField("employment_status")
    private String employmentStatus;

    /**
     * 公司名称
     */
    @TableField("company_name")
    private String companyName;

    /**
     * 行业;枚举
     */
    @TableField("industry")
    private String industry;

    /**
     * 职位;枚举
     */
    @TableField("position")
    private String position;

    /**
     * 工作年限；枚举
     */
    @TableField("years_of_experience")
    private String yearsOfExperience;

    /**
     * 年收入;枚举
     */
    @TableField("annual_income")
    private String annualIncome;

    /**
     * 资产证明图片上传ids;','号分割
     */
    @TableField("asset_proof_upload_ids")
    private String assetProofUploadIds;

    /**
     * 资产证明；枚举集合;','号分割
     */
    @TableField("asset_proof")
    private String assetProof;

    /**
     * 资产证明其他说明
     */
    @TableField("asset_proof_other_notes")
    private String assetProofOtherNotes;

    /**
     * 资产证明文件上传ids;','号分割
     */
    @TableField("asset_proof_document_upload_ids")
    private String assetProofDocumentUploadIds;

    /**
     * 资产证明文件url集合;','分割
     */
    @TableField("asset_proof_document")
    private String assetProofDocument;

    /**
     * 数币资金来源;枚举集合;‘，’分割
     */
    @TableField("cryptocurrency_funding_source")
    private String cryptocurrencyFundingSource;

    /**
     * 数币资金来源其他说明
     */
    @TableField("cryptocurrency_funding_source_other_notes")
    private String cryptocurrencyFundingSourceOtherNotes;

    /**
     * 数币资金来源证明文件上传ids;','号分割
     */
    @TableField("cryptocurrency_funding_source_document_upload_ids")
    private String cryptocurrencyFundingSourceDocumentUploadIds;

    /**
     * 数币资金来源证明文件url集合;','分割
     */
    @TableField("cryptocurrency_funding_source_document")
    private String cryptocurrencyFundingSourceDocument;

    /**
     * 报送时间
     */
    @TableField("submission_time")
    private Date submissionTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Long getKycLevel3Id() {
        return kycLevel3Id;
    }

    public void setKycLevel3Id(Long kycLevel3Id) {
        this.kycLevel3Id = kycLevel3Id;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }
    public String getEmploymentStatus() {
        return employmentStatus;
    }

    public void setEmploymentStatus(String employmentStatus) {
        this.employmentStatus = employmentStatus;
    }
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }
    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }
    public String getYearsOfExperience() {
        return yearsOfExperience;
    }

    public void setYearsOfExperience(String yearsOfExperience) {
        this.yearsOfExperience = yearsOfExperience;
    }
    public String getAnnualIncome() {
        return annualIncome;
    }

    public void setAnnualIncome(String annualIncome) {
        this.annualIncome = annualIncome;
    }
    public String getAssetProofUploadIds() {
        return assetProofUploadIds;
    }

    public void setAssetProofUploadIds(String assetProofUploadIds) {
        this.assetProofUploadIds = assetProofUploadIds;
    }
    public String getAssetProof() {
        return assetProof;
    }

    public void setAssetProof(String assetProof) {
        this.assetProof = assetProof;
    }
    public String getAssetProofOtherNotes() {
        return assetProofOtherNotes;
    }

    public void setAssetProofOtherNotes(String assetProofOtherNotes) {
        this.assetProofOtherNotes = assetProofOtherNotes;
    }
    public String getAssetProofDocumentUploadIds() {
        return assetProofDocumentUploadIds;
    }

    public void setAssetProofDocumentUploadIds(String assetProofDocumentUploadIds) {
        this.assetProofDocumentUploadIds = assetProofDocumentUploadIds;
    }
    public String getAssetProofDocument() {
        return assetProofDocument;
    }

    public void setAssetProofDocument(String assetProofDocument) {
        this.assetProofDocument = assetProofDocument;
    }
    public String getCryptocurrencyFundingSource() {
        return cryptocurrencyFundingSource;
    }

    public void setCryptocurrencyFundingSource(String cryptocurrencyFundingSource) {
        this.cryptocurrencyFundingSource = cryptocurrencyFundingSource;
    }
    public String getCryptocurrencyFundingSourceOtherNotes() {
        return cryptocurrencyFundingSourceOtherNotes;
    }

    public void setCryptocurrencyFundingSourceOtherNotes(String cryptocurrencyFundingSourceOtherNotes) {
        this.cryptocurrencyFundingSourceOtherNotes = cryptocurrencyFundingSourceOtherNotes;
    }
    public String getCryptocurrencyFundingSourceDocumentUploadIds() {
        return cryptocurrencyFundingSourceDocumentUploadIds;
    }

    public void setCryptocurrencyFundingSourceDocumentUploadIds(String cryptocurrencyFundingSourceDocumentUploadIds) {
        this.cryptocurrencyFundingSourceDocumentUploadIds = cryptocurrencyFundingSourceDocumentUploadIds;
    }
    public String getCryptocurrencyFundingSourceDocument() {
        return cryptocurrencyFundingSourceDocument;
    }

    public void setCryptocurrencyFundingSourceDocument(String cryptocurrencyFundingSourceDocument) {
        this.cryptocurrencyFundingSourceDocument = cryptocurrencyFundingSourceDocument;
    }
    public Date getSubmissionTime() {
        return submissionTime;
    }

    public void setSubmissionTime(Date submissionTime) {
        this.submissionTime = submissionTime;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CustomerKycLevel3Info{" +
            "kycLevel3Id=" + kycLevel3Id +
            ", customerId=" + customerId +
            ", organizationNo=" + organizationNo +
            ", caseNo=" + caseNo +
            ", employmentStatus=" + employmentStatus +
            ", companyName=" + companyName +
            ", industry=" + industry +
            ", position=" + position +
            ", yearsOfExperience=" + yearsOfExperience +
            ", annualIncome=" + annualIncome +
            ", assetProofUploadIds=" + assetProofUploadIds +
            ", assetProof=" + assetProof +
            ", assetProofOtherNotes=" + assetProofOtherNotes +
            ", assetProofDocumentUploadIds=" + assetProofDocumentUploadIds +
            ", assetProofDocument=" + assetProofDocument +
            ", cryptocurrencyFundingSource=" + cryptocurrencyFundingSource +
            ", cryptocurrencyFundingSourceOtherNotes=" + cryptocurrencyFundingSourceOtherNotes +
            ", cryptocurrencyFundingSourceDocumentUploadIds=" + cryptocurrencyFundingSourceDocumentUploadIds +
            ", cryptocurrencyFundingSourceDocument=" + cryptocurrencyFundingSourceDocument +
            ", submissionTime=" + submissionTime +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
        "}";
    }
}

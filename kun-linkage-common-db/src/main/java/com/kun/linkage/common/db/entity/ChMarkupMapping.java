package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 持卡人markup映射关系
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@TableName("common_ch_markup_mapping")
public class ChMarkupMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * processor
     */
    @TableField("processor")
    private String processor;

    /**
     * 商户号
     */
    @TableField("merchant_no")
    private String merchantNo;

    /**
     * 卡bin
     */
    @TableField("card_bin")
    private String cardBin;

    /**
     * 卡产品码
     */
    @TableField("card_product_code")
    private String cardProductCode;

    /**
     * 交易币种
     */
    @TableField("billing_currency")
    private String billingCurrency;

    /**
     * 持卡人交易金额
     */
    @TableField("billing_amount")
    private BigDecimal billingAmount;

    /**
     * 持卡人markup费率
     */
    @TableField("markup_rate")
    private BigDecimal markupRate;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 逻辑删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    @TableId("id")
    private Integer id;

    @TableField("create_date")
    private Date createDate;

    @TableField("update_date")
    private Date updateDate;

    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }
    public String getCardBin() {
        return cardBin;
    }

    public void setCardBin(String cardBin) {
        this.cardBin = cardBin;
    }
    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }
    public String getBillingCurrency() {
        return billingCurrency;
    }

    public void setBillingCurrency(String billingCurrency) {
        this.billingCurrency = billingCurrency;
    }
    public BigDecimal getBillingAmount() {
        return billingAmount;
    }

    public void setBillingAmount(BigDecimal billingAmount) {
        this.billingAmount = billingAmount;
    }
    public BigDecimal getMarkupRate() {
        return markupRate;
    }

    public void setMarkupRate(BigDecimal markupRate) {
        this.markupRate = markupRate;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "ChMarkupMapping{" +
            "processor=" + processor +
            ", merchantNo=" + merchantNo +
            ", cardBin=" + cardBin +
            ", cardProductCode=" + cardProductCode +
            ", billingCurrency=" + billingCurrency +
            ", billingAmount=" + billingAmount +
            ", markupRate=" + markupRate +
            ", status=" + status +
            ", isDeleted=" + isDeleted +
            ", createdBy=" + createdBy +
            ", updatedBy=" + updatedBy +
            ", id=" + id +
            ", createDate=" + createDate +
            ", updateDate=" + updateDate +
        "}";
    }
}

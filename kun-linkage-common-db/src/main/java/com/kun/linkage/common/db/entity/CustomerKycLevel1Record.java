package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * kyc一级认证信息记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@TableName("kl_customer_kyc_level1_record")
public class CustomerKycLevel1Record implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * kyc一级认证信息主键ID
     */
    @TableId(value = "kyc_level1_id", type = IdType.ASSIGN_ID)
    private Long kycLevel1Id;

    /**
     * 用户id
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 机构号
     */
    @TableField("organization_no")
    private String organizationNo;

    /**
     * 案件号
     */
    @TableField("case_no")
    private String caseNo;

    /**
     * 姓
     */
    @TableField("last_name")
    private String lastName;

    /**
     * 中间名
     */
    @TableField("middle_name")
    private String middleName;

    /**
     * 名
     */
    @TableField("first_name")
    private String firstName;

    /**
     * 证件类型
     */
    @TableField("id_type")
    private String idType;

    /**
     * 加密,证件号
     */
    @TableField("id_no")
    private String idNo;

    /**
     * 脱敏的证件号
     */
    @TableField("masked_id_no")
    private String maskedIdNo;

    /**
     * 出生日期;yyyy/mm/dd
     */
    @TableField("birth_date")
    private String birthDate;

    /**
     * 性别;1:男;2:女
     */
    @TableField("gender")
    private Integer gender;

    /**
     * 证件签发日
     */
    @TableField("id_issue_date")
    private String idIssueDate;

    /**
     * 证件有效期 
     */
    @TableField("id_expiry_date")
    private String idExpiryDate;

    /**
     * 国籍
     */
    @TableField("nationality")
    private String nationality;

    /**
     * 国家代码,3位字母
     */
    @TableField("country_code")
    private String countryCode;

    /**
     * 国家地区代码,3位数字
     */
    @TableField("country_no")
    private String countryNo;

    /**
     * 证件正面地址上传id
     */
    @TableField("id_card_front_image_upload_id")
    private String idCardFrontImageUploadId;

    /**
     * 证件正面地址
     */
    @TableField("id_card_front_image")
    private String idCardFrontImage;

    /**
     * 人脸图片地址上传id
     */
    @TableField("face_photo_image_upload_id")
    private String facePhotoImageUploadId;

    /**
     * 人脸图片地址
     */
    @TableField("face_photo_image")
    private String facePhotoImage;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 手机区号
     */
    @TableField("phone_area")
    private String phoneArea;

    /**
     * 手机号码
     */
    @TableField("mobile_no")
    private String mobileNo;

    /**
     * 居住地国家/地区;3位字母
     */
    @TableField("residence_country_code")
    private String residenceCountryCode;

    /**
     * 居住地州/省
     */
    @TableField("residence_state_province")
    private String residenceStateProvince;

    /**
     * 居住地城市
     */
    @TableField("residence_city")
    private String residenceCity;

    /**
     * 居住地详细地址
     */
    @TableField("residence_address_detail")
    private String residenceAddressDetail;

    /**
     * 邮编
     */
    @TableField("postal_code")
    private String postalCode;

    /**
     * 创建时间
     */
    @TableField("create_datetime")
    private Date createDatetime;

    /**
     * 更新时间
     */
    @TableField("update_datetime")
    private Date updateDatetime;

    public Long getKycLevel1Id() {
        return kycLevel1Id;
    }

    public void setKycLevel1Id(Long kycLevel1Id) {
        this.kycLevel1Id = kycLevel1Id;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }
    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }
    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }
    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }
    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }
    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }
    public String getMaskedIdNo() {
        return maskedIdNo;
    }

    public void setMaskedIdNo(String maskedIdNo) {
        this.maskedIdNo = maskedIdNo;
    }
    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }
    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }
    public String getIdIssueDate() {
        return idIssueDate;
    }

    public void setIdIssueDate(String idIssueDate) {
        this.idIssueDate = idIssueDate;
    }
    public String getIdExpiryDate() {
        return idExpiryDate;
    }

    public void setIdExpiryDate(String idExpiryDate) {
        this.idExpiryDate = idExpiryDate;
    }
    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }
    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }
    public String getCountryNo() {
        return countryNo;
    }

    public void setCountryNo(String countryNo) {
        this.countryNo = countryNo;
    }
    public String getIdCardFrontImageUploadId() {
        return idCardFrontImageUploadId;
    }

    public void setIdCardFrontImageUploadId(String idCardFrontImageUploadId) {
        this.idCardFrontImageUploadId = idCardFrontImageUploadId;
    }
    public String getIdCardFrontImage() {
        return idCardFrontImage;
    }

    public void setIdCardFrontImage(String idCardFrontImage) {
        this.idCardFrontImage = idCardFrontImage;
    }
    public String getFacePhotoImageUploadId() {
        return facePhotoImageUploadId;
    }

    public void setFacePhotoImageUploadId(String facePhotoImageUploadId) {
        this.facePhotoImageUploadId = facePhotoImageUploadId;
    }
    public String getFacePhotoImage() {
        return facePhotoImage;
    }

    public void setFacePhotoImage(String facePhotoImage) {
        this.facePhotoImage = facePhotoImage;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public String getPhoneArea() {
        return phoneArea;
    }

    public void setPhoneArea(String phoneArea) {
        this.phoneArea = phoneArea;
    }
    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }
    public String getResidenceCountryCode() {
        return residenceCountryCode;
    }

    public void setResidenceCountryCode(String residenceCountryCode) {
        this.residenceCountryCode = residenceCountryCode;
    }
    public String getResidenceStateProvince() {
        return residenceStateProvince;
    }

    public void setResidenceStateProvince(String residenceStateProvince) {
        this.residenceStateProvince = residenceStateProvince;
    }
    public String getResidenceCity() {
        return residenceCity;
    }

    public void setResidenceCity(String residenceCity) {
        this.residenceCity = residenceCity;
    }
    public String getResidenceAddressDetail() {
        return residenceAddressDetail;
    }

    public void setResidenceAddressDetail(String residenceAddressDetail) {
        this.residenceAddressDetail = residenceAddressDetail;
    }
    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public Date getCreateDatetime() {
        return createDatetime;
    }

    public void setCreateDatetime(Date createDatetime) {
        this.createDatetime = createDatetime;
    }
    public Date getUpdateDatetime() {
        return updateDatetime;
    }

    public void setUpdateDatetime(Date updateDatetime) {
        this.updateDatetime = updateDatetime;
    }

    @Override
    public String toString() {
        return "CustomerKycLevel1Record{" +
            "kycLevel1Id=" + kycLevel1Id +
            ", customerId=" + customerId +
            ", organizationNo=" + organizationNo +
            ", caseNo=" + caseNo +
            ", lastName=" + lastName +
            ", middleName=" + middleName +
            ", firstName=" + firstName +
            ", idType=" + idType +
            ", idNo=" + idNo +
            ", maskedIdNo=" + maskedIdNo +
            ", birthDate=" + birthDate +
            ", gender=" + gender +
            ", idIssueDate=" + idIssueDate +
            ", idExpiryDate=" + idExpiryDate +
            ", nationality=" + nationality +
            ", countryCode=" + countryCode +
            ", countryNo=" + countryNo +
            ", idCardFrontImageUploadId=" + idCardFrontImageUploadId +
            ", idCardFrontImage=" + idCardFrontImage +
            ", facePhotoImageUploadId=" + facePhotoImageUploadId +
            ", facePhotoImage=" + facePhotoImage +
            ", email=" + email +
            ", phoneArea=" + phoneArea +
            ", mobileNo=" + mobileNo +
            ", residenceCountryCode=" + residenceCountryCode +
            ", residenceStateProvince=" + residenceStateProvince +
            ", residenceCity=" + residenceCity +
            ", residenceAddressDetail=" + residenceAddressDetail +
            ", postalCode=" + postalCode +
            ", createDatetime=" + createDatetime +
            ", updateDatetime=" + updateDatetime +
        "}";
    }
}

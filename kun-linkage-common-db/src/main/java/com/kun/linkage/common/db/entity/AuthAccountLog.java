package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 交易请求账户记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-15
 */
@TableName("kl_auth_account_log")
public class AuthAccountLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 授权流水ID
     */
    @TableField("auth_flow_id")
    private String authFlowId;

    /**
     * 账户类型
     */
    @TableField("account_type")
    private String accountType;

    /**
     * 账户号
     */
    @TableField("account_no")
    private String accountNo;

    /**
     * 请求参数
     */
    @TableField("request_json")
    private String requestJson;

    /**
     * 请求号
     */
    @TableField("request_no")
    private String requestNo;

    /**
     * 响应码
     */
    @TableField("response_code")
    private String responseCode;

    /**
     * 响应信息
     */
    @TableField("response_message")
    private String responseMessage;

    /**
     * 响应参数
     */
    @TableField("response_json")
    private String responseJson;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getAuthFlowId() {
        return authFlowId;
    }

    public void setAuthFlowId(String authFlowId) {
        this.authFlowId = authFlowId;
    }
    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }
    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }
    public String getRequestJson() {
        return requestJson;
    }

    public void setRequestJson(String requestJson) {
        this.requestJson = requestJson;
    }
    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }
    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }
    public String getResponseMessage() {
        return responseMessage;
    }

    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }
    public String getResponseJson() {
        return responseJson;
    }

    public void setResponseJson(String responseJson) {
        this.responseJson = responseJson;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AuthAccountLog{" +
            "id=" + id +
            ", authFlowId=" + authFlowId +
            ", accountType=" + accountType +
            ", accountNo=" + accountNo +
            ", requestJson=" + requestJson +
            ", requestNo=" + requestNo +
            ", responseCode=" + responseCode +
            ", responseMessage=" + responseMessage +
            ", responseJson=" + responseJson +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
        "}";
    }
}

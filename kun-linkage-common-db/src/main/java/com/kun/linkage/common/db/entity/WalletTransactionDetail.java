package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 机构用户钱包交易流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@TableName("kl_wallet_transaction_detail")
public class WalletTransactionDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 流水id
     */
    @TableId("id")
    private String id;

    /**
     * 机构号
     */
    @TableField("organization_no")
    private String organizationNo;

    /**
     * 客户号
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 请求流水号
     */
    @TableField("request_no")
    private String requestNo;

    /**
     * 钱包通道
     */
    @TableField("wallet_network")
    private String walletNetwork;

    /**
     * 链网络
     */
    @TableField("chain_network")
    private String chainNetwork;

    /**
     * 钱包地址
     */
    @TableField("wallet_address")
    private String walletAddress;

    /**
     * 交易类型
     */
    @TableField("transaction_type")
    private String transactionType;

    /**
     * 钱包交易日期时间
     */
    @TableField("transaction_datetime")
    private LocalDateTime transactionDatetime;

    /**
     * 数币金额
     */
    @TableField("digital_amount")
    private BigDecimal digitalAmount;

    /**
     * 数币币种
     */
    @TableField("digital_currency_code")
    private String digitalCurrencyCode;

    /**
     * 数币币种精度
     */
    @TableField("digital_currency_precision")
    private Integer digitalCurrencyPrecision;

    /**
     * 法币金额
     */
    @TableField("fiat_amount")
    private BigDecimal fiatAmount;

    /**
     * 法币币种
     */
    @TableField("fiat_currency_code")
    private String fiatCurrencyCode;

    /**
     * 法币币种精度
     */
    @TableField("fiat_currency_precision")
    private Integer fiatCurrencyPrecision;

    /**
     * 换汇汇率
     */
    @TableField("fx_rate")
    private BigDecimal fxRate;

    /**
     * markup百分比费率
     */
    @TableField("markup_fee_proportion_rate")
    private BigDecimal markupFeeProportionRate;

    /**
     * markup百分比费率金额最小值
     */
    @TableField("markup_fee_proportion_min_amount")
    private BigDecimal markupFeeProportionMinAmount;

    /**
     * markup百分比费率金额最大值
     */
    @TableField("markup_fee_proportion_max_amount")
    private BigDecimal markupFeeProportionMaxAmount;

    /**
     * markup百分比费率金额
     */
    @TableField("markup_fee_proportion_amount")
    private BigDecimal markupFeeProportionAmount;

    /**
     * markup固定值费用
     */
    @TableField("markup_fee_fixed_amount")
    private BigDecimal markupFeeFixedAmount;

    /**
     * markup费用金额
     */
    @TableField("markup_fee_amount")
    private BigDecimal markupFeeAmount;

    /**
     * 充值承兑百分比费率
     */
    @TableField("recharge_acceptance_fee_proportion_rate")
    private BigDecimal rechargeAcceptanceFeeProportionRate;

    /**
     * 充值承兑百分比费率金额最小值
     */
    @TableField("recharge_acceptance_fee_proportion_min_amount")
    private BigDecimal rechargeAcceptanceFeeProportionMinAmount;

    /**
     * 充值承兑百分比费率金额最大值
     */
    @TableField("recharge_acceptance_fee_proportion_max_amount")
    private BigDecimal rechargeAcceptanceFeeProportionMaxAmount;

    /**
     * 充值承兑百分比费率金额
     */
    @TableField("recharge_acceptance_fee_proportion_amount")
    private BigDecimal rechargeAcceptanceFeeProportionAmount;

    /**
     * 充值承兑固定值费用
     */
    @TableField("recharge_acceptance_fee_fixed_amount")
    private BigDecimal rechargeAcceptanceFeeFixedAmount;

    /**
     * 充值承兑费用金额(充值时会使用)
     */
    @TableField("recharge_acceptance_fee_amount")
    private BigDecimal rechargeAcceptanceFeeAmount;

    /**
     * 记账流水号
     */
    @TableField("bookkeep_no")
    private String bookkeepNo;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 最后一次修改时间
     */
    @TableField("last_modify_time")
    private LocalDateTime lastModifyTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }
    public String getWalletNetwork() {
        return walletNetwork;
    }

    public void setWalletNetwork(String walletNetwork) {
        this.walletNetwork = walletNetwork;
    }
    public String getChainNetwork() {
        return chainNetwork;
    }

    public void setChainNetwork(String chainNetwork) {
        this.chainNetwork = chainNetwork;
    }
    public String getWalletAddress() {
        return walletAddress;
    }

    public void setWalletAddress(String walletAddress) {
        this.walletAddress = walletAddress;
    }
    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }
    public LocalDateTime getTransactionDatetime() {
        return transactionDatetime;
    }

    public void setTransactionDatetime(LocalDateTime transactionDatetime) {
        this.transactionDatetime = transactionDatetime;
    }
    public BigDecimal getDigitalAmount() {
        return digitalAmount;
    }

    public void setDigitalAmount(BigDecimal digitalAmount) {
        this.digitalAmount = digitalAmount;
    }
    public String getDigitalCurrencyCode() {
        return digitalCurrencyCode;
    }

    public void setDigitalCurrencyCode(String digitalCurrencyCode) {
        this.digitalCurrencyCode = digitalCurrencyCode;
    }
    public Integer getDigitalCurrencyPrecision() {
        return digitalCurrencyPrecision;
    }

    public void setDigitalCurrencyPrecision(Integer digitalCurrencyPrecision) {
        this.digitalCurrencyPrecision = digitalCurrencyPrecision;
    }
    public BigDecimal getFiatAmount() {
        return fiatAmount;
    }

    public void setFiatAmount(BigDecimal fiatAmount) {
        this.fiatAmount = fiatAmount;
    }
    public String getFiatCurrencyCode() {
        return fiatCurrencyCode;
    }

    public void setFiatCurrencyCode(String fiatCurrencyCode) {
        this.fiatCurrencyCode = fiatCurrencyCode;
    }
    public Integer getFiatCurrencyPrecision() {
        return fiatCurrencyPrecision;
    }

    public void setFiatCurrencyPrecision(Integer fiatCurrencyPrecision) {
        this.fiatCurrencyPrecision = fiatCurrencyPrecision;
    }
    public BigDecimal getFxRate() {
        return fxRate;
    }

    public void setFxRate(BigDecimal fxRate) {
        this.fxRate = fxRate;
    }
    public BigDecimal getMarkupFeeProportionRate() {
        return markupFeeProportionRate;
    }

    public void setMarkupFeeProportionRate(BigDecimal markupFeeProportionRate) {
        this.markupFeeProportionRate = markupFeeProportionRate;
    }
    public BigDecimal getMarkupFeeProportionMinAmount() {
        return markupFeeProportionMinAmount;
    }

    public void setMarkupFeeProportionMinAmount(BigDecimal markupFeeProportionMinAmount) {
        this.markupFeeProportionMinAmount = markupFeeProportionMinAmount;
    }
    public BigDecimal getMarkupFeeProportionMaxAmount() {
        return markupFeeProportionMaxAmount;
    }

    public void setMarkupFeeProportionMaxAmount(BigDecimal markupFeeProportionMaxAmount) {
        this.markupFeeProportionMaxAmount = markupFeeProportionMaxAmount;
    }
    public BigDecimal getMarkupFeeProportionAmount() {
        return markupFeeProportionAmount;
    }

    public void setMarkupFeeProportionAmount(BigDecimal markupFeeProportionAmount) {
        this.markupFeeProportionAmount = markupFeeProportionAmount;
    }
    public BigDecimal getMarkupFeeFixedAmount() {
        return markupFeeFixedAmount;
    }

    public void setMarkupFeeFixedAmount(BigDecimal markupFeeFixedAmount) {
        this.markupFeeFixedAmount = markupFeeFixedAmount;
    }
    public BigDecimal getMarkupFeeAmount() {
        return markupFeeAmount;
    }

    public void setMarkupFeeAmount(BigDecimal markupFeeAmount) {
        this.markupFeeAmount = markupFeeAmount;
    }
    public BigDecimal getRechargeAcceptanceFeeProportionRate() {
        return rechargeAcceptanceFeeProportionRate;
    }

    public void setRechargeAcceptanceFeeProportionRate(BigDecimal rechargeAcceptanceFeeProportionRate) {
        this.rechargeAcceptanceFeeProportionRate = rechargeAcceptanceFeeProportionRate;
    }
    public BigDecimal getRechargeAcceptanceFeeProportionMinAmount() {
        return rechargeAcceptanceFeeProportionMinAmount;
    }

    public void setRechargeAcceptanceFeeProportionMinAmount(BigDecimal rechargeAcceptanceFeeProportionMinAmount) {
        this.rechargeAcceptanceFeeProportionMinAmount = rechargeAcceptanceFeeProportionMinAmount;
    }
    public BigDecimal getRechargeAcceptanceFeeProportionMaxAmount() {
        return rechargeAcceptanceFeeProportionMaxAmount;
    }

    public void setRechargeAcceptanceFeeProportionMaxAmount(BigDecimal rechargeAcceptanceFeeProportionMaxAmount) {
        this.rechargeAcceptanceFeeProportionMaxAmount = rechargeAcceptanceFeeProportionMaxAmount;
    }
    public BigDecimal getRechargeAcceptanceFeeProportionAmount() {
        return rechargeAcceptanceFeeProportionAmount;
    }

    public void setRechargeAcceptanceFeeProportionAmount(BigDecimal rechargeAcceptanceFeeProportionAmount) {
        this.rechargeAcceptanceFeeProportionAmount = rechargeAcceptanceFeeProportionAmount;
    }
    public BigDecimal getRechargeAcceptanceFeeFixedAmount() {
        return rechargeAcceptanceFeeFixedAmount;
    }

    public void setRechargeAcceptanceFeeFixedAmount(BigDecimal rechargeAcceptanceFeeFixedAmount) {
        this.rechargeAcceptanceFeeFixedAmount = rechargeAcceptanceFeeFixedAmount;
    }
    public BigDecimal getRechargeAcceptanceFeeAmount() {
        return rechargeAcceptanceFeeAmount;
    }

    public void setRechargeAcceptanceFeeAmount(BigDecimal rechargeAcceptanceFeeAmount) {
        this.rechargeAcceptanceFeeAmount = rechargeAcceptanceFeeAmount;
    }
    public String getBookkeepNo() {
        return bookkeepNo;
    }

    public void setBookkeepNo(String bookkeepNo) {
        this.bookkeepNo = bookkeepNo;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    @Override
    public String toString() {
        return "WalletTransactionDetail{" +
            "id=" + id +
            ", organizationNo=" + organizationNo +
            ", customerId=" + customerId +
            ", requestNo=" + requestNo +
            ", walletNetwork=" + walletNetwork +
            ", chainNetwork=" + chainNetwork +
            ", walletAddress=" + walletAddress +
            ", transactionType=" + transactionType +
            ", transactionDatetime=" + transactionDatetime +
            ", digitalAmount=" + digitalAmount +
            ", digitalCurrencyCode=" + digitalCurrencyCode +
            ", digitalCurrencyPrecision=" + digitalCurrencyPrecision +
            ", fiatAmount=" + fiatAmount +
            ", fiatCurrencyCode=" + fiatCurrencyCode +
            ", fiatCurrencyPrecision=" + fiatCurrencyPrecision +
            ", fxRate=" + fxRate +
            ", markupFeeProportionRate=" + markupFeeProportionRate +
            ", markupFeeProportionMinAmount=" + markupFeeProportionMinAmount +
            ", markupFeeProportionMaxAmount=" + markupFeeProportionMaxAmount +
            ", markupFeeProportionAmount=" + markupFeeProportionAmount +
            ", markupFeeFixedAmount=" + markupFeeFixedAmount +
            ", markupFeeAmount=" + markupFeeAmount +
            ", rechargeAcceptanceFeeProportionRate=" + rechargeAcceptanceFeeProportionRate +
            ", rechargeAcceptanceFeeProportionMinAmount=" + rechargeAcceptanceFeeProportionMinAmount +
            ", rechargeAcceptanceFeeProportionMaxAmount=" + rechargeAcceptanceFeeProportionMaxAmount +
            ", rechargeAcceptanceFeeProportionAmount=" + rechargeAcceptanceFeeProportionAmount +
            ", rechargeAcceptanceFeeFixedAmount=" + rechargeAcceptanceFeeFixedAmount +
            ", rechargeAcceptanceFeeAmount=" + rechargeAcceptanceFeeAmount +
            ", bookkeepNo=" + bookkeepNo +
            ", createTime=" + createTime +
            ", lastModifyTime=" + lastModifyTime +
        "}";
    }
}

package com.kun.linkage.customer.facade.api.bean.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

public class OrgPageQueryCardRechargeDetailReq implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 卡id(可为空)
     */
    @Schema(description = "卡id(可为空)")
    private String cardId;
    /**
     * 开始日期
     */
    @Schema(description = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private LocalDate startDate;
    /**
     * 结束日期
     */
    @Schema(description = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private LocalDate endDate;

    @Schema(description = "充值金额 - 开始", example = "100.00")
    private BigDecimal rechargeAmountFrom;

    @Schema(description = "充值金额 - 结束", example = "100.00")
    private BigDecimal rechargeAmountTo;

    @Schema(description = "充值状态", example = "SUCCESS")
    private String rechargeStatus;
    /**
     * 机构号
     */
    @Schema(description = "机构号")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String organizationNo;
    /**
     * 客户号
     */
    @Schema(description = "客户号")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String customerId;

    /**
     * 开始页
     */
    @Schema(description = "开始页")
    @Min(value = 1, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    @Max(value = 1000, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private int pageNum = 1;
    /**
     * 每页数量
     */
    @Schema(description = "每页数量")
    @Min(value = 1, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    @Max(value = 100, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private int pageSize = 5;

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }


    public BigDecimal getRechargeAmountFrom() {
        return rechargeAmountFrom;
    }

    public void setRechargeAmountFrom(BigDecimal rechargeAmountFrom) {
        this.rechargeAmountFrom = rechargeAmountFrom;
    }

    public BigDecimal getRechargeAmountTo() {
        return rechargeAmountTo;
    }

    public void setRechargeAmountTo(BigDecimal rechargeAmountTo) {
        this.rechargeAmountTo = rechargeAmountTo;
    }

    public String getRechargeStatus() {
        return rechargeStatus;
    }

    public void setRechargeStatus(String rechargeStatus) {
        this.rechargeStatus = rechargeStatus;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
}


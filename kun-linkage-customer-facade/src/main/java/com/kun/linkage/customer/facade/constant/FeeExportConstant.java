package com.kun.linkage.customer.facade.constant;

/**
 * 手续费导出相关常量
 */
public class FeeExportConstant {

    /**
     * 文件类型
     */
    public static class FileType {
        public static final String ORGANIZATION_FEE_EXPORT = "ORGANIZATION_FEE_EXPORT";
    }

    /**
     * 文件状态枚举
     */
    public enum FileStatus {
        PROCESSING("PROCESSING", "处理中"),
        SUCCESS("SUCCESS", "成功"),
        FAILED("FAILED", "失败");

        private final String code;
        private final String description;

        FileStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * CSV文件头
     */
    public static final String[] CSV_HEADERS = {
        "Fee Date 日期",
        "商户号",
        "Fee Type 手续费类型",
        "Fee Currency 手续费币种",
        "Fee Amount 手续费金额",
        "Deduct Currency 扣收币种",
        "Deduct Amount 扣收金额",
        "收取方式",
        "关联交易ID",
        "备注"
    };

    /**
     * 文件名模板
     */
    public static final String FILE_NAME_TEMPLATE = "Settlement_%s_%s.csv";

    /**
     * S3文件夹路径
     */
    public static final String S3_FOLDER_PATH = "customer/fee-reports/organization";

    /**
     * 最大查询天数
     */
    public static final int MAX_QUERY_DAYS = 31;
}

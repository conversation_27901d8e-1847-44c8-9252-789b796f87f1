package com.kun.linkage.customer.facade.enums;

public enum CardRechargeBusinessTypeEnum {
    CARD_RECHARGE("CARD_RECHARGE", "卡充值"),
    NEGATIVE_AMOUNT_DEDUCTION("NEGATIVE_AMOUNT_DEDUCTION", "未知");

    private final String value;
    private final String desc;

    CardRechargeBusinessTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}

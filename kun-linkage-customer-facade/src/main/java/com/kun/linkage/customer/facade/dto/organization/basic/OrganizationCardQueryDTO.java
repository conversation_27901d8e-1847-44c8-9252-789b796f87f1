package com.kun.linkage.customer.facade.dto.organization.basic;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.page.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 机构客户卡片信息查询参数
 */
public class OrganizationCardQueryDTO extends PageParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开卡日期 - 开始日期
     */
    @Schema(description = "开卡日期 - 开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate createDateFrom;

    /**
     * 开卡日期 - 结束日期
     */
    @Schema(description = "开卡日期 - 结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate createDateUntil;
    ;

    /**
     * 机构号
     */
    @Schema(description = "机构号")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String organizationNo;

    /**
     * CardID
     */
    @Schema(description = "CardId")
    private String cardId;

    /**
     * 客户号
     */
    @Schema(description = "客户号")
    private String customerId;

    /**
     * 卡片状态
     */
    @Schema(description = "卡片状态")
    private String cardStatus;

    public LocalDate getCreateDateFrom() {
        return createDateFrom;
    }

    public void setCreateDateFrom(LocalDate createDateFrom) {
        this.createDateFrom = createDateFrom;
    }

    public LocalDate getCreateDateUntil() {
        return createDateUntil;
    }

    public void setCreateDateUntil(LocalDate createDateUntil) {
        this.createDateUntil = createDateUntil;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(String cardStatus) {
        this.cardStatus = cardStatus;
    }

    @Override
    public String toString() {
        return "OrganizationCustomerCardListQueryDTO{" +
                "organizationNo='" + organizationNo + '\'' +
                ", createDateFrom=" + createDateFrom +
                ", createDateUntil=" + createDateUntil +
                ", cardId='" + cardId + '\'' +
                ", customerId='" + customerId + '\'' +
                ", cardStatus='" + cardStatus + '\'' +
                '}';
    }

}

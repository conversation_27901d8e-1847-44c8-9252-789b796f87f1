package com.kun.linkage.customer.facade.vo.mq;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.kun.common.util.mq.BaseMqMessage;

import java.io.Serializable;
import java.time.LocalDateTime;

public class OrganizationFeeDeductionEventVO extends BaseMqMessage implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 机构费用明细表记录id
     */
    private String feeDetailId;
    /**
     * 交易日期时间(分表键)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime transactionDatetime;
    /**
     * mpcToken(调用kun和payx时的Token)
     */
    private String mpcToken;
    /**
     * mpcGroupCode(调用kun和payx时的GroupProductCode)
     */
    private String mpcGroupCode;

    public String getFeeDetailId() {
        return feeDetailId;
    }

    public void setFeeDetailId(String feeDetailId) {
        this.feeDetailId = feeDetailId;
    }

    public LocalDateTime getTransactionDatetime() {
        return transactionDatetime;
    }

    public void setTransactionDatetime(LocalDateTime transactionDatetime) {
        this.transactionDatetime = transactionDatetime;
    }

    public String getMpcToken() {
        return mpcToken;
    }

    public void setMpcToken(String mpcToken) {
        this.mpcToken = mpcToken;
    }

    public String getMpcGroupCode() {
        return mpcGroupCode;
    }

    public void setMpcGroupCode(String mpcGroupCode) {
        this.mpcGroupCode = mpcGroupCode;
    }

    @Override
    public String toString() {
        return "OrganizationFeeDeductionEventVO{" +
                "feeDetailId='" + feeDetailId + '\'' +
                ", transactionDatetime=" + transactionDatetime +
                ", mpcToken='" + mpcToken + '\'' +
                ", mpcGroupCode='" + mpcGroupCode + '\'' +
                '}';
    }
}

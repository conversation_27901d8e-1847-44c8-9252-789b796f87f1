package com.kun.linkage.customer.facade.vo.organization;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class OrganizationCardQueryVO {

    /**
     * 主键id
     */
    private String id;

    /**
     * 开卡日期时间
     */
    private LocalDateTime openTime;

    /**
     * 机构号
     */
    private String organizationNo;

    /**
     * 客户号
     */
    private String customerId;

    /**
     * CardId
     */
    private String cardId;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 币种
     */
    private String currency;

    /**
     * 卡片状态
     */
    private String cardStatus;

    /**
     * 激活状态
     */
    private String cardActiveStatus;

    /**
     * 持卡人手机号
     */
    private String cardholderMobile;

    /**
     * 持卡人邮箱
     */
    private String cardholderEmail;

    /**
     * 持卡人姓名
     */
    private String cardholderName;

    /**
     * 可用余额
     */
    private BigDecimal availableAmount;

    public BigDecimal getAvailableAmount() {
        return availableAmount;
    }

    public void setAvailableAmount(BigDecimal availableAmount) {
        this.availableAmount = availableAmount;
    }

    public String getCardholderName() {
        return cardholderName;
    }

    public void setCardholderName(String cardholderName) {
        this.cardholderName = cardholderName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public LocalDateTime getOpenTime() {
        return openTime;
    }

    public void setOpenTime(LocalDateTime openTime) {
        this.openTime = openTime;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(String cardStatus) {
        this.cardStatus = cardStatus;
    }

    public String getCardActiveStatus() {
        return cardActiveStatus;
    }

    public void setCardActiveStatus(String cardActiveStatus) {
        this.cardActiveStatus = cardActiveStatus;
    }

    public String getCardholderMobile() {
        return cardholderMobile;
    }

    public void setCardholderMobile(String cardholderMobile) {
        this.cardholderMobile = cardholderMobile;
    }

    public String getCardholderEmail() {
        return cardholderEmail;
    }

    public void setCardholderEmail(String cardholderEmail) {
        this.cardholderEmail = cardholderEmail;
    }

    @Override
    public String toString() {
        return "OrganizationCustomerCardListQueryVO{" +
                "id=" + id +
                ", openTime=" + openTime +
                ", organizationNo='" + organizationNo + '\'' +
                ", customerId='" + customerId + '\'' +
                ", cardId='" + cardId + '\'' +
                ", cardNo='" + cardNo + '\'' +
                ", currency='" + currency + '\'' +
                ", cardStatus='" + cardStatus + '\'' +
                ", cardActiveStatus='" + cardActiveStatus + '\'' +
                ", cardholderMobile='" + cardholderMobile + '\'' +
                ", cardholderName='" + cardholderName + '\'' +
                ", availableAmount='" + availableAmount + '\'' +
                ", cardholderEmail='" + cardholderEmail + '\'' +
                '}';
    }

}

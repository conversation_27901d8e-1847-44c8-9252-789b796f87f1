package com.kun.linkage.customer.facade.vo.mq;

import com.kun.common.util.mq.BaseMqMessage;

import java.io.Serializable;
import java.math.BigDecimal;

public class CancelCardRefundBalanceEventVO extends BaseMqMessage implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * mpcToken(调用kun和payx时的Token)
     */
    private String mpcToken;
    /**
     * mpcGroupCode(调用kun和payx时的GroupProductCode)
     */
    private String mpcGroupCode;
    /**
     * 机构号
     */
    private String organizationNo;
    /**
     * 卡id
     */
    private String cardId;
    /**
     * 卡产品编号
     */
    private String cardProductCode;
    /**
     * 加锁使用的requestNo
     */
    private String lockRequestNo;
    /**
     * 客户账户号
     */
    private String customerAccountNo;
    /**
     * 客户账户余额
     */
    private BigDecimal customerAccountBalance;
    /**
     * 客户账记账流水号(重试时使用)
     */
    private String customerAccountRequestNo;
    /**
     * 客户账户币种
     */
    private String customerAccountCurrencyCode;
    /**
     * 退还机构处理方
     */
    private String refundOrganizationProcessor;
    /**
     * 退还机构金额
     */
    private BigDecimal refundOrganizationAmount;
    /**
     * 机构账记账流水号(重试时使用)
     */
    private String organizationAccountRequestNo;
    /**
     * 退还币种
     */
    private String refundCurrencyCode;
    /**
     * 换汇汇率
     */
    private BigDecimal fxRate;
    /**
     * 客户账记账标记
     */
    private Integer customerBookkeepingStatus;
    /**
     * 机构账记账标记
     */
    private Integer organizationBookkeepingStatus;
    /**
     * 调用账户扣账次数
     */
    private Integer callCount;

    public String getMpcToken() {
        return mpcToken;
    }

    public void setMpcToken(String mpcToken) {
        this.mpcToken = mpcToken;
    }

    public String getMpcGroupCode() {
        return mpcGroupCode;
    }

    public void setMpcGroupCode(String mpcGroupCode) {
        this.mpcGroupCode = mpcGroupCode;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }

    public String getLockRequestNo() {
        return lockRequestNo;
    }

    public void setLockRequestNo(String lockRequestNo) {
        this.lockRequestNo = lockRequestNo;
    }

    public String getCustomerAccountNo() {
        return customerAccountNo;
    }

    public void setCustomerAccountNo(String customerAccountNo) {
        this.customerAccountNo = customerAccountNo;
    }

    public BigDecimal getCustomerAccountBalance() {
        return customerAccountBalance;
    }

    public void setCustomerAccountBalance(BigDecimal customerAccountBalance) {
        this.customerAccountBalance = customerAccountBalance;
    }

    public String getCustomerAccountRequestNo() {
        return customerAccountRequestNo;
    }

    public void setCustomerAccountRequestNo(String customerAccountRequestNo) {
        this.customerAccountRequestNo = customerAccountRequestNo;
    }

    public String getCustomerAccountCurrencyCode() {
        return customerAccountCurrencyCode;
    }

    public void setCustomerAccountCurrencyCode(String customerAccountCurrencyCode) {
        this.customerAccountCurrencyCode = customerAccountCurrencyCode;
    }

    public String getRefundOrganizationProcessor() {
        return refundOrganizationProcessor;
    }

    public void setRefundOrganizationProcessor(String refundOrganizationProcessor) {
        this.refundOrganizationProcessor = refundOrganizationProcessor;
    }

    public BigDecimal getRefundOrganizationAmount() {
        return refundOrganizationAmount;
    }

    public void setRefundOrganizationAmount(BigDecimal refundOrganizationAmount) {
        this.refundOrganizationAmount = refundOrganizationAmount;
    }

    public String getOrganizationAccountRequestNo() {
        return organizationAccountRequestNo;
    }

    public void setOrganizationAccountRequestNo(String organizationAccountRequestNo) {
        this.organizationAccountRequestNo = organizationAccountRequestNo;
    }

    public String getRefundCurrencyCode() {
        return refundCurrencyCode;
    }

    public void setRefundCurrencyCode(String refundCurrencyCode) {
        this.refundCurrencyCode = refundCurrencyCode;
    }

    public BigDecimal getFxRate() {
        return fxRate;
    }

    public void setFxRate(BigDecimal fxRate) {
        this.fxRate = fxRate;
    }

    public Integer getCustomerBookkeepingStatus() {
        return customerBookkeepingStatus;
    }

    public void setCustomerBookkeepingStatus(Integer customerBookkeepingStatus) {
        this.customerBookkeepingStatus = customerBookkeepingStatus;
    }

    public Integer getOrganizationBookkeepingStatus() {
        return organizationBookkeepingStatus;
    }

    public void setOrganizationBookkeepingStatus(Integer organizationBookkeepingStatus) {
        this.organizationBookkeepingStatus = organizationBookkeepingStatus;
    }

    public Integer getCallCount() {
        return callCount;
    }

    public void setCallCount(Integer callCount) {
        this.callCount = callCount;
    }
}

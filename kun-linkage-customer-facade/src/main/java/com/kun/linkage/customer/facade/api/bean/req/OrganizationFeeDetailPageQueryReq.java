package com.kun.linkage.customer.facade.api.bean.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.page.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 分页查询机构手续费明细参数
 */
@Schema(name = "OrganizationFeeDetailPageQueryReq", description = "机构手续费明细分页查询参数")
public class OrganizationFeeDetailPageQueryReq extends PageParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机构号
     */
    @Schema(description = "机构号", example = "12345")
    private String organizationNo;

    /**
     * 手续费类型
     */
    @Schema(description = "手续费类型:字典:KL_ORGANIZATION_FEE_TYPE")
    private String feeType;

    /**
     * 关联交易id
     */
    @Schema(description = "关联交易id", example = "TXN123456")
    private String relatedTransactionId;

    /**
     * Fee start date
     */
    @Schema(description = "Fee start date", example = "2025-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private LocalDate feeStartDate;

    /**
     * fee end date
     */
    @Schema(description = "Fee end date", example = "2025-12-31")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private LocalDate feeEndDate;

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getRelatedTransactionId() {
        return relatedTransactionId;
    }

    public void setRelatedTransactionId(String relatedTransactionId) {
        this.relatedTransactionId = relatedTransactionId;
    }

    public LocalDate getFeeStartDate() {
        return feeStartDate;
    }

    public void setFeeStartDate(LocalDate feeStartDate) {
        this.feeStartDate = feeStartDate;
    }

    public LocalDate getFeeEndDate() {
        return feeEndDate;
    }

    public void setFeeEndDate(LocalDate feeEndDate) {
        this.feeEndDate = feeEndDate;
    }
}

package com.kun.linkage.customer.facade.api.bean.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class OrgPageQueryCardRechargeDetailRes implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "充值单号")
    private String requestNo;

    @Schema(description = "商户号")
    private String organizationNo;

    @Schema(description = "uuid")
    private String customerId;

    @Schema(description = "充值状态")
    private String rechargeStatus;

    @Schema(description = "失败信息")
    private String failMessage;

    /**
     * 卡id
     */
    @Schema(description = "卡id")
    private String cardId;
    /**
     * 充值日期时间
     */
    @Schema(description = "充值日期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime rechargeDatetime;
    /**
     * 充值金额
     */
    @Schema(description = "充值金额")
    private BigDecimal rechargeAmount;
    /**
     * 充值币种(卡币种)
     */
    @Schema(description = "充值币种(卡币种)")
    private String rechargeCurrencyCode;
    /**
     * 充值币种精度
     */
    @Schema(description = "充值币种精度")
    private Integer rechargeCurrencyPrecision;
    /**
     * 扣除金额(扣除机构的资金)
     */
    @Schema(description = "扣除金额(扣除机构的资金)")
    private BigDecimal deductAmount;
    /**
     * 扣除币种(扣除机构资金的币种)
     */
    @Schema(description = "扣除币种(扣除机构资金的币种)")
    private String deductCurrencyCode;
    /**
     * 扣除币种精度
     */
    @Schema(description = "扣除币种精度")
    private Integer deductCurrencyPrecision;

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public LocalDateTime getRechargeDatetime() {
        return rechargeDatetime;
    }

    public void setRechargeDatetime(LocalDateTime rechargeDatetime) {
        this.rechargeDatetime = rechargeDatetime;
    }

    public BigDecimal getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(BigDecimal rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public String getRechargeCurrencyCode() {
        return rechargeCurrencyCode;
    }

    public void setRechargeCurrencyCode(String rechargeCurrencyCode) {
        this.rechargeCurrencyCode = rechargeCurrencyCode;
    }

    public Integer getRechargeCurrencyPrecision() {
        return rechargeCurrencyPrecision;
    }

    public void setRechargeCurrencyPrecision(Integer rechargeCurrencyPrecision) {
        this.rechargeCurrencyPrecision = rechargeCurrencyPrecision;
    }

    public BigDecimal getDeductAmount() {
        return deductAmount;
    }

    public void setDeductAmount(BigDecimal deductAmount) {
        this.deductAmount = deductAmount;
    }

    public String getDeductCurrencyCode() {
        return deductCurrencyCode;
    }

    public void setDeductCurrencyCode(String deductCurrencyCode) {
        this.deductCurrencyCode = deductCurrencyCode;
    }

    public Integer getDeductCurrencyPrecision() {
        return deductCurrencyPrecision;
    }

    public void setDeductCurrencyPrecision(Integer deductCurrencyPrecision) {
        this.deductCurrencyPrecision = deductCurrencyPrecision;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getRechargeStatus() {
        return rechargeStatus;
    }

    public void setRechargeStatus(String rechargeStatus) {
        this.rechargeStatus = rechargeStatus;
    }

    public String getFailMessage() {
        return failMessage;
    }

    public void setFailMessage(String failMessage) {
        this.failMessage = failMessage;
    }
}

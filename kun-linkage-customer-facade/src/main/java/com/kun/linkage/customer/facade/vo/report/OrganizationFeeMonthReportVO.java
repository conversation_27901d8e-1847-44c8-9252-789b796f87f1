package com.kun.linkage.customer.facade.vo.report;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

public class OrganizationFeeMonthReportVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机构号
     */
    @ExcelProperty("商户号")
    private String organizationNo;
    /**
     * 机构名称
     */
    @ExcelProperty("商户名称")
    private String organizationName;
    /**
     * 卡产品编号
     */
    @ExcelProperty("卡产品编号")
    private String cardProductCode;
    /**
     * 手续费类型
     */
    @ExcelProperty("手续费类型")
    private String feeType;
    /**
     * 收取方式
     */
    @ExcelProperty("收取方式")
    private String feeCollectionMethod;
    /**
     * 金额范围-Min
     */
    @ExcelProperty("金额范围-Min")
    private String snapshotMinAmount;
    /**
     * 金额范围-Max
     */
    @ExcelProperty("金额范围-Max")
    private String snapshotMaxAmount;
    /**
     * 笔数
     */
    @ExcelProperty("笔数")
    private Integer totalNum;
    /**
     * 账单币种
     */
    @ExcelProperty("账单币种")
    private String transactionCurrencyCode;
    /**
     * 交易金额(总)
     */
    @ExcelProperty("交易金额(总)")
    private String totalTransactionAmount;
    /**
     * 手续费金额(总)
     */
    @ExcelProperty("手续费金额")
    private String totalFeeAmount;
    /**
     * KUN币种
     */
    @ExcelProperty("KUN币种")
    private String kunCurrencyCode;
    /**
     * 已收KUN金额
     */
    @ExcelProperty("已收KUN金额")
    private String collectedKunAmount;
    /**
     * 未收KUN金额
     */
    @ExcelProperty("未收KUN金额")
    private String notCollectedKunAmount;
    /**
     * PayX币种
     */
    @ExcelProperty("PayX币种")
    private String payXCurrencyCode;
    /**
     * 已收PayX金额
     */
    @ExcelProperty("已收PayX金额")
    private String collectedPayXAmount;
    /**
     * 未收PayX金额
     */
    @ExcelProperty("未收PayX金额")
    private String notCollectedPayXAmount;

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getFeeCollectionMethod() {
        return feeCollectionMethod;
    }

    public void setFeeCollectionMethod(String feeCollectionMethod) {
        this.feeCollectionMethod = feeCollectionMethod;
    }

    public String getSnapshotMinAmount() {
        return snapshotMinAmount;
    }

    public void setSnapshotMinAmount(String snapshotMinAmount) {
        this.snapshotMinAmount = snapshotMinAmount;
    }

    public String getSnapshotMaxAmount() {
        return snapshotMaxAmount;
    }

    public void setSnapshotMaxAmount(String snapshotMaxAmount) {
        this.snapshotMaxAmount = snapshotMaxAmount;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public String getTransactionCurrencyCode() {
        return transactionCurrencyCode;
    }

    public void setTransactionCurrencyCode(String transactionCurrencyCode) {
        this.transactionCurrencyCode = transactionCurrencyCode;
    }

    public String getTotalTransactionAmount() {
        return totalTransactionAmount;
    }

    public void setTotalTransactionAmount(String totalTransactionAmount) {
        this.totalTransactionAmount = totalTransactionAmount;
    }

    public String getTotalFeeAmount() {
        return totalFeeAmount;
    }

    public void setTotalFeeAmount(String totalFeeAmount) {
        this.totalFeeAmount = totalFeeAmount;
    }

    public String getKunCurrencyCode() {
        return kunCurrencyCode;
    }

    public void setKunCurrencyCode(String kunCurrencyCode) {
        this.kunCurrencyCode = kunCurrencyCode;
    }

    public String getCollectedKunAmount() {
        return collectedKunAmount;
    }

    public void setCollectedKunAmount(String collectedKunAmount) {
        this.collectedKunAmount = collectedKunAmount;
    }

    public String getNotCollectedKunAmount() {
        return notCollectedKunAmount;
    }

    public void setNotCollectedKunAmount(String notCollectedKunAmount) {
        this.notCollectedKunAmount = notCollectedKunAmount;
    }

    public String getPayXCurrencyCode() {
        return payXCurrencyCode;
    }

    public void setPayXCurrencyCode(String payXCurrencyCode) {
        this.payXCurrencyCode = payXCurrencyCode;
    }

    public String getCollectedPayXAmount() {
        return collectedPayXAmount;
    }

    public void setCollectedPayXAmount(String collectedPayXAmount) {
        this.collectedPayXAmount = collectedPayXAmount;
    }

    public String getNotCollectedPayXAmount() {
        return notCollectedPayXAmount;
    }

    public void setNotCollectedPayXAmount(String notCollectedPayXAmount) {
        this.notCollectedPayXAmount = notCollectedPayXAmount;
    }
}

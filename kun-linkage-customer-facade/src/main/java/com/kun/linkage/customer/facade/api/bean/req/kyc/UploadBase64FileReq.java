package com.kun.linkage.customer.facade.api.bean.req.kyc;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class UploadBase64FileReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "客户号")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String customerId;

    @Schema(description = "机构号")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String organizationNo;

    @Schema(description = "文件名称,一定要带文件类型后缀")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String filename;

    @Schema(description = "base64文件")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String base64File;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getBase64File() {
        return base64File;
    }

    public void setBase64File(String base64File) {
        this.base64File = base64File;
    }
}

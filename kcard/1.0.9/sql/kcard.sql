-- 交易主表增加卡余额字段
alter table kc_trans_main
    add column card_balance decimal(30,15) null comment '卡余额' AFTER global_trans_code;

-- 系统Mcc配置表新增预授权状态
alter table kc_mcc_info
    add column pre_auth_status varchar(12) default 'DISABLED' not null comment '预授权状态' after status;


-- 冻结主表
create table kc_freeze_main_info
(
    id                bigint auto_increment comment '自增主键 id'
        primary key,
    channel           varchar(12)     not null comment '通道类型',
    order_no          varchar(64)     not null comment '关联订单号（订单唯一编号）',
    card_id           varchar(64)     not null comment 'cardId',
    freeze_type       char            not null comment '冻结类型（P-普通授权、Y-预授权）',
    freeze_start_time datetime        not null comment '冻结开始时间',
    freeze_end_time   datetime        not null comment '冻结结束时间',
    ccy               char(3)         not null comment '冻结币种',
    total_amt         decimal(30, 15) not null comment '冻结总金额',
    unfreeze_amt      decimal(30, 15) not null comment '解冻金额',
    remain_frozen_amt decimal(30, 15) not null comment '剩余冻结金额',
    cleared_time      datetime        null comment '清算日期',
    status varchar(12) default 'INIT' not null comment '状态 INIT-初始化 PROCESS-人工审核中 SUCC-已完成',
    rmk               varchar(128)    null comment '备注',
    create_time       datetime        not null comment '创建时间',
    update_time       datetime        null comment '修改时间',
    KEY idx_freeze_end_time (freeze_end_time),
    KEY idx_order_no (order_no)
)
    comment '冻结主表';

-- 冻结明细表
create table kc_freeze_details_info
(
    id           bigint auto_increment comment '自增主键 id'
        primary key,
    freeze_id    bigint          not null comment '冻结主表ID',
    card_id      varchar(64)     not null comment 'cardId',
    order_no     varchar(64)     not null comment '交易关联流水号',
    execute_type varchar(12)     not null comment '执行类型（冻结、解冻） F 冻结 U 解冻',
    execute_time datetime        not null comment '执行时间',
    ccy          char(3)         not null comment '执行币种',
    amt          decimal(30, 15) not null comment '执行金额',
    rmk          varchar(128)    null comment '备注',
    create_time  datetime        not null comment '创建时间',
    KEY idx_freeze_id (freeze_id)
)
    comment '冻结明细表';

-- 交易审核信息表
create table kc_transaction_review_info
(
    id                      bigint auto_increment comment '主键id'
        primary key,
    transaction_id          varchar(128)    not null comment '交易编码',
    business_association_id bigint          null comment '业务关联流水号',
    member_name             varchar(32)     null comment '商户名称',
    member_id               bigint          not null comment '商户id',
    card_id                 varchar(64)     not null comment '卡id',
    card_channel            varchar(16)     not null comment '卡片渠道',
    review_type             varchar(32)     not null comment '审核类型',
    review_status           varchar(16)     null comment '审核状态',
    review_remark           varchar(255)    null comment '审核备注',
    operation_type          varchar(16)     null comment '操作类型DEDUCTED-扣款，REFUND-退款',
    auth_type               varchar(16)     null comment '预授权类型:Auth,Pre-auth',
    pending_amount          decimal(30, 15) not null comment '待处理金额',
    bill_currency           varchar(3)      not null comment '账单币种',
    merchant_id             varchar(64)     null comment '交易商户ID',
    mcc_code                varchar(16)     null comment '商户类别代码 (MCC)，用于描述商户的行业分类',
    merchant_country        varchar(6)      null comment '商户所在国家',
    trans_time              datetime        null comment '交易时间',
    time_zone               varchar(24)     null comment '交易时区',
    response_code           varchar(32)     null comment '响应码',
    description             varchar(255)    null comment '失败原因',
    global_trans_code       varchar(128)    not null comment '全局流水号',
    create_time             datetime        null comment '创建时间',
    create_user             varchar(64)     null comment '创建人',
    update_time             datetime        null comment '更新时间',
    update_user             varchar(64)     null comment '更新人',
    constraint kc_transaction_review_info_id_uindex
        unique (id)
)
    comment '交易审核信息表';

-- 扩充数据字典表
alter table kc_dict
    modify dict_value varchar(255) not null comment 'value 值';

-- 增加交易审核类型字典
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-CN', 'REVIEW_TYPE', 'ABNORMAL_CLEARING_AMT', '清算金额<授权金额', NULL, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-TW', 'REVIEW_TYPE', 'ABNORMAL_CLEARING_AMT', '清算金額<授權金額', NULL, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('en', 'REVIEW_TYPE', 'ABNORMAL_CLEARING_AMT', 'The liquidation amount is less than the authorized amount', null, 1);

INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-CN', 'REVIEW_TYPE', 'CLEARING_FAIL', '清算失败交易', NULL, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-TW', 'REVIEW_TYPE', 'CLEARING_FAIL', '清算失敗交易', NULL, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('en', 'REVIEW_TYPE', 'CLEARING_FAIL', 'Clearing failed transactions', null, 1);

INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-CN', 'REVIEW_TYPE', 'UNAUTHORIZED_REFUND', '未匹配到授权订单', NULL, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-TW', 'REVIEW_TYPE', 'UNAUTHORIZED_REFUND', '未匹配到授權訂單', NULL, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('en', 'REVIEW_TYPE', 'UNAUTHORIZED_REFUND', 'Unauthorized order', null, 1);

INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-CN', 'REVIEW_TYPE', 'REFUND_FAIL', '退款失败交易', NULL, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-TW', 'REVIEW_TYPE', 'REFUND_FAIL', '退款失敗交易', NULL, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('en', 'REVIEW_TYPE', 'REFUND_FAIL', 'Refund failed transaction', null, 1);

INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-CN', 'REVIEW_TYPE', 'DEDUCTION_FAIL', '扣款失败交易', NULL, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-TW', 'REVIEW_TYPE', 'DEDUCTION_FAIL', '扣款失敗交易', NULL, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('en', 'REVIEW_TYPE', 'DEDUCTION_FAIL', 'Deduction failed transaction', null, 1);

INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-CN', 'EXPORT_FILE_TYPE', 'TRANSACTION_REVIEW', '交易审核记录', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-TW', 'EXPORT_FILE_TYPE', 'TRANSACTION_REVIEW', '交易審核記錄', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('en', 'EXPORT_FILE_TYPE', 'TRANSACTION_REVIEW', 'Transaction Review Record', null, 1);

INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-CN', 'TRANSACTION_TYPE', 'FUND_DEDUCTION', '资金补扣', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-TW', 'TRANSACTION_TYPE', 'FUND_DEDUCTION', '資金補扣', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('en', 'TRANSACTION_TYPE', 'FUND_DEDUCTION', 'Supplement Of Funds', null, 1);

-- 新增菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, domain) VALUES (100257, '修改MCC预授权状态', 100044, 11, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'mcc:pre:auth:stauts', '#', '<EMAIL>', now(), '', NULL, '', 'IOP');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, domain) VALUES (100269, '审核', 100261, 1, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'trans:exception:second:review', '#', '<EMAIL>', now(), '', NULL, '', 'IOP');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, domain) VALUES (100268, '详情', 100263, 2, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'trans:exception:second:info', '#', '<EMAIL>', now(), '', NULL, '', 'IOP');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, domain) VALUES (100267, '详情', 100262, 2, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'trans:exception:first:info', '#', '<EMAIL>', now(), '', NULL, '', 'IOP');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, domain) VALUES (100266, '审核', 100259, 1, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'trans:exception:first:review', '#', '<EMAIL>', now(), '', NULL, '', 'IOP');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, domain) VALUES (100265, '数据导出', 100260, 3, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'exception:second:processed:export', '#', '<EMAIL>', now(), '<EMAIL>', NULL, '', 'IOP');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, domain) VALUES (100264, '数据导出', 100258, 3, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'exception:processed:export', '#', '<EMAIL>', now(), '<EMAIL>', NULL, '', 'IOP');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, domain) VALUES (100263, '已处理', 100260, 2, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'exception:seconde:processed', '#', '<EMAIL>', now() , '', NULL, '', 'IOP');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, domain) VALUES (100262, '已处理', 100258, 2, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'exception:first:processed', '#', '<EMAIL>', now(), '', NULL, '', 'IOP');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, domain) VALUES (100261, '待处理', 100260, 1, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'exception:seconde:wait', '#', '<EMAIL>', now(), '', NULL, '', 'IOP');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, domain) VALUES (100260, '交易异常审核', 100141, 5, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'adopt:trans:exception:seconde:query', '#', '<EMAIL>', now(), '', NULL, '', 'IOP');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, domain) VALUES (100259, '待处理', 100258, 1, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'exception:first:wait', '#', '<EMAIL>', now() , '', NULL, '', 'IOP');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, domain) VALUES (100258, '交易异常审核', 100137, 5, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'adopt:trans:exception:first:query', '#', '<EMAIL>', now(), '', NULL, '', 'IOP');
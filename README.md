# kun-linkage-clearing

## 项目概述

kun-linkage-clearing 是坤联支付系统中的清分结算服务模块，负责处理交易清分、结算、授权释放等核心业务功能。该服务是支付链路中的重要环节，确保交易资金的安全、准确结算。

## 项目架构

```
kun-linkage-clearing/
├── kun-linkage-clearing-facade/     # 对外接口层
│   ├── src/main/java/
│   │   └── com/kun/linkage/clearing/facade/
│   │       ├── api/                 # API接口定义
│   │       ├── vo/                  # 值对象
│   │       ├── constant/            # 常量定义
│   │       └── config/              # 配置类
└── kun-linkage-clearing-service/    # 业务服务层
    ├── src/main/java/
    │   └── com/kun/linkage/clearing/
    │       ├── controller/          # 控制器层
    │       ├── service/             # 业务服务层
    │       ├── task/                # 定时任务
    │       ├── listener/            # 事件监听器
    │       ├── utils/               # 工具类
    │       ├── dto/                 # 数据传输对象
    │       ├── vo/                  # 视图对象
    │       ├── enums/               # 枚举类
    │       ├── constant/            # 常量类
    │       ├── config/              # 配置类
    │       ├── annotation/          # 注解类
    │       └── ext/                 # 扩展类
    └── src/main/resources/
        ├── mapper/                  # MyBatis映射文件
        ├── bootstrap-local.yml      # 本地环境配置
        ├── bootstrap-dev.yml        # 开发环境配置
        └── bootstrap-nc.yml         # 生产环境配置
```

## 核心功能模块

### 1. 清分处理 (KLClearingController)
- **接口路径**: `/api/v1/kl-clearing/process`
- **功能**: 处理交易清分请求
- **主要职责**:
  - 接收清分请求
  - 验证交易数据
  - 执行清分逻辑
  - 返回清分结果

### 2. 授权释放 (AuthReleaseController)
- **接口路径**: `/api/v1/authExpire/release`
- **功能**: 处理授权释放请求
- **主要职责**:
  - 验证授权释放参数
  - 执行授权释放逻辑
  - 更新授权状态
  - 返回处理结果

### 3. 清分查询 (KLClearingQueryController)
- **功能**: 提供清分数据查询服务
- **主要职责**:
  - 查询清分记录
  - 统计清分数据
  - 导出清分报表

### 4. 异常处理 (KLClearingExceptionController)
- **功能**: 处理清分异常情况
- **主要职责**:
  - 异常记录管理
  - 异常处理流程
  - 异常通知机制

### 5. 定时任务 (Task)
- **VisaBaseFileTask**: Visa基础文件处理任务
- **功能**: 定期处理Visa相关的基础文件

## 技术栈

### 核心框架
- **Spring Boot**: 2.7.x
- **Spring Cloud**: 2021.0.x
- **Spring Cloud Alibaba**: 2021.0.x

### 微服务组件
- **Nacos**: 服务注册与配置中心
- **Sentinel**: 流量控制与熔断
- **LoadBalancer**: 负载均衡

### 数据存储
- **MySQL**: 主数据库
- **Redis**: 缓存数据库
- **ShardingSphere**: 数据库分片

### 消息队列
- **RocketMQ**: 异步消息处理

### 任务调度
- **XXL-Job**: 分布式任务调度

### 其他组件
- **Undertow**: Web服务器
- **MyBatis-Plus**: ORM框架
- **Druid**: 数据库连接池
- **SpringDoc**: API文档

## 数据库设计

### 分片策略
项目采用ShardingSphere进行数据库分片，主要分片策略包括：

1. **按机构号分片**: 客户卡片信息相关表
   - `kl_organization_customer_card_operation_record`
   - `kl_organization_customer_card_info`
   - `kl_organization_customer_account_info`

2. **按时间分片**: 交易流水相关表
   - `kl_clearing_trans` (按月分片)
   - `kl_auth_flow` (按月分片)
   - `kl_auth_flow_ext` (按月分片)
   - `kl_auth_account_log` (按月分片)

3. **按季度分片**: 清分信息表
   - `kc_clearing_info` (按年季度分片)

## 环境配置

### 开发环境 (local)
- **端口**: 9020
- **数据库**: mysql.qa.kun:3306
- **Redis**: redis.qa.kun:6379
- **Nacos**: 10.171.3.91:8848

### 配置说明
- **服务注册**: 使用Nacos进行服务注册与发现
- **配置中心**: 使用Nacos进行配置管理
- **数据库连接**: 使用Druid连接池，支持加密配置
- **缓存**: 使用Redis进行数据缓存

## API接口

### 1. 清分处理接口
```http
POST /api/v1/kl-clearing/process
Content-Type: application/json

{
  "requestId": "string",
  "transType": "string",
  "amount": "number",
  "currency": "string"
}
```

### 2. 授权释放接口
```http
POST /api/v1/authExpire/release
Content-Type: application/json

{
  "requestId": "string",
  "processor": "string",
  "mid": "string",
  "processorCardId": "string",
  "originalTransType": "string",
  "originalProcessorTransId": "string",
  "ccy": "string",
  "amt": "number",
  "cardholderBillingCurrency": "string",
  "cardholderBillingAmount": "number",
  "cardholderMarkupBillingAmount": "number"
}
```

## 部署说明

### Docker部署
项目支持Docker容器化部署，相关文件位于 `docker/` 目录：
- `Dockerfile`: 容器构建文件
- `dockerfile-part`: 部分构建配置
- `gracefulstop.sh`: 优雅停止脚本

### 构建命令
```bash
# 编译项目
mvn clean compile

# 打包项目
mvn clean package

# 运行项目
java -jar kun-linkage-clearing-service/target/kun-linkage-clearing.jar
```

## 监控与运维

### 健康检查
- **Actuator端点**: `/actuator/health`
- **应用信息**: `/actuator/info`

### 日志管理
- **日志级别**: 可配置的日志级别
- **日志文件**: 按日期滚动存储
- **错误日志**: 单独的错误日志文件

### 性能监控
- **数据库监控**: Druid监控面板
- **应用监控**: Spring Boot Actuator
- **链路追踪**: Spring Cloud Sleuth

## 开发指南

### 本地开发环境搭建
1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd kun-linkage-clearing
   ```

2. **配置环境**
   - 确保MySQL、Redis、Nacos服务可用
   - 修改 `bootstrap-local.yml` 中的连接配置

3. **启动应用**
   ```bash
   mvn spring-boot:run -pl kun-linkage-clearing-service
   ```

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用统一的异常处理机制
- 保持代码注释的完整性
- 遵循RESTful API设计规范

### 测试
- **单元测试**: 使用JUnit进行单元测试
- **集成测试**: 使用Spring Boot Test进行集成测试
- **接口测试**: 使用Postman等工具进行接口测试

## 版本信息

- **当前版本**: 1.0.0-SNAPSHOT
- **Java版本**: 1.8
- **Spring Boot版本**: 2.7.x
- **Spring Cloud版本**: 2021.0.x

## 联系方式

如有问题或建议，请联系开发团队或提交Issue。

---

**注意**: 本项目为内部项目，请勿对外公开敏感信息。

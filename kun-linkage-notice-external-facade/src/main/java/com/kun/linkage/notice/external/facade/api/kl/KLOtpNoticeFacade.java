package com.kun.linkage.notice.external.facade.api.kl;

import com.kun.linkage.common.base.Result;
import com.kun.linkage.notice.external.facade.api.OtpNoticeBaseReq;
import com.kun.linkage.notice.external.facade.api.OtpNoticeBaseRes;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "kun-linkage-customer", path = "/linkage-customer/api/otp")
public interface KLOtpNoticeFacade {

    /**
     * opt 通知转发，调用成功直接返回，不需要等待短信，邮件的通知
     * @param otpNoticeBaseReq 请求数据
     * @return 返回发送邮件或者短信，以及发送的手机号或者邮箱
     */
    @PostMapping("/notice")
    public Result<OtpNoticeBaseRes> optNotice(@RequestBody OtpNoticeBaseReq otpNoticeBaseReq);
}

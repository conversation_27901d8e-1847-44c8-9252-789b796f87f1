package com.kun.linkage.notice.external.facade.api;

import java.io.Serializable;

public class OtpNoticeBaseRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 1:Sms;2:Email
     */
    private Integer method;
    /**
     * 接收方;接收方手机号或邮箱
     */
    private String destination;

    public Integer getMethod() {
        return method;
    }

    public void setMethod(Integer method) {
        this.method = method;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }
}

package com.kun.linkage.facade.enums;

public enum MessageTypeEnum {
    SMS("SMS",2 ,"短信"),
    EMAIL("Email", 1,"邮件");

    private final String code;
    private final Integer type;
    private final String description;


    MessageTypeEnum(String code,Integer type, String description) {
        this.code = code;
        this.type = type;
        this.description = description;
    }

    public String getCode() {
        return code;
    }
    public Integer getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    // 根据 code 获取枚举实例
    public static MessageTypeEnum fromCode(String code) {
        for (MessageTypeEnum type : MessageTypeEnum.values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
      return null;
    }

}

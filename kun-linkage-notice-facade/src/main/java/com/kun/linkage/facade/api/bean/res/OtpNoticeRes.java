package com.kun.linkage.facade.api.bean.res;

import java.io.Serializable;

public class OtpNoticeRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 请求编号
     */
    private String requestId;
    /**
     * 返回的code
     */
    private String returnCode;
    /**
     * 错误消息描述
     */
    private String errorMessage;
    /**
     * 返回的通知
     */
    private OtpNoticeResData data;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public OtpNoticeResData getData() {
        return data;
    }

    public void setData(OtpNoticeResData data) {
        this.data = data;
    }
}

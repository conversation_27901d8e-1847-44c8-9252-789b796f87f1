package com.kun.linkage.facade.enums;

/**
 * 来源系统枚举
 */
public enum SystemEnum {

    /**
     * VCC
     */
    VCC("VCC"),
    /**
     * KUN_LINKAGE
     */
    KL("KL"),
    /**
     * UP
     */
    UP("UP");

    /**
     * 值
     */
    private final String value;

    SystemEnum(String value) {
        this.value = value;
    }




    public static SystemEnum getEnumByValue(String value) {
        for (SystemEnum systemEnum : SystemEnum.values()) {
            if (systemEnum.getValue().equals(value)) {
                return systemEnum;
            }
        }
        return null;
    }

    public static boolean contains(String value) {
        return getEnumByValue(value) != null;
    }

    public String getValue() {
        return value;
    }
}

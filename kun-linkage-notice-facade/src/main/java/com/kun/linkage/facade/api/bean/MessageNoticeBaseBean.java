package com.kun.linkage.facade.api.bean;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.facade.enums.SystemEnum;
import com.kun.linkage.facade.enums.TemplateLanguageEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

public class MessageNoticeBaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 来源；对应枚举 SystemEnum VCC;KL;UP
     */
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Schema(description = "来源；VCC;KL;UP")
    private String sourceFrom;

    /**
     * 模板编号
     */
    @Schema(description = "模板编号")
    private String templateNo;

    /**
     * 请求流水
     */
    @Schema(description = "请求流水")
    private String requestNo;

    /**
     * 外部请求编号
     */
    @Schema(description = "外部请求编号,关联编号")
    private String externalNo;

    /**
     * 模板语言; 默认 'EN'，支持 'EN', 'ZH'
     */
    @Schema(description = "模板语言; 默认 'EN'，支持 'EN', 'ZH'")
    private String templateLanguage = "EN";

    public String getSourceFrom() {
        return sourceFrom;
    }

    public void setSourceFrom(String sourceFrom) {
        this.sourceFrom = sourceFrom;
    }

    public String getTemplateNo() {
        return templateNo;
    }

    public void setTemplateNo(String templateNo) {
        this.templateNo = templateNo;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getExternalNo() {
        return externalNo;
    }

    public void setExternalNo(String externalNo) {
        this.externalNo = externalNo;
    }

    public String getTemplateLanguage() {
        return templateLanguage;
    }

    public void setTemplateLanguage(String templateLanguage) {
        this.templateLanguage = templateLanguage;
    }
}

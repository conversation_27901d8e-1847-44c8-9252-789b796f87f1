package com.kun.linkage.facade.api;

import com.kun.linkage.common.base.config.FeignConfiguration;
import com.kun.linkage.facade.api.bean.req.OtpNoticeReq;
import com.kun.linkage.facade.api.bean.res.OtpNoticeRes;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "kun-linkage-notice", path = "/api/notice", configuration = FeignConfiguration.class)
public interface NoticeFacade {

    @PostMapping("/opt")
    public OtpNoticeRes optNotice(@RequestBody OtpNoticeReq otpNoticeReq);
}

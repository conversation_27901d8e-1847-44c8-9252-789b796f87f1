package com.kun.linkage.facade.enums;

public enum SendStatusEnum {

    SUCCESS("SUCCESS", "成功"),
    FAILED("FAILED", "失败");

    private final String code;
    private final String description;

    // 构造函数
    SendStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // 获取枚举的值
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}

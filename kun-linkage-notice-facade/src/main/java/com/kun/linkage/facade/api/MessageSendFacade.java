package com.kun.linkage.facade.api;

import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.config.FeignConfiguration;
import com.kun.linkage.facade.api.bean.req.SenEmailReq;
import com.kun.linkage.facade.api.bean.req.SenSmsReq;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "kun-linkage-notice", path = "/api/send", configuration = FeignConfiguration.class)
public interface MessageSendFacade {

    @Operation(description = "发送短信")
    @PostMapping("/sendSms")
    Result<Void> sendSms(@RequestBody @Validated SenSmsReq senSmsReq);

    @Operation(description = "发送邮件")
    @PostMapping("/sendEmail")
    Result<Void> sendEmail(@RequestBody @Validated SenEmailReq senEmailReq);

}

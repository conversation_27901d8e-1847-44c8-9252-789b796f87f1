package com.kun.linkage.facade.api.bean.req;

import java.io.Serializable;

public class SendEmailParam implements Serializable {

    private static final long serialVersionUID = 1L;

        /**
         * 邮箱账号
         */
        private String account;

        /**
         * 邮箱密码
         */
        private String password;

        /**
         * 邮箱服务器
         */
        private String host;

        /**
         * 邮箱是否开启SSL
         */
        private Boolean sslEnable;

        /**
         * 邮箱端口
         */
        private Integer port;

        public String getAccount() {
            return account;
        }

        public void setAccount(String account) {
            this.account = account;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public Boolean getSslEnable() {
            return sslEnable;
        }

        public void setSslEnable(Boolean sslEnable) {
            this.sslEnable = sslEnable;
        }

        public Integer getPort() {
            return port;
        }

        public void setPort(Integer port) {
            this.port = port;
        }
}

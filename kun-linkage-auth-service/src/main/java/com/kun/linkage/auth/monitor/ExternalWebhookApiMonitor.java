package com.kun.linkage.auth.monitor;

import com.kun.linkage.auth.config.ExternalWebhookApiProperties;
import com.kun.linkage.auth.utils.StopWatchUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 外部接口调用监控
 * 
 * 用于收集和监控外部接口调用的性能指标
 */
@Slf4j
@Component
public class ExternalWebhookApiMonitor {

    @Resource
    private ExternalWebhookApiProperties externalApiProperties;

    @Resource
    private RedisMonitorStorage redisMonitorStorage;

    // 日期格式化器
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 记录接口调用开始
     * 
     * @param url 接口地址
     * @return StopWatch实例
     */
    public StopWatch recordCallStart(String url) {
        if (!externalApiProperties.getMonitoring().isEnabled()) {
            return null;
        }
        
        return StopWatchUtil.createAndStartForApi(url);
    }

    /**
     * 记录接口调用成功
     *
     * @param organizationNo
     * @param url            接口地址
     * @param stopWatch      StopWatch实例
     */
    public void recordCallSuccess(String organizationNo, String url, StopWatch stopWatch) {
        if (!externalApiProperties.getMonitoring().isEnabled() || stopWatch == null) {
            return;
        }
        
        long duration = StopWatchUtil.stopAndGetTotalTime(stopWatch);
        StopWatchUtil.stopAndLog(stopWatch, url, true);
        
        // 存储到Redis
        try {
            // 存储监控事件
            RedisMonitorStorage.MonitorEvent event = new RedisMonitorStorage.MonitorEvent(
                    url, organizationNo, "SUCCESS", duration, null);
            redisMonitorStorage.storeMonitorEvent(event);
            
            // 使用原子操作更新统计信息
            redisMonitorStorage.atomicUpdateApiCallStats(url, organizationNo, duration, true);
            
            // 存储每日统计信息
            String today = LocalDateTime.now().format(DATE_FORMATTER);
            ApiCallStats stats = getApiCallStats(url);
            if (stats != null) {
                redisMonitorStorage.storeDailyStats(today, url, stats);
            }
        } catch (Exception e) {
            log.error("存储监控数据到Redis失败: {}", url, e);
        }
        
        // 慢查询日志
        if (externalApiProperties.getMonitoring().isLogSlowQueries() && 
            duration > externalApiProperties.getMonitoring().getSlowQueryThreshold()) {
            log.warn("慢查询监控: 接口 {} 调用耗时 {}ms, 超过阈值 {}ms", 
                    url, duration, externalApiProperties.getMonitoring().getSlowQueryThreshold());
            
            // 存储慢查询事件
            try {
                RedisMonitorStorage.MonitorEvent slowEvent = new RedisMonitorStorage.MonitorEvent(
                        url, null, "SLOW_QUERY", duration, "超过阈值" + externalApiProperties.getMonitoring().getSlowQueryThreshold() + "ms");
                redisMonitorStorage.storeMonitorEvent(slowEvent);
            } catch (Exception e) {
                log.error("存储慢查询事件到Redis失败: {}", url, e);
            }
        }
    }

    /**
     * 记录接口调用失败
     *
     * @param organizationNo
     * @param url            接口地址
     * @param stopWatch      StopWatch实例
     * @param error          错误信息
     */
    public void recordCallFailure(String organizationNo, String url, StopWatch stopWatch, String error) {
        if (!externalApiProperties.getMonitoring().isEnabled() || stopWatch == null) {
            return;
        }
        
        long duration = StopWatchUtil.stopAndGetTotalTime(stopWatch);
        StopWatchUtil.stopAndLog(stopWatch, url, false);
        
        // 存储到Redis
        try {
            // 存储失败事件
            RedisMonitorStorage.MonitorEvent event = new RedisMonitorStorage.MonitorEvent(
                    url, null, "FAILURE", duration, error);
            redisMonitorStorage.storeMonitorEvent(event);
            
            // 使用原子操作更新统计信息
            redisMonitorStorage.atomicUpdateApiCallStats(url, organizationNo,duration, false);
            
            // 存储每日统计信息
            String today = LocalDateTime.now().format(DATE_FORMATTER);
            ApiCallStats stats = getApiCallStats(url);
            if (stats != null) {
                redisMonitorStorage.storeDailyStats(today, url, stats);
            }
        } catch (Exception e) {
            log.error("存储失败监控数据到Redis失败: {}", url, e);
        }
        
        log.error("接口调用失败监控: 接口 {} 调用失败, 耗时 {}ms, 错误: {}", url, duration, error);
    }

    /**
     * 获取接口调用统计信息
     * 
     * @param url 接口地址
     * @return 统计信息
     */
    public ApiCallStats getApiCallStats(String url) {
        return getApiCallStats(url, null);
    }

    /**
     * 获取接口调用统计信息（带机构号）
     * 
     * @param url 接口地址
     * @param orgCode 机构号
     * @return 统计信息
     */
    public ApiCallStats getApiCallStats(String url, String orgCode) {
        // 从Redis原子统计获取
        ApiCallStats atomicStats = redisMonitorStorage.getAtomicApiCallStats(url, orgCode);
        if (atomicStats != null) {
            return atomicStats;
        }
        
        // 如果没有数据，返回空统计
        return ApiCallStats.builder()
                .url(url)
                .orgCode(orgCode)
                .totalCalls(0)
                .successCalls(0)
                .failureCalls(0)
                .successRate(0)
                .avgTime(0)
                .maxTime(0)
                .minTime(0)
                .totalTime(0)
                .build();
    }

    /**
     * 获取所有接口的统计信息
     * 
     * @return 所有接口的统计信息
     */
    public ConcurrentHashMap<String, ApiCallStats> getAllApiCallStats() {
        // 从Redis获取所有统计数据
        // 这里返回空Map，实际数据通过Redis存储
        return new ConcurrentHashMap<>();
    }

    /**
     * 重置统计信息
     */
    public void resetStats() {
        try {
            redisMonitorStorage.cleanupExpiredData();
            log.info("外部接口调用统计信息已重置，Redis监控数据已清理");
        } catch (Exception e) {
            log.error("清理Redis监控数据失败", e);
        }
    }

    /**
     * 接口调用统计信息
     */
    public static class ApiCallStats {
        private String url;
        private String orgCode; // 机构号
        private long totalCalls;
        private long successCalls;
        private long failureCalls;
        private double successRate;
        private double avgTime;
        private long maxTime;
        private long minTime;
        private long totalTime;

        // Builder模式
        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private ApiCallStats stats = new ApiCallStats();

            public Builder url(String url) {
                stats.url = url;
                return this;
            }

            public Builder orgCode(String orgCode) {
                stats.orgCode = orgCode;
                return this;
            }

            public Builder totalCalls(long totalCalls) {
                stats.totalCalls = totalCalls;
                return this;
            }

            public Builder successCalls(long successCalls) {
                stats.successCalls = successCalls;
                return this;
            }

            public Builder failureCalls(long failureCalls) {
                stats.failureCalls = failureCalls;
                return this;
            }

            public Builder successRate(double successRate) {
                stats.successRate = successRate;
                return this;
            }

            public Builder avgTime(double avgTime) {
                stats.avgTime = avgTime;
                return this;
            }

            public Builder maxTime(long maxTime) {
                stats.maxTime = maxTime;
                return this;
            }

            public Builder minTime(long minTime) {
                stats.minTime = minTime;
                return this;
            }

            public Builder totalTime(long totalTime) {
                stats.totalTime = totalTime;
                return this;
            }

            public ApiCallStats build() {
                return stats;
            }
        }

        // Getters
        public String getUrl() { return url; }
        public String getOrgCode() { return orgCode; }
        public long getTotalCalls() { return totalCalls; }
        public long getSuccessCalls() { return successCalls; }
        public long getFailureCalls() { return failureCalls; }
        public double getSuccessRate() { return successRate; }
        public double getAvgTime() { return avgTime; }
        public long getMaxTime() { return maxTime; }
        public long getMinTime() { return minTime; }
        public long getTotalTime() { return totalTime; }
    }
} 
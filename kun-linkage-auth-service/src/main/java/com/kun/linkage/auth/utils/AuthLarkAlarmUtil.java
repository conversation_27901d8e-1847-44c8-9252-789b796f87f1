package com.kun.linkage.auth.utils;

import com.kun.common.util.lark.LarkAlarmUtil;
import com.kun.linkage.auth.dto.AuthTransactionContextDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 授权服务Lark告警工具类
 * <p>
 * 提供统一的告警消息格式和发送功能，专门用于授权交易相关的告警通知
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class AuthLarkAlarmUtil {

    @Resource
    private LarkAlarmUtil larkAlarmUtil;

    /**
     * 发送交易手续费处理异常告警
     *
     * @param authTransactionContextDTO 交易上下文
     * @param errorMessage 错误信息
     * @param errorType 错误类型
     * @param retryTimes 重试次数
     */
    public void sendTransactionFeeAlarm(AuthTransactionContextDTO authTransactionContextDTO, 
                                       String errorMessage, String errorType, int retryTimes) {
        try {
            String organizationNo = getOrganizationNo(authTransactionContextDTO);
            String cardId = getCardId(authTransactionContextDTO);
            String transAmount = getTransAmount(authTransactionContextDTO);
            String transCurrency = getTransCurrency(authTransactionContextDTO);

            String msg = String.format(
                "【授权交易手续费处理异常】\n" +
                "机构号: %s\n" +
                "卡号: %s\n" +
                "交易ID: %s\n" +
                "交易金额: %s %s\n" +
                "错误类型: %s\n" +
                "错误信息: %s\n" +
                "重试次数: %d\n" +
                "处理时间: %s",
                organizationNo,
                cardId,
                authTransactionContextDTO.getTransactionId(),
                transAmount,
                transCurrency,
                errorType,
                errorMessage,
                retryTimes,
                java.time.LocalDateTime.now().toString()
            );

            larkAlarmUtil.sendTextAlarm(msg);
            log.info("发送交易手续费处理异常告警成功, transactionId: {}", 
                authTransactionContextDTO.getTransactionId());

        } catch (Exception e) {
            log.error("发送交易手续费处理异常告警失败, transactionId: {}, error: {}",
                authTransactionContextDTO.getTransactionId(), e.getMessage(), e);
        }
    }

    /**
     * 发送记账冲正异常告警
     *
     * @param authTransactionContextDTO 交易上下文
     * @param errorMessage 错误信息
     * @param errorType 错误类型
     * @param retryTimes 重试次数
     */
    public void sendAccountingReversalAlarm(AuthTransactionContextDTO authTransactionContextDTO, 
                                          String errorMessage, String errorType, int retryTimes) {
        try {
            String organizationNo = getOrganizationNo(authTransactionContextDTO);
            String cardId = getCardId(authTransactionContextDTO);
            String transAmount = getTransAmount(authTransactionContextDTO);
            String transCurrency = getTransCurrency(authTransactionContextDTO);

            String msg = String.format(
                "【授权交易记账冲正异常】\n" +
                "机构号: %s\n" +
                "卡号: %s\n" +
                "交易ID: %s\n" +
                "交易金额: %s %s\n" +
                "错误类型: %s\n" +
                "错误信息: %s\n" +
                "重试次数: %d\n" +
                "处理时间: %s",
                organizationNo,
                cardId,
                authTransactionContextDTO.getTransactionId(),
                transAmount,
                transCurrency,
                errorType,
                errorMessage,
                retryTimes,
                java.time.LocalDateTime.now().toString()
            );

            larkAlarmUtil.sendTextAlarm(msg);
            log.info("发送记账冲正异常告警成功, transactionId: {}", 
                authTransactionContextDTO.getTransactionId());

        } catch (Exception e) {
            log.error("发送记账冲正异常告警失败, transactionId: {}, error: {}",
                authTransactionContextDTO.getTransactionId(), e.getMessage(), e);
        }
    }

    /**
     * 发送通用授权交易异常告警
     *
     * @param title 告警标题
     * @param authTransactionContextDTO 交易上下文
     * @param errorMessage 错误信息
     * @param additionalInfo 附加信息
     */
    public void sendGeneralTransactionAlarm(String title, AuthTransactionContextDTO authTransactionContextDTO, 
                                          String errorMessage, String additionalInfo) {
        try {
            String organizationNo = getOrganizationNo(authTransactionContextDTO);
            String cardId = getCardId(authTransactionContextDTO);
            String transAmount = getTransAmount(authTransactionContextDTO);
            String transCurrency = getTransCurrency(authTransactionContextDTO);

            String msg = String.format(
                "【%s】\n" +
                "机构号: %s\n" +
                "卡号: %s\n" +
                "交易ID: %s\n" +
                "交易金额: %s %s\n" +
                "错误信息: %s\n" +
                "附加信息: %s\n" +
                "处理时间: %s",
                title,
                organizationNo,
                cardId,
                authTransactionContextDTO.getTransactionId(),
                transAmount,
                transCurrency,
                errorMessage,
                additionalInfo != null ? additionalInfo : "无",
                java.time.LocalDateTime.now().toString()
            );

            larkAlarmUtil.sendTextAlarm(msg);
            log.info("发送通用授权交易异常告警成功, transactionId: {}, title: {}", 
                authTransactionContextDTO.getTransactionId(), title);

        } catch (Exception e) {
            log.error("发送通用授权交易异常告警失败, transactionId: {}, title: {}, error: {}",
                authTransactionContextDTO.getTransactionId(), title, e.getMessage(), e);
        }
    }

    /**
     * 获取机构号
     */
    private String getOrganizationNo(AuthTransactionContextDTO context) {
        return context.getOrganizationBasicInfo() != null ? 
            context.getOrganizationBasicInfo().getOrganizationNo() : "未知";
    }

    /**
     * 获取卡号
     */
    private String getCardId(AuthTransactionContextDTO context) {
        return context.getOrganizationCustomerCardInfo() != null ? 
            context.getOrganizationCustomerCardInfo().getCardId() : "未知";
    }

    /**
     * 获取交易金额
     */
    private String getTransAmount(AuthTransactionContextDTO context) {
        return context.getBaseAuthRequestVO() != null ? 
            context.getBaseAuthRequestVO().getTransAmount().toString() : "未知";
    }

    /**
     * 获取交易币种
     */
    private String getTransCurrency(AuthTransactionContextDTO context) {
        return context.getBaseAuthRequestVO() != null ? 
            context.getBaseAuthRequestVO().getTransCurrency() : "未知";
    }
}

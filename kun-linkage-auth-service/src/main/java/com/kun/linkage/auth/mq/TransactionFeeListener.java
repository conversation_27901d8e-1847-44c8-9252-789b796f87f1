package com.kun.linkage.auth.mq;

import com.alibaba.fastjson.JSON;
import com.kun.linkage.auth.dto.AuthTransactionContextDTO;
import com.kun.linkage.auth.service.AuthTransactionFeeService;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.MqConsumerGroupConstant;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.auth.utils.AuthLarkAlarmUtil;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.db.entity.OrganizationFeeDetail;
import com.kun.linkage.common.redis.utils.RedissonCacheUtil;
import com.kun.common.util.mq.RocketMqService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 授权交易手续费处理消息监听器
 * <p>
 * 负责处理授权交易成功后的手续费异步处理操作，主要功能：
 * 1. 接收交易成功完成消息
 * 2. 解析交易上下文
 * 3. 执行手续费计算、保存和扣收
 * 4. 记录处理结果
 * 5. 失败重试机制（最多重试6次，间隔分别为1秒、10秒、30秒、1分钟、5分钟、30分钟）
 *
 * @since 1.0.0
 */
@Slf4j
@Component
@RocketMQMessageListener(
    topic = MqTopicConstant.AUTH_TRANSACTION_FEE_TOPIC,
    consumerGroup = MqConsumerGroupConstant.KL_AUTH_TRANSACTION_FEE_GROUP,
    messageModel = MessageModel.CLUSTERING
)
public class TransactionFeeListener implements RocketMQListener<AuthTransactionContextDTO> {

    @Autowired
    private AuthTransactionFeeService authTransactionFeeService;

    @Autowired
    private RocketMqService rocketMqService;

    @Autowired
    private AuthLarkAlarmUtil authLarkAlarmUtil;

    @Autowired
    private RedissonCacheUtil redissonCacheUtil;

    // 重试次数和延迟时间配置
    private static final int[] RETRY_DELAYS = {
        MqTopicConstant.DELAY_LEVEL_1S,    // 1秒
        MqTopicConstant.DELAY_LEVEL_10S,   // 10秒
        MqTopicConstant.DELAY_LEVEL_30S,   // 30秒
        MqTopicConstant.DELAY_LEVEL_1M,    // 1分钟
        MqTopicConstant.DELAY_LEVEL_5M,    // 5分钟
        MqTopicConstant.DELAY_LEVEL_30M    // 30分钟
    };
    private static final int MAX_RETRY_TIMES = RETRY_DELAYS.length;

    // 告警去重相关常量
    private static final String ALARM_DEDUP_KEY_PREFIX = "transaction_fee_alarm_dedup:";
    private static final long ALARM_DEDUP_EXPIRE_HOURS = 24; // 告警去重键过期时间：24小时

    // 消息发送相关常量
    private static final int MESSAGE_TIMEOUT_SECONDS = 10; // 消息发送超时时间：10秒

    // 错误类型常量
    private static final String ERROR_TYPE_PROCESSING_FAILED = "处理失败";
    private static final String ERROR_TYPE_SYSTEM_EXCEPTION = "系统异常";

    // 日志消息模板
    private static final String LOG_TEMPLATE_RETRY = "{}，将在{}级延迟后进行第{}次重试, transactionId: {}";
    private static final String LOG_TEMPLATE_MAX_RETRIES = "{}，已达到最大重试次数{}, transactionId: {}";
    private static final String LOG_TEMPLATE_SUCCESS = "交易手续费处理成功, transactionId: {}, 费用明细ID: {}, 手续费金额: {}";
    private static final String LOG_TEMPLATE_NO_FEE = "交易无需收取手续费, transactionId: {}";

    @NewSpan
    @Override
    public void onMessage(AuthTransactionContextDTO authTransactionContextDTO) {
        try {
            log.info("收到交易手续费处理消息: {}", JSON.toJSONString(authTransactionContextDTO));

            // 获取当前重试次数
            int retryTimes = getRetryTimes(authTransactionContextDTO);

            // 执行交易手续费处理
            Result<OrganizationFeeDetail> result = authTransactionFeeService.processTransactionFee(authTransactionContextDTO);

            if (!result.isSuccess()) {
                log.error("交易手续费处理失败, transactionId: {}, error: {}, 当前重试次数: {}",
                    authTransactionContextDTO.getTransactionId(),
                    result.getMessage(),
                    retryTimes
                );

                // 处理重试或告警
                handleRetryOrAlarm(authTransactionContextDTO, retryTimes, result.getMessage(), ERROR_TYPE_PROCESSING_FAILED);
            } else {
                logSuccessfulProcessing(authTransactionContextDTO, result.getData());
            }
        } catch (BusinessException e) {
            log.error("处理交易手续费消息时发生业务异常: {}", e.getMessage(), e);
            handleRetry(authTransactionContextDTO, e);
        } catch (Exception e) {
            log.error("处理交易手续费消息时发生系统异常: {}", e.getMessage(), e);
            handleRetry(authTransactionContextDTO, e);
        }
    }

    /**
     * 获取当前重试次数
     */
    private int getRetryTimes(AuthTransactionContextDTO authTransactionContextDTO) {
        // 从上下文中获取重试次数，如果没有则默认为0
        return authTransactionContextDTO.getRetryTimes() != null ? 
            authTransactionContextDTO.getRetryTimes() : 0;
    }

    /**
     * 更新重试次数
     */
    private void updateRetryTimes(AuthTransactionContextDTO authTransactionContextDTO, int retryTimes) {
        authTransactionContextDTO.setRetryTimes(retryTimes);
    }

    /**
     * 处理重试逻辑
     */
    private void handleRetry(AuthTransactionContextDTO authTransactionContextDTO, Exception e) {
        int retryTimes = getRetryTimes(authTransactionContextDTO);
        handleRetryOrAlarm(authTransactionContextDTO, retryTimes, e.getMessage(), ERROR_TYPE_SYSTEM_EXCEPTION);
    }

    /**
     * 统一处理重试或告警逻辑
     * <p>
     * 根据当前重试次数决定是继续重试还是发送告警通知
     * </p>
     *
     * @param authTransactionContextDTO 交易上下文
     * @param retryTimes 当前重试次数
     * @param errorMessage 错误信息
     * @param errorType 错误类型
     */
    private void handleRetryOrAlarm(AuthTransactionContextDTO authTransactionContextDTO,
                                   int retryTimes, String errorMessage, String errorType) {
        String transactionId = authTransactionContextDTO.getTransactionId();

        if (retryTimes < MAX_RETRY_TIMES) {
            // 执行重试逻辑
            executeRetry(authTransactionContextDTO, retryTimes, errorMessage, errorType);
        } else {
            // 达到最大重试次数，发送告警
            logMaxRetriesReached(transactionId, errorMessage, errorType);
            sendAlarmWithDeduplication(authTransactionContextDTO, errorMessage, errorType);
        }
    }

    /**
     * 执行重试逻辑
     */
    private void executeRetry(AuthTransactionContextDTO authTransactionContextDTO,
                             int retryTimes, String errorMessage, String errorType) {
        String transactionId = authTransactionContextDTO.getTransactionId();

        // 更新重试次数
        updateRetryTimes(authTransactionContextDTO, retryTimes + 1);

        // 计算下次重试的延迟时间
        int delayLevel = RETRY_DELAYS[retryTimes];

        // 发送延迟消息
        rocketMqService.delayedSend(
            MqTopicConstant.AUTH_TRANSACTION_FEE_TOPIC,
            authTransactionContextDTO,
            MESSAGE_TIMEOUT_SECONDS,
            delayLevel
        );

        // 记录重试日志
        logRetryAttempt(transactionId, delayLevel, retryTimes + 1, errorMessage, errorType);
    }

    /**
     * 记录重试尝试日志
     */
    private void logRetryAttempt(String transactionId, int delayLevel, int nextRetryTimes,
                                String errorMessage, String errorType) {
        if (ERROR_TYPE_SYSTEM_EXCEPTION.equals(errorType)) {
            log.info(LOG_TEMPLATE_RETRY + ", error: {}",
                "发生异常", delayLevel, nextRetryTimes, transactionId, errorMessage);
        } else {
            log.info(LOG_TEMPLATE_RETRY,
                "交易手续费处理失败", delayLevel, nextRetryTimes, transactionId);
        }
    }

    /**
     * 记录达到最大重试次数的日志
     */
    private void logMaxRetriesReached(String transactionId, String errorMessage, String errorType) {
        if (ERROR_TYPE_SYSTEM_EXCEPTION.equals(errorType)) {
            log.error(LOG_TEMPLATE_MAX_RETRIES + ", error: {}",
                "发生异常", MAX_RETRY_TIMES, transactionId, errorMessage);
        } else {
            log.error(LOG_TEMPLATE_MAX_RETRIES,
                "交易手续费处理失败", MAX_RETRY_TIMES, transactionId);
        }
    }

    /**
     * 记录成功处理日志
     */
    private void logSuccessfulProcessing(AuthTransactionContextDTO authTransactionContextDTO,
                                       OrganizationFeeDetail feeDetail) {
        String transactionId = authTransactionContextDTO.getTransactionId();
        if (feeDetail != null) {
            log.info(LOG_TEMPLATE_SUCCESS, transactionId, feeDetail.getId(), feeDetail.getFeeAmount());
        } else {
            log.info(LOG_TEMPLATE_NO_FEE, transactionId);
        }
    }

    /**
     * 发送告警通知（带去重机制）
     * <p>
     * 使用 Redis 实现告警去重，确保对于同一个交易ID在达到最大重试次数后只发送一次告警通知
     * </p>
     *
     * @param authTransactionContextDTO 交易上下文
     * @param errorMessage 错误信息
     * @param errorType 错误类型
     */
    private void sendAlarmWithDeduplication(AuthTransactionContextDTO authTransactionContextDTO,
                                          String errorMessage, String errorType) {
        if (authTransactionContextDTO == null) {
            log.error("交易上下文为空，无法发送告警");
            return;
        }

        String transactionId = authTransactionContextDTO.getTransactionId();
        if (transactionId == null || transactionId.trim().isEmpty()) {
            log.error("交易ID为空，无法发送告警");
            return;
        }

        try {
            String alarmKey = buildAlarmDedupKey(transactionId);

            // 使用原子操作检查并设置去重键
            if (trySetAlarmDedupKey(alarmKey)) {
                // 首次告警，发送通知
                sendAlarmNotification(authTransactionContextDTO, errorMessage, errorType, transactionId);
            } else {
                // 重复告警，跳过发送
                log.info("交易手续费处理告警已发送过，跳过重复发送, transactionId: {}, errorType: {}",
                    transactionId, errorType);
            }
        } catch (Exception e) {
            log.error("处理告警去重逻辑时发生异常, transactionId: {}, error: {}",
                transactionId, e.getMessage(), e);

            // 降级处理：直接发送告警
            fallbackSendAlarm(authTransactionContextDTO, errorMessage, errorType, transactionId);
        }
    }

    /**
     * 尝试设置告警去重键
     *
     * @param alarmKey 告警去重键
     * @return true表示首次设置成功，false表示键已存在
     */
    private boolean trySetAlarmDedupKey(String alarmKey) {
        try {
            // 检查键是否存在
            boolean keyExists = redissonCacheUtil.exists(alarmKey);
            if (!keyExists) {
                // 键不存在，设置键并返回true
                redissonCacheUtil.set(alarmKey, System.currentTimeMillis(),
                    ALARM_DEDUP_EXPIRE_HOURS, TimeUnit.HOURS);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.warn("Redis操作异常，降级处理: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 发送告警通知
     */
    private void sendAlarmNotification(AuthTransactionContextDTO authTransactionContextDTO,
                                     String errorMessage, String errorType, String transactionId) {
        try {
            authLarkAlarmUtil.sendTransactionFeeAlarm(authTransactionContextDTO,
                errorMessage, errorType, MAX_RETRY_TIMES);
            log.info("已发送交易手续费处理告警通知, transactionId: {}, errorType: {}",
                transactionId, errorType);
        } catch (Exception e) {
            log.error("发送告警通知失败, transactionId: {}, errorType: {}, error: {}",
                transactionId, errorType, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 降级发送告警（当去重逻辑异常时）
     */
    private void fallbackSendAlarm(AuthTransactionContextDTO authTransactionContextDTO,
                                  String errorMessage, String errorType, String transactionId) {
        try {
            authLarkAlarmUtil.sendTransactionFeeAlarm(authTransactionContextDTO,
                errorMessage, errorType, MAX_RETRY_TIMES);
            log.warn("去重逻辑异常，已降级发送告警通知, transactionId: {}", transactionId);
        } catch (Exception alarmException) {
            log.error("降级发送告警通知也失败, transactionId: {}, error: {}",
                transactionId, alarmException.getMessage(), alarmException);
        }
    }

    /**
     * 构建告警去重键
     * <p>
     * 格式：transaction_fee_alarm_dedup:transactionId
     * 使用StringBuilder提高字符串拼接性能
     * </p>
     *
     * @param transactionId 交易ID
     * @return 告警去重键
     */
    private String buildAlarmDedupKey(String transactionId) {
        if (transactionId == null || transactionId.trim().isEmpty()) {
            throw new IllegalArgumentException("交易ID不能为空");
        }
        return ALARM_DEDUP_KEY_PREFIX +transactionId;
    }

}

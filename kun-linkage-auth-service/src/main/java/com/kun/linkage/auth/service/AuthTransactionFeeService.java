package com.kun.linkage.auth.service;

import com.kun.linkage.auth.dto.AuthTransactionContextDTO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.db.entity.OrganizationFeeDetail;
import com.kun.linkage.customer.facade.enums.OrganizationFeeTypeEnum;

import java.math.BigDecimal;

/**
 * 授权交易手续费服务接口
 * <p>
 * 负责授权交易过程中的手续费计算、扣收和冲回等操作
 * </p>
 *
 * @since 2025-07-18
 */
public interface AuthTransactionFeeService {

    /**
     * 计算授权交易手续费
     * <p>
     * 根据机构费率配置和交易信息计算手续费金额
     * </p>
     *
     * @param authTransactionContextDTO 授权交易上下文
     * @return 计算结果，包含手续费金额和相关信息
     */
    Result<BigDecimal> calculateTransactionFee(AuthTransactionContextDTO authTransactionContextDTO, OrganizationFeeTypeEnum feeType);

    /**
     * 计算并保存授权交易手续费明细
     * <p>
     * 计算手续费并保存到费用明细表，支持KUN和PayX双币种扣收
     * </p>
     *
     * @param authTransactionContextDTO 授权交易上下文
     * @return 保存的费用明细记录
     */
    Result<OrganizationFeeDetail> calculateAndSaveTransactionFeeDetail(AuthTransactionContextDTO authTransactionContextDTO, OrganizationFeeTypeEnum feeTyp,String remark);

    /**
     * 处理授权交易手续费完整流程
     * <p>
     * 包含计算、保存、扣收的完整流程，支持事务回滚
     * </p>
     *
     * @param authTransactionContextDTO 授权交易上下文
     * @return 处理结果
     */
    Result<OrganizationFeeDetail> processTransactionFee(AuthTransactionContextDTO authTransactionContextDTO);

    /**
     * 获取机构的交易手续费配置
     * <p>
     * 根据机构号和卡产品获取有效的费率配置
     * </p>
     *
     * @param organizationNo  机构号
     * @param cardProductCode 卡产品代码
     * @return 费率配置信息
     */
    Result<com.kun.linkage.common.db.entity.OrganizationFeeConfig> getTransactionFeeConfig(String organizationNo, String cardProductCode);
}

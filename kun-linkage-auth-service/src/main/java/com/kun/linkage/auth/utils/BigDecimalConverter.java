package com.kun.linkage.auth.utils;

import java.math.BigDecimal;

/**
 * title: <br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * @date 2025/6/11 11:05
 */
public class BigDecimalConverter {

    /** 
     * @description: 数字转换工具类  69985022 =》9.985022
     * @param: input 
     * @return: java.math.BigDecimal 
     *
     * <AUTHOR>
     * @since 2025/6/11 11:07
     */
    public static BigDecimal convert(String input) {
        if (input == null || input.length() < 2) {
            throw new IllegalArgumentException("输入格式不合法，至少需要两位数字");
        }

        try {
            int decimalPlaces = Integer.parseInt(input.substring(0, 1));
            String numberStr = input.substring(1);
            BigDecimal number = new BigDecimal(numberStr);
            BigDecimal divisor = BigDecimal.TEN.pow(decimalPlaces);
            return number.divide(divisor);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("输入字符串包含非法数字", e);
        }
    }

    public static void main(String[] args) {
        String input = "69985022";
        BigDecimal result = convert(input);
        System.out.println(result);  // 输出：9.985022
    }
}

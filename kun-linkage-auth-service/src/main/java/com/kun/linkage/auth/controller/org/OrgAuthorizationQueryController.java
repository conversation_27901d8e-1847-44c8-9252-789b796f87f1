package com.kun.linkage.auth.controller.org;

import com.kun.linkage.auth.facade.constant.AuthApplicationRequestParamNameConstant;
import com.kun.linkage.auth.facade.constant.AuthExportConstant;
import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationExportRequestVO;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationInquiryPageVO;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationInquiryReuqestVO;
import com.kun.linkage.auth.service.AuthFlowService;
import com.kun.linkage.auth.service.AuthorizationExportService;
import com.kun.linkage.auth.service.ExportFileRecordService;
import com.kun.linkage.auth.utils.I18nMessageService;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.KLExportFileRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

@Tag(name = "OrgAuthorizationQueryController", description = "授权记录查询")
@RestController
@RequestMapping("/org/authorization")
public class OrgAuthorizationQueryController extends BaseVccBossController {

    @Resource
    private I18nMessageService i18nMessageService;

    @Resource
    private AuthFlowService authFlowService;

    @Resource
    private AuthorizationExportService authorizationExportService;

    @Resource
    private ExportFileRecordService exportFileRecordService;

    /**
     * 分页查询授权记录
     *
     * @param requestVO
     * @return
     */
    @Operation(description = "分页查询授权记录", summary = "分页查询授权记录")
    @PostMapping("/pageList")
    public Result<PageResult<AuthorizationInquiryPageVO>> pageList(@RequestBody AuthorizationInquiryReuqestVO requestVO) {
        if (StringUtils.isBlank(requestVO.getAuthorizationDateFrom())) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                            i18nMessageService.getMessage(AuthApplicationRequestParamNameConstant.AUTHORIZATION_DATE_FROM)));
        }
        if (StringUtils.isBlank(requestVO.getAuthorizationDateUntil())) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                            i18nMessageService.getMessage(AuthApplicationRequestParamNameConstant.AUTHORIZATION_DATE_TO)));
        }
        PageResult<AuthorizationInquiryPageVO> pageList = authFlowService.pageList(requestVO);
        return Result.success(pageList);
    }

    /**
     * 异步导出授权记录
     *
     * @param requestVO
     * @return
     */
    @Operation(description = "异步导出授权记录", summary = "异步导出授权记录")
    @PostMapping("/asyncExport")
    public Result<String> asyncExport(@RequestBody AuthorizationExportRequestVO requestVO) {
        // 验证必填参数
        if (StringUtils.isBlank(requestVO.getAuthorizationDateFrom())) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                            i18nMessageService.getMessage(AuthApplicationRequestParamNameConstant.AUTHORIZATION_DATE_FROM)));
        }
        if (StringUtils.isBlank(requestVO.getAuthorizationDateUntil())) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                            i18nMessageService.getMessage(AuthApplicationRequestParamNameConstant.AUTHORIZATION_DATE_TO)));
        }

        // 验证日期范围不超过31天
        try {
            LocalDate fromDate = LocalDate.parse(requestVO.getAuthorizationDateFrom(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDate toDate = LocalDate.parse(requestVO.getAuthorizationDateUntil(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            long daysBetween = ChronoUnit.DAYS.between(fromDate, toDate);

            if (daysBetween > AuthExportConstant.MAX_QUERY_DAYS) {
                return Result.fail(KunLinkageAuthResponseCodeConstant.DATE_RANGE_EXCEEDED.getCode(),
                        i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.DATE_RANGE_EXCEEDED.getCode()));
            }
        } catch (Exception e) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                    "Invalid date format, please use yyyy-MM-dd");
        }

        // 生成文件名
        String fileName = authorizationExportService.generateFileName(requestVO.getMerchantNo());

        // 创建文件记录
        KLExportFileRecord fileRecord = exportFileRecordService.createFileRecord(
                requestVO.getMerchantNo(),
                fileName,
                AuthExportConstant.FileType.AUTHORIZATION_EXPORT);

        // 启动异步导出任务
        authorizationExportService.asyncExportData(fileRecord, requestVO);

        return Result.success(fileRecord.getFileRecordId());
    }
}

package com.kun.linkage.auth.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;

/**
 * StopWatch工具类
 * 
 * 提供增强的时间统计功能，支持更精确的计时和统计
 */
@Slf4j
public class StopWatchUtil {

    /**
     * 创建并启动StopWatch
     * 
     * @param taskName 任务名称
     * @return StopWatch实例
     */
    public static StopWatch createAndStart(String taskName) {
        StopWatch stopWatch = new StopWatch(taskName);
        stopWatch.start("Task");
        return stopWatch;
    }

    /**
     * 创建并启动StopWatch（带URL标识）
     * 
     * @param url 接口URL
     * @return StopWatch实例
     */
    public static StopWatch createAndStartForApi(String url) {
        String taskName = "ExternalApi-" + url;
        return createAndStart(taskName);
    }

    /**
     * 停止StopWatch并获取总耗时（毫秒）
     * 
     * @param stopWatch StopWatch实例
     * @return 总耗时（毫秒）
     */
    public static long stopAndGetTotalTime(StopWatch stopWatch) {
        if (stopWatch == null) {
            return 0;
        }
        
        if (stopWatch.isRunning()) {
            stopWatch.stop();
        }
        
        return stopWatch.getTotalTimeMillis();
    }

    /**
     * 停止StopWatch并记录日志
     * 
     * @param stopWatch StopWatch实例
     * @param url 接口URL
     * @param success 是否成功
     */
    public static void stopAndLog(StopWatch stopWatch, String url, boolean success) {
        if (stopWatch == null) {
            return;
        }
        
        long totalTime = stopAndGetTotalTime(stopWatch);
        String status = success ? "成功" : "失败";
        
        log.info("接口调用完成: {}, 状态: {}, 耗时: {}ms", url, status, totalTime);
        
        // 记录详细的StopWatch信息
        if (log.isDebugEnabled()) {
            log.debug("StopWatch详细信息: {}", stopWatch.prettyPrint());
        }
    }

    /**
     * 检查是否为慢查询
     * 
     * @param stopWatch StopWatch实例
     * @param threshold 慢查询阈值（毫秒）
     * @return 是否为慢查询
     */
    public static boolean isSlowQuery(StopWatch stopWatch, long threshold) {
        if (stopWatch == null) {
            return false;
        }
        
        long totalTime = stopWatch.getTotalTimeMillis();
        return totalTime > threshold;
    }
} 
package com.kun.linkage.auth.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.kun.common.util.mq.RocketMqService;
import com.kun.linkage.auth.dto.AuthTransactionContextDTO;
import com.kun.linkage.auth.service.AuthTransactionFeeService;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.base.enums.DigitalCurrencyEnum;
import com.kun.linkage.common.base.enums.FiatCurrencyEnum;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.common.base.enums.YesFlagEnum;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.db.entity.*;
import com.kun.linkage.common.db.mapper.CurrencyInfoMapper;
import com.kun.linkage.common.db.mapper.OrganizationFeeConfigMapper;
import com.kun.linkage.common.db.mapper.OrganizationFeeDetailMapper;
import com.kun.linkage.common.db.mapper.OrganizationFeeTemplateDetailMapper;
import com.kun.linkage.common.external.facade.api.kcard.KCardKunAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.KCardPayXAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXRemarkEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunSideTypeEnum;
import com.kun.linkage.common.external.facade.api.kcard.req.KunAskPriceReq;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXAskPriceReq;
import com.kun.linkage.common.external.facade.api.kcard.res.KunAskPriceRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXAskPriceRsp;
import com.kun.linkage.customer.facade.constants.CustomerTipConstant;
import com.kun.linkage.customer.facade.enums.DeductProcessorEnum;
import com.kun.linkage.customer.facade.enums.OrganizationFeeBillingDimensionEnum;
import com.kun.linkage.customer.facade.enums.OrganizationFeeTypeEnum;
import com.kun.linkage.customer.facade.vo.mq.OrganizationFeeDeductionEventVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;

/**
 * 授权交易手续费服务实现类
 *
 * @since 2025-07-18
 */
@Service
@Slf4j
public class AuthTransactionFeeServiceImpl implements AuthTransactionFeeService {
    @Resource
    private OrganizationFeeConfigMapper organizationFeeConfigMapper;
    @Resource
    private OrganizationFeeTemplateDetailMapper organizationFeeTemplateDetailMapper;
    @Resource
    private OrganizationFeeDetailMapper organizationFeeDetailMapper;
    @Resource
    private KCardKunAccountFacade kCardKunAccountFacade;
    @Resource
    private KCardPayXAccountFacade kCardPayXAccountFacade;
    @Resource
    private CurrencyInfoMapper currencyInfoMapper;
    @Resource
    private RocketMqService rocketMqService;

    /**
     * 处理授权交易手续费
     * <p>
     * 在记账前处理手续费计算和扣收
     * </p>
     *
     * @param authTransactionContextDTO 交易上下文
     * @return 处理结果，成功或不需要收取手续费时返回null，扣收失败时返回错误响应
     */
    public Result<OrganizationFeeDetail> processTransactionFee(AuthTransactionContextDTO authTransactionContextDTO) {
        try {
            OrganizationBasicInfo organizationBasicInfo = authTransactionContextDTO.getOrganizationBasicInfo();
            // 检查是否需要收取手续费
            if (authTransactionContextDTO.getTransactionTypeEnum().getDirection() == null) {
                log.info("交易不需要收取手续费，交易ID: {}", authTransactionContextDTO.getTransactionId());
                return null;
            }

            // thirdPartyAuthorizationFlag==1&&poolCurrencyCode是数币的时候需要扣除承兑费
            if (organizationBasicInfo.getThirdPartyAuthorizationFlag() == 1
                    && DigitalCurrencyEnum.contains(organizationBasicInfo.getPoolCurrencyCode())) {
                calculateAndSaveTransactionFeeDetail(authTransactionContextDTO, OrganizationFeeTypeEnum.ACCEPTANCE_FEE, KunAndPayXRemarkEnum.ACCEPTANCE_FEE.getRemark());
            }
            // 计算并保存手续费明细
            Result<OrganizationFeeDetail> feeDetailResult = calculateAndSaveTransactionFeeDetail(authTransactionContextDTO, OrganizationFeeTypeEnum.TRANSACTION_FEE, KunAndPayXRemarkEnum.TRANSACTION_FEE.getRemark());

            if (!feeDetailResult.isSuccess()) {
                log.error("计算并保存手续费明细失败，交易ID: {}, 错误信息: {}",
                        authTransactionContextDTO.getTransactionId(), feeDetailResult.getMessage());
                return null;
            }

            OrganizationFeeDetail feeDetail = feeDetailResult.getData();
            if (feeDetail == null) {
                log.info("无需收取手续费，交易ID: {}", authTransactionContextDTO.getTransactionId());
                return null;
            }
            log.info("授权交易手续费处理成功，交易ID: {}, 手续费金额: {}, 币种: {}",
                    authTransactionContextDTO.getTransactionId(),
                    feeDetail.getFeeAmount(),
                    feeDetail.getTransactionCurrencyCode());
            return Result.success(feeDetail);
        } catch (Exception e) {
            log.error("处理授权交易手续费异常，交易ID: {}", authTransactionContextDTO.getTransactionId(), e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        }
    }

    @Override
    public Result<BigDecimal> calculateTransactionFee(AuthTransactionContextDTO authTransactionContextDTO, OrganizationFeeTypeEnum feeType) {
        try {
            log.info("开始计算授权交易手续费，交易ID: {}", authTransactionContextDTO.getTransactionId());

            // 获取费率配置
            Result<OrganizationFeeConfig> configResult = getTransactionFeeConfig(
                    authTransactionContextDTO.getOrganizationBasicInfo().getOrganizationNo(),
                    authTransactionContextDTO.getOrganizationCustomerCardInfo().getCardProductCode()
            );

            if (!configResult.isSuccess() || configResult.getData() == null) {
                log.warn("未找到机构费率配置，手续费按0处理，机构号: {}, 卡产品: {}",
                        authTransactionContextDTO.getOrganizationBasicInfo().getOrganizationNo(),
                        authTransactionContextDTO.getOrganizationCustomerCardInfo().getCardProductCode());
                return Result.success(BigDecimal.ZERO);
            }

            OrganizationFeeConfig feeConfig = configResult.getData();

            // 获取费率模板明细
            OrganizationFeeTemplateDetail templateDetail = organizationFeeTemplateDetailMapper.selectOne(
                    new LambdaQueryWrapper<OrganizationFeeTemplateDetail>()
                            .eq(OrganizationFeeTemplateDetail::getTemplateNo, feeConfig.getTemplateNo())
                            .eq(OrganizationFeeTemplateDetail::getFeeType, feeType.getValue())
                            .eq(OrganizationFeeTemplateDetail::getCurrencyCode, authTransactionContextDTO.getAuthFlow().getCardholderBillingCurrency())
                            .in(OrganizationFeeTemplateDetail::getBillingDimension,
                                    Arrays.asList(OrganizationFeeBillingDimensionEnum.SINGLE_AMOUNT.getValue(),
                                            OrganizationFeeBillingDimensionEnum.TIERED_SINGLE_AMOUNT.getValue()))
                            .lt(OrganizationFeeTemplateDetail::getMinAmount, authTransactionContextDTO.getAuthFlow().getCardholderMarkupBillingAmount())
                            .ge(OrganizationFeeTemplateDetail::getMaxAmount, authTransactionContextDTO.getAuthFlow().getCardholderMarkupBillingAmount())
            );

            if (templateDetail == null) {
                log.warn("未找到匹配的费率模板明细，手续费按0处理，模板号: {}, 交易金额: {}, 币种: {}",
                        feeConfig.getTemplateNo(),
                        authTransactionContextDTO.getAuthFlow().getCardholderMarkupBillingAmount(),
                        authTransactionContextDTO.getAuthFlow().getCardholderBillingCurrency());
                return Result.success(BigDecimal.ZERO);
            }

            authTransactionContextDTO.setOrganizationFeeTemplateDetail(templateDetail);
            // 计算手续费
            BigDecimal feeAmount = calculateFeeAmount(templateDetail, authTransactionContextDTO.getAuthFlow().getCardholderMarkupBillingAmount());

            log.info("授权交易手续费计算完成，交易ID: {}, 手续费金额: {}, 币种: {}",
                    authTransactionContextDTO.getTransactionId(), feeAmount, authTransactionContextDTO.getAuthFlow().getCardholderBillingCurrency());
            return Result.success(feeAmount);
        } catch (Exception e) {
            log.error("计算授权交易手续费异常，交易ID: {}", authTransactionContextDTO.getTransactionId(), e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        }
    }

    @Override
    public Result<OrganizationFeeDetail> calculateAndSaveTransactionFeeDetail(AuthTransactionContextDTO authTransactionContextDTO
            , OrganizationFeeTypeEnum feeType, String remark) {
        try {
            log.info("开始计算并保存授权交易手续费明细，交易ID: {}", authTransactionContextDTO.getTransactionId());

            // 计算手续费
            Result<BigDecimal> feeResult = calculateTransactionFee(authTransactionContextDTO, feeType);
            if (!feeResult.isSuccess()) {
                return Result.fail(feeResult.getMessage());
            }

            BigDecimal feeAmount = feeResult.getData();
            if (feeAmount.compareTo(BigDecimal.ZERO) == 0) {
                log.info("手续费金额为0，不保存费用明细，交易ID: {}", authTransactionContextDTO.getTransactionId());
                return Result.success(null);
            }

            // 构建费用明细记录
            OrganizationFeeDetail feeDetail = buildFeeDetail(authTransactionContextDTO, feeAmount, remark);

            // 保存费用明细
            organizationFeeDetailMapper.insert(feeDetail);

            log.info("授权交易手续费明细保存成功，交易ID: {}, 明细ID: {}, 手续费金额: {}",
                    authTransactionContextDTO.getTransactionId(), feeDetail.getId(), feeAmount);

            this.sendOrganizationFeeDeductionToMq(feeDetail, authTransactionContextDTO.getOrganizationBasicInfo().getMpcToken(),
                    authTransactionContextDTO.getOrganizationBasicInfo().getMpcGroupCode());
            log.info("费率类型:{},收取方式为实时收取,发送收取消息到mq,记录id:{}", feeType.getValue(), feeDetail.getId());

            return Result.success(feeDetail);
        } catch (Exception e) {
            log.error("计算并保存授权交易手续费明细异常，交易ID: {}", authTransactionContextDTO.getTransactionId(), e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        }
    }

    @Override
    public Result<OrganizationFeeConfig> getTransactionFeeConfig(String organizationNo, String cardProductCode) {
        try {
            LocalDateTime now = LocalDateTime.now();
            OrganizationFeeConfig feeConfig = organizationFeeConfigMapper.selectOne(
                    new LambdaQueryWrapper<OrganizationFeeConfig>()
                            .eq(OrganizationFeeConfig::getOrganizationNo, organizationNo)
                            .eq(OrganizationFeeConfig::getCardProductCode, cardProductCode)
                            .eq(OrganizationFeeConfig::getStatus, ValidStatusEnum.VALID.getValue())
                            .le(OrganizationFeeConfig::getEffectiveStartTime, now)
                            .ge(OrganizationFeeConfig::getEffectiveEndTime, now)
                            .orderByDesc(OrganizationFeeConfig::getCreateTime)
                            .last("LIMIT 1")
            );

            return Result.success(feeConfig);
        } catch (Exception e) {
            log.error("获取机构费率配置异常，机构号: {}, 卡产品: {}", organizationNo, cardProductCode, e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        }
    }

    /**
     * 计算手续费金额
     */
    private BigDecimal calculateFeeAmount(OrganizationFeeTemplateDetail templateDetail, BigDecimal billingAmount) {
        // 比例费用计算
        BigDecimal proportionRate = templateDetail.getProportionRate() != null ? templateDetail.getProportionRate() : BigDecimal.ZERO;
        BigDecimal proportionFeeAmount = billingAmount.multiply(proportionRate).setScale(6, RoundingMode.HALF_UP);

        // 应用比例费用的最小值和最大值限制
        if (templateDetail.getProportionMinAmount() != null && proportionFeeAmount.compareTo(templateDetail.getProportionMinAmount()) < 0) {
            proportionFeeAmount = templateDetail.getProportionMinAmount();
        }
        if (templateDetail.getProportionMaxAmount() != null && proportionFeeAmount.compareTo(templateDetail.getProportionMaxAmount()) > 0) {
            proportionFeeAmount = templateDetail.getProportionMaxAmount();
        }

        // 固定费用
        BigDecimal fixedAmount = templateDetail.getFixedAmount() != null ? templateDetail.getFixedAmount() : BigDecimal.ZERO;

        // 总手续费 = 比例费用 + 固定费用
        return proportionFeeAmount.add(fixedAmount);
    }

    /**
     * 构建费用明细记录
     */
    private OrganizationFeeDetail buildFeeDetail(AuthTransactionContextDTO authTransactionContextDTO, BigDecimal feeAmount, String remark) {
        LocalDateTime now = LocalDateTime.now().withNano(0);
        OrganizationFeeDetail feeDetail = new OrganizationFeeDetail();
        OrganizationFeeTemplateDetail organizationFeeTemplateDetail = authTransactionContextDTO.getOrganizationFeeTemplateDetail();
        // 基本信息
        feeDetail.setOrganizationNo(authTransactionContextDTO.getOrganizationBasicInfo().getOrganizationNo());
        feeDetail.setCardProductCode(authTransactionContextDTO.getOrganizationCustomerCardInfo().getCardProductCode());
        feeDetail.setRelatedTransactionId(authTransactionContextDTO.getTransactionId());
        feeDetail.setCalculateDatetime(now);
        Date transDoneTime = authTransactionContextDTO.getAuthFlow().getTransDoneTime();
        feeDetail.setTransactionDatetime(transDoneTime == null ? null : DateUtil.toLocalDateTime(transDoneTime));
        feeDetail.setFeeType(organizationFeeTemplateDetail.getFeeType());
        feeDetail.setFeeCollectionMethod(organizationFeeTemplateDetail.getCollectionMethod());

        // 交易信息
        feeDetail.setTransactionAmount(authTransactionContextDTO.getAuthFlow().getCardholderMarkupBillingAmount());
        feeDetail.setTransactionCurrencyCode(authTransactionContextDTO.getAuthFlow().getCardholderBillingCurrency());
        feeDetail.setTransactionCurrencyPrecision(authTransactionContextDTO.getAuthFlow().getCardholderCurrencyExponent());

        // 手续费金额（与交易币种相同）
        feeDetail.setFeeAmount(feeAmount);

        BigDecimal proportionRate = organizationFeeTemplateDetail.getProportionRate() != null ? organizationFeeTemplateDetail.getProportionRate() : BigDecimal.ZERO;
        BigDecimal fixedAmount = organizationFeeTemplateDetail.getFixedAmount() != null ? organizationFeeTemplateDetail.getFixedAmount() : BigDecimal.ZERO;
        feeDetail.setSnapshotBillingDimension(organizationFeeTemplateDetail.getBillingDimension());
        feeDetail.setSnapshotMinAmount(organizationFeeTemplateDetail.getMinAmount());
        feeDetail.setSnapshotMaxAmount(organizationFeeTemplateDetail.getMaxAmount());
        feeDetail.setSnapshotProportionRate(proportionRate);
        feeDetail.setSnapshotProportionMinAmount(organizationFeeTemplateDetail.getProportionMinAmount());
        feeDetail.setSnapshotProportionMaxAmount(organizationFeeTemplateDetail.getProportionMaxAmount());
        feeDetail.setSnapshotFixedAmount(fixedAmount);
        // 设置KUN和PayX扣收金额
        BigDecimal fxRate = BigDecimal.ONE;
        if (DigitalCurrencyEnum.contains(authTransactionContextDTO.getOrganizationBasicInfo().getPoolCurrencyCode())) {
            // feeAmount需要和资金池币种比较，不一样需要根据kun汇率换算
            feeDetail.setDeductProcessor(DeductProcessorEnum.KUN.getValue());
            if (!authTransactionContextDTO.getAuthFlow().getCardholderBillingCurrency()
                    .equals(authTransactionContextDTO.getOrganizationBasicInfo().getPoolCurrencyCode())) {
                fxRate = processingDigitalCurrencyExchangeRate(authTransactionContextDTO, feeAmount);
            }
            feeDetail.setDeductCurrencyPrecision(6);
            feeDetail.setDeductFeeAmount(feeAmount.multiply(fxRate).setScale(6, RoundingMode.UP));
        } else {
            // feeAmount需要和资金池币种比较，不一样需要根据payx汇率换算
            feeDetail.setDeductProcessor(DeductProcessorEnum.PAYX.getValue());
            if (!authTransactionContextDTO.getAuthFlow().getCardholderBillingCurrency()
                    .equals(authTransactionContextDTO.getOrganizationBasicInfo().getPoolCurrencyCode())) {
                fxRate = processingFiatCurrencyExchangeRate(authTransactionContextDTO);
            }

            // 扣款币种精度查询数据库获取
            QueryWrapper<CurrencyInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CurrencyInfo::getCcyCode, authTransactionContextDTO.getOrganizationBasicInfo().getPoolCurrencyCode())
                    .eq(CurrencyInfo::getStatus, YesFlagEnum.YES.getNumValue());
            CurrencyInfo currencyInfo = currencyInfoMapper.selectOne(queryWrapper);

            int precision = currencyInfo != null ? currencyInfo.getExponent() : 2;
            feeDetail.setDeductCurrencyPrecision(precision);

            feeDetail.setDeductFeeAmount(feeAmount.multiply(fxRate).setScale(precision, RoundingMode.UP));
        }
        feeDetail.setFxRate(fxRate);
        feeDetail.setDeductCurrencyCode(authTransactionContextDTO.getOrganizationBasicInfo().getPoolCurrencyCode());
        feeDetail.setDeductRequestNo(String.valueOf(IdWorker.getId()));
        feeDetail.setCallCount(1);
        // 备注信息
        feeDetail.setRemark(remark);

        // 初始状态为未收
        feeDetail.setFeeCollectionStatus(YesFlagEnum.NO.getNumValue());

        // 时间戳
        feeDetail.setCreateTime(now);
        feeDetail.setLastModifyTime(now);
        return feeDetail;
    }

    /**
     * 组装kun询价请求参数
     *
     * @param feeAmount
     * @param authTransactionContextDTO
     * @return
     */
    private KunAskPriceReq assKunAskPriceReq(BigDecimal feeAmount, AuthTransactionContextDTO authTransactionContextDTO) {
        OrganizationBasicInfo organizationBasicInfo = authTransactionContextDTO.getOrganizationBasicInfo();
        KunAskPriceReq kunAskPriceReq = new KunAskPriceReq();
        kunAskPriceReq.setToken(organizationBasicInfo.getMpcToken());
        kunAskPriceReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        kunAskPriceReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        kunAskPriceReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        kunAskPriceReq.setPayAmount(feeAmount);
        kunAskPriceReq.setSideType(KunSideTypeEnum.BUY.getType());
        // kun接口币对必须数币在前法币在后
        kunAskPriceReq.setSymbol(organizationBasicInfo.getPoolCurrencyCode() + "_" + authTransactionContextDTO.getAuthFlow().getCardholderBillingCurrency());
        return kunAskPriceReq;
    }

    /**
     * 进行法币转数币汇率换算
     *
     * @param authTransactionContextDTO
     */
    private BigDecimal processingDigitalCurrencyExchangeRate(AuthTransactionContextDTO authTransactionContextDTO, BigDecimal feeAmount) {
        // 美金兑USDC需要查最新汇率,兑USDT不需要
        // 港币都需要查询最新汇率
        BigDecimal fxRate = BigDecimal.ONE;
        OrganizationBasicInfo organizationBasicInfo = authTransactionContextDTO.getOrganizationBasicInfo();
        String billingCurrency = authTransactionContextDTO.getAuthFlow().getCardholderBillingCurrency();
        if (!(StringUtils.equals(billingCurrency, FiatCurrencyEnum.USD.getCurrencyCode())
                && StringUtils.equals(organizationBasicInfo.getPoolCurrencyCode(), DigitalCurrencyEnum.USDT.getValue()))) {
            KunAskPriceReq kunAskPriceReq = this.assKunAskPriceReq(feeAmount, authTransactionContextDTO);
            log.info("调用KUN汇率查询接口开始,请求参数:{}", JSON.toJSONString(kunAskPriceReq));
            Result<KunAskPriceRsp> kunAskPriceRspResult = kCardKunAccountFacade.kunExchangeRate(kunAskPriceReq);
            log.info("调用KUN汇率查询接口结束,响应参数:{}", JSON.toJSONString(kunAskPriceRspResult));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            if (kunAskPriceRspResult != null && StringUtils.equals(kunAskPriceRspResult.getCode(), String.valueOf(HttpStatus.SC_OK))
                    && kunAskPriceRspResult.getData() != null && kunAskPriceRspResult.getData().getPrice() != null) {
                // 此处应使用kun接口返回汇率的导数
                fxRate = kunAskPriceRspResult.getData().getPrice();
            } else {
                log.error("调用KUN汇率查询接口失败,响应信息:{}", kunAskPriceRspResult);
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }
        log.info("法币转数币汇率换算完成,原币种:{},目标币种:{},汇率:{}",
                billingCurrency, organizationBasicInfo.getPoolCurrencyCode(), fxRate);
        return fxRate;
    }

    /**
     * 进行法币转法币汇率换算
     *
     * @param authTransactionContextDTO
     */
    private BigDecimal processingFiatCurrencyExchangeRate(AuthTransactionContextDTO authTransactionContextDTO) {
        // feeAmount和资金池币种不一致才需要换汇
        BigDecimal fxRate = BigDecimal.ONE;
        OrganizationBasicInfo organizationBasicInfo = authTransactionContextDTO.getOrganizationBasicInfo();
        String billingCurrency = authTransactionContextDTO.getAuthFlow().getCardholderBillingCurrency();
        if (!StringUtils.equals(billingCurrency, organizationBasicInfo.getPoolCurrencyCode())) {
            PayXAskPriceReq payXAskPriceReq = this.assPayXAskPriceReq(billingCurrency, organizationBasicInfo);
            log.info("调用PayX汇率查询接口开始,请求参数:{}", JSON.toJSONString(payXAskPriceReq));
            Result<PayXAskPriceRsp> payXAskPriceRspResult = kCardPayXAccountFacade.payXExchangeRate(payXAskPriceReq);
            log.info("调用PayX汇率查询接口结束,响应参数:{}", JSON.toJSONString(payXAskPriceRspResult));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            if (payXAskPriceRspResult != null && StringUtils.equals(payXAskPriceRspResult.getCode(), String.valueOf(HttpStatus.SC_OK))
                    && payXAskPriceRspResult.getData() != null && payXAskPriceRspResult.getData().getExchangeRate() != null) {
                fxRate = payXAskPriceRspResult.getData().getExchangeRate();
            } else {
                log.error("调用PayX汇率查询接口失败,响应信息:{}", fxRate);
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }
        log.info("法币转法币汇率换算完成,原币种:{},目标币种:{},汇率:{}",
                billingCurrency, organizationBasicInfo.getPoolCurrencyCode(), fxRate);
        return fxRate;
    }

    /**
     * 组装payX询价请求参数
     *
     * @param sourceCurrency
     * @param organizationBasicInfo
     * @return
     */
    private PayXAskPriceReq assPayXAskPriceReq(String sourceCurrency, OrganizationBasicInfo organizationBasicInfo) {
        PayXAskPriceReq payXAskPriceReq = new PayXAskPriceReq();
        payXAskPriceReq.setToken(organizationBasicInfo.getMpcToken());
        payXAskPriceReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        payXAskPriceReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        payXAskPriceReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        payXAskPriceReq.setSourceCurrency(sourceCurrency);
        payXAskPriceReq.setTargetCurrency(organizationBasicInfo.getPoolCurrencyCode());
        return payXAskPriceReq;
    }

    /**
     * 发送机构费用扣除事件到mq中
     *
     * @param organizationFeeDetail
     * @param mpcToken
     * @param mpcGroupCode
     */
    public void sendOrganizationFeeDeductionToMq(OrganizationFeeDetail organizationFeeDetail, String mpcToken, String mpcGroupCode) {
        log.info("发送机构费用扣除事件到mq中");
        OrganizationFeeDeductionEventVO organizationFeeDeductionEventVO = new OrganizationFeeDeductionEventVO();
        organizationFeeDeductionEventVO.setFeeDetailId(organizationFeeDetail.getId());
        organizationFeeDeductionEventVO.setTransactionDatetime(organizationFeeDetail.getTransactionDatetime());
        organizationFeeDeductionEventVO.setMpcToken(mpcToken);
        organizationFeeDeductionEventVO.setMpcGroupCode(mpcGroupCode);
        rocketMqService.delayedSend(MqTopicConstant.ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC, organizationFeeDeductionEventVO, 10000, MqTopicConstant.DELAY_LEVEL_10S);
    }
}

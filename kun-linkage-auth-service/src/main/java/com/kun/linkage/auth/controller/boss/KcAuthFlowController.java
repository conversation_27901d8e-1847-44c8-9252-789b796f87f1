package com.kun.linkage.auth.controller.boss;

import com.kun.linkage.auth.facade.vo.boss.KcAuthFlowDetailRequestVO;
import com.kun.linkage.auth.facade.vo.boss.KcAuthFlowDetailVO;
import com.kun.linkage.auth.facade.vo.boss.KcAuthFlowPageVO;
import com.kun.linkage.auth.facade.vo.boss.KcAuthFlowRequestVO;
import com.kun.linkage.auth.service.KcAuthFlowService;
import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * title: <br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * @date 2025/7/9 16:00
 */
@Tag(name = "KcAuthFlowController", description = "KCARD授权记录查询")
@RestController
@RequestMapping("/boss/authFlow")
public class KcAuthFlowController {

    @Resource
    private KcAuthFlowService kcAuthFlowService;

    @Operation(description = "分页查询kcard授权记录列表", summary = "分页查询kcard授权记录列表")
    @VerifyVccBossPermission(verifyCodes = {"kc-auth-query"})
    @PostMapping("/pageList")
    public Result<PageResult<KcAuthFlowPageVO>> pageList(@RequestBody @Validated KcAuthFlowRequestVO requestVO) {
        return kcAuthFlowService.pageList(requestVO);
    }


    @Operation(description = "查询授权记录详情", summary = "查询授权记录详情")
    @VerifyVccBossPermission(verifyCodes = {"kc-auth-query"})
    @PostMapping("/detail")
    public Result<KcAuthFlowDetailVO> detail(@RequestBody @Validated KcAuthFlowDetailRequestVO requestVO) {
        return kcAuthFlowService.detail(requestVO);
    }
}

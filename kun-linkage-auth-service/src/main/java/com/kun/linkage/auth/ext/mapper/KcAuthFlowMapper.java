package com.kun.linkage.auth.ext.mapper;

import com.kun.linkage.auth.facade.vo.boss.KcAuthFlowDetailRequestVO;
import com.kun.linkage.auth.facade.vo.boss.KcAuthFlowDetailVO;
import com.kun.linkage.auth.facade.vo.boss.KcAuthFlowPageVO;
import com.kun.linkage.auth.facade.vo.boss.KcAuthFlowRequestVO;

import java.util.List;

/**
 * title: <br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * @date 2025/7/9 15:57
 */
public interface KcAuthFlowMapper {

    List<KcAuthFlowPageVO> pageList(KcAuthFlowRequestVO requestVO);

    KcAuthFlowDetailVO detail(KcAuthFlowDetailRequestVO requestVO);
}

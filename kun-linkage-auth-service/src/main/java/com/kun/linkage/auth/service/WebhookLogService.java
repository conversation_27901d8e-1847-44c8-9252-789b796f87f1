package com.kun.linkage.auth.service;

import brave.Tracer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kun.linkage.common.db.entity.ExternalWebhookLog;
import com.kun.linkage.common.db.mapper.ExternalWebhookLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Webhook日志服务
 * 
 * 负责ExternalWebhookLog的保存和更新操作
 * 使用独立事务确保日志数据不会随主事务回滚
 */
@Slf4j
@Service
public class WebhookLogService {

    @Resource
    private ExternalWebhookLogMapper externalWebhookLogMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    Tracer tracer;

    /**
     * 保存成功日志
     * 
     * 使用REQUIRES_NEW传播级别，确保在独立事务中执行
     * 即使主事务回滚，日志数据也会被保存
     * 
     * @param webhookUrl 调用的URL
     * @param request 请求内容
     * @param response 响应内容
     * @param httpStatus HTTP状态码
     * @param cost 请求耗时（毫秒）
     * @param organizationNo 机构号（可选）
     * @return 是否保存成功
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public boolean saveSuccessLog(String webhookUrl, Object request, String response, 
                                Integer httpStatus, long cost, String organizationNo) {
        try {
            // 创建日志实体
            ExternalWebhookLog logEntity = createLogEntity(webhookUrl, request, organizationNo);
            
            // 设置成功相关字段
            logEntity.setResponse(truncateResponse(response));
            logEntity.setHttpStatus(httpStatus != null ? httpStatus : null);
            logEntity.setStatus("S"); // 成功
            logEntity.setCost(BigDecimal.valueOf(cost));
            
            // 保存到数据库
            int result = externalWebhookLogMapper.insert(logEntity);
            
            if (result > 0) {
                log.debug("成功记录外部API调用日志到数据库: URL={}, 耗时={}ms", webhookUrl, cost);
                return true;
            } else {
                log.warn("外部API调用日志保存失败: URL={}, 耗时={}ms", webhookUrl, cost);
                return false;
            }
            
        } catch (Exception e) {
            log.error("保存外部API调用成功日志失败: URL={}, 耗时={}ms", webhookUrl, cost, e);
            return false;
        }
    }

    /**
     * 保存失败日志
     * 
     * 使用REQUIRES_NEW传播级别，确保在独立事务中执行
     * 即使主事务回滚，日志数据也会被保存
     * 
     * @param webhookUrl 调用的URL
     * @param request 请求内容
     * @param errorMsg 错误信息
     * @param cost 请求耗时（毫秒）
     * @param organizationNo 机构号（可选）
     * @return 是否保存成功
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public boolean saveFailureLog(String webhookUrl, Object request, String errorMsg, 
                                long cost, String organizationNo) {
        try {
            // 创建日志实体
            ExternalWebhookLog logEntity = createLogEntity(webhookUrl, request, organizationNo);
            
            // 设置失败相关字段
            logEntity.setResponse(null);
            logEntity.setHttpStatus(null);
            logEntity.setErrorMsg(errorMsg);
            logEntity.setStatus("F"); // 失败
            logEntity.setCost(BigDecimal.valueOf(cost));
            
            // 保存到数据库
            int result = externalWebhookLogMapper.insert(logEntity);
            
            if (result > 0) {
                log.debug("成功记录外部API调用失败日志到数据库: URL={}, 错误={}, 耗时={}ms", webhookUrl, errorMsg, cost);
                return true;
            } else {
                log.warn("外部API调用失败日志保存失败: URL={}, 错误={}, 耗时={}ms", webhookUrl, errorMsg, cost);
                return false;
            }
            
        } catch (Exception e) {
            log.error("保存外部API调用失败日志失败: URL={}, 错误={}, 耗时={}ms", webhookUrl, errorMsg, cost, e);
            return false;
        }
    }

    /**
     * 保存超时日志
     * 
     * 使用REQUIRES_NEW传播级别，确保在独立事务中执行
     * 即使主事务回滚，日志数据也会被保存
     * 
     * @param webhookUrl 调用的URL
     * @param request 请求内容
     * @param cost 请求耗时（毫秒）
     * @param organizationNo 机构号（可选）
     * @return 是否保存成功
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public boolean saveTimeoutLog(String webhookUrl, Object request, long cost, String organizationNo) {
        try {
            // 创建日志实体
            ExternalWebhookLog logEntity = createLogEntity(webhookUrl, request, organizationNo);
            
            // 设置超时相关字段
            logEntity.setResponse(null);
            logEntity.setHttpStatus(null);
            logEntity.setErrorMsg("请求超时");
            logEntity.setStatus("T"); // 超时
            logEntity.setCost(BigDecimal.valueOf(cost));
            
            // 保存到数据库
            int result = externalWebhookLogMapper.insert(logEntity);
            
            if (result > 0) {
                log.debug("成功记录外部API调用超时日志到数据库: URL={}, 耗时={}ms", webhookUrl, cost);
                return true;
            } else {
                log.warn("外部API调用超时日志保存失败: URL={}, 耗时={}ms", webhookUrl, cost);
                return false;
            }
            
        } catch (Exception e) {
            log.error("保存外部API调用超时日志失败: URL={}, 耗时={}ms", webhookUrl, cost, e);
            return false;
        }
    }

    /**
     * 更新日志状态
     * 
     * 使用REQUIRES_NEW传播级别，确保在独立事务中执行
     * 即使主事务回滚，日志更新也会被保存
     * 
     * @param id 日志ID
     * @param status 新状态
     * @param response 响应内容（可选）
     * @param errorMsg 错误信息（可选）
     * @return 是否更新成功
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public boolean updateLogStatus(String id, String status, String response, String errorMsg) {
        try {
            // 查询现有日志
            ExternalWebhookLog logEntity = externalWebhookLogMapper.selectById(id);
            if (logEntity == null) {
                log.warn("要更新的日志不存在: ID={}", id);
                return false;
            }
            
            // 更新字段
            logEntity.setStatus(status);
            logEntity.setUpdateDate(new Date());
            
            if (response != null) {
                logEntity.setResponse(truncateResponse(response));
            }
            
            if (errorMsg != null) {
                logEntity.setErrorMsg(errorMsg);
            }
            
            // 更新到数据库
            int result = externalWebhookLogMapper.updateById(logEntity);
            
            if (result > 0) {
                log.debug("成功更新外部API调用日志状态: ID={}, 状态={}", id, status);
                return true;
            } else {
                log.warn("外部API调用日志状态更新失败: ID={}, 状态={}", id, status);
                return false;
            }
            
        } catch (Exception e) {
            log.error("更新外部API调用日志状态失败: ID={}, 状态={}", id, status, e);
            return false;
        }
    }

    /**
     * 异步保存成功日志
     * 
     * @param webhookUrl 调用的URL
     * @param request 请求内容
     * @param response 响应内容
     * @param httpStatus HTTP状态码
     * @param cost 请求耗时（毫秒）
     * @param organizationNo 机构号（可选）
     */
    public void saveSuccessLogAsync(String webhookUrl, Object request, String response, 
                                  Integer httpStatus, long cost, String organizationNo) {
        // 使用CompletableFuture异步执行，避免阻塞主线程
        CompletableFuture.runAsync(() -> {
            try {
                saveSuccessLog(webhookUrl, request, response, httpStatus, cost, organizationNo);
            } catch (Exception e) {
                log.error("异步保存成功日志失败: URL={}", webhookUrl, e);
            }
        });
    }

    /**
     * 异步保存失败日志
     * 
     * @param webhookUrl 调用的URL
     * @param request 请求内容
     * @param errorMsg 错误信息
     * @param cost 请求耗时（毫秒）
     * @param organizationNo 机构号（可选）
     */
    public void saveFailureLogAsync(String webhookUrl, Object request, String errorMsg, 
                                  long cost, String organizationNo) {
        // 使用CompletableFuture异步执行，避免阻塞主线程
        CompletableFuture.runAsync(() -> {
            try {
                saveFailureLog(webhookUrl, request, errorMsg, cost, organizationNo);
            } catch (Exception e) {
                log.error("异步保存失败日志失败: URL={}", webhookUrl, e);
            }
        });
    }

    /**
     * 创建日志实体对象
     * 
     * @param webhookUrl 调用的URL
     * @param request 请求内容
     * @param organizationNo 机构号（可选）
     * @return 日志实体对象
     */
    private ExternalWebhookLog createLogEntity(String webhookUrl, Object request, String organizationNo) {
        ExternalWebhookLog logEntity = new ExternalWebhookLog();
        
        // 设置基础属性
        logEntity.setId(UUID.randomUUID().toString().replace("-", ""));
        logEntity.setWebhookUrl(webhookUrl);
        logEntity.setRequest(serializeRequest(request));
        logEntity.setCreateDate(new Date());
        logEntity.setUpdateDate(new Date());
        
        // 设置请求ID
        String requestId = organizationNo != null ? 
            organizationNo + "_" + System.currentTimeMillis() : 
            "REQ_" + System.currentTimeMillis();
        logEntity.setRequestId(requestId);
        
        // 设置必填字段
        logEntity.setTraceId(tracer.currentSpan().context().traceIdString());
        logEntity.setSpanId(tracer.currentSpan().context().spanIdString());
        logEntity.setMethod("POST"); // 默认POST方法
        
        return logEntity;
    }

    /**
     * 序列化请求对象
     * 
     * @param request 请求对象
     * @return 序列化后的字符串
     */
    private String serializeRequest(Object request) {
        try {
            return objectMapper.writeValueAsString(request);
        } catch (Exception e) {
            log.warn("序列化请求对象失败", e);
            // 序列化失败时，不填入默认值，返回null
            return null;
        }
    }

    /**
     * 截断超长内容
     * 
     * 当内容超过指定长度时，截断超长部分并添加截断标识
     * 
     * @param content 原始内容
     * @param maxLength 最大长度（字符数）
     * @return 截断后的内容
     */
    private String truncateContent(String content, int maxLength) {
        if (content == null) {
            return null;
        }
        
        if (content.length() <= maxLength) {
            return content;
        }
        
        // 截断超长部分，保留前maxLength个字符
        String truncated = content.substring(0, maxLength);
        
        // 添加截断标识
        return truncated + "\n... [内容已截断，原始长度: " + content.length() + " 字符]";
    }

    /**
     * 截断响应内容
     * 
     * 当响应数据超过MySQL TEXT字段最大长度时截断超长部分内容
     * MySQL TEXT最大长度为65,535字符（2^16 - 1）
     * 为了系统稳定性，设置为65,000字符，留出安全余量
     * 
     * @param response 原始响应内容
     * @return 截断后的响应内容
     */
    private String truncateResponse(String response) {
        // MySQL TEXT最大长度：65,535字符
        // 设置为65,000字符，留出安全余量
        return truncateContent(response, 65_000);
    }
} 
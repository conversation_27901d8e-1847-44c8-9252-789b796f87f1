package com.kun.linkage.auth.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 外部接口调用配置属性
 * 
 * 用于读取配置文件中的性能优化参数
 */
@Data
@Component
@ConfigurationProperties(prefix = "external.api")
public class ExternalWebhookApiProperties {

    /**
     * 连接池配置
     */
    private Connection connection = new Connection();

    /**
     * 超时配置
     */
    private Timeout timeout = new Timeout();

    /**
     * 重试配置
     */
    private Retry retry = new Retry();

    /**
     * 监控配置
     */
    private Monitoring monitoring = new Monitoring();

    /**
     * Redis存储配置
     */
    private Redis redis = new Redis();

    @Data
    public static class Connection {
        /**
         * 最大连接数
         */
        private int maxTotal = 200;

        /**
         * 每个路由最大连接数
         */
        private int defaultMaxPerRoute = 50;

        /**
         * 连接空闲验证时间（毫秒）
         */
        private int validateAfterInactivity = 30000;

        /**
         * 清理空闲连接间隔（秒）
         */
        private int evictIdleConnections = 60;
    }

    @Data
    public static class Timeout {
        /**
         * 连接超时（毫秒）
         */
        private int connect = 5000;

        /**
         * 读取超时（毫秒）
         */
        private int read = 30000;

        /**
         * 请求连接超时（毫秒）
         */
        private int request = 10000;
    }

    @Data
    public static class Retry {
        /**
         * 最大重试次数
         */
        private int maxAttempts = 3;

        /**
         * 退避乘数
         */
        private double backoffMultiplier = 2.0;

        /**
         * 初始重试间隔（毫秒）
         */
        private int initialInterval = 1000;
    }

    @Data
    public static class Monitoring {
        /**
         * 是否启用监控
         */
        private boolean enabled = true;

        /**
         * 是否记录慢查询
         */
        private boolean logSlowQueries = true;

        /**
         * 慢查询阈值（毫秒）
         */
        private int slowQueryThreshold = 5000;
    }

    @Data
    public static class Redis {
        /**
         * 是否启用Redis存储
         */
        private boolean enabled = true;

        /**
         * 数据保留天数
         */
        private int dataRetentionDays = 30;
    }
} 
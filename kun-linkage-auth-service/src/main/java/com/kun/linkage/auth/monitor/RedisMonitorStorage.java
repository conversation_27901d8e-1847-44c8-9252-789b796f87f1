package com.kun.linkage.auth.monitor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kun.linkage.auth.config.ExternalWebhookApiProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;
import java.util.HashSet;
import java.util.Set;
import java.util.List;

/**
 * Redis监控数据存储服务
 * 
 * 用于将外部接口调用监控数据持久化到Redis中
 * 支持30天的数据存储周期
 */
@Slf4j
@Component
public class RedisMonitorStorage {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private ExternalWebhookApiProperties externalApiProperties;

    // Redis键前缀
    private static final String MONITOR_PREFIX = "external:api:monitor";
    private static final String STATS_PREFIX = "external:api:stats";
    private static final String DAILY_PREFIX = "external:api:daily";
    
    // 日期格式化器
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 原子更新接口调用统计信息（带机构号）
     * 
     * @param url 接口地址
     * @param organizationNo 机构号
     * @param duration 调用耗时
     * @param isSuccess 是否成功
     */
    public void atomicUpdateApiCallStats(String url, String organizationNo, long duration, boolean isSuccess) {
        try {
            String baseKey = String.format("%s:%s", STATS_PREFIX, url);
            long expireDays = externalApiProperties.getRedis().getDataRetentionDays();
            
            // 使用Lua脚本进行原子操作
            String luaScript = 
                "local baseKey = KEYS[1] " +
                "local orgCode = ARGV[1] " +
                "local duration = tonumber(ARGV[2]) " +
                "local isSuccess = ARGV[3] " +
                "local expireDays = tonumber(ARGV[4]) " +
                "local currentTime = ARGV[5] " +
                " " +
                "local totalCallsKey = baseKey .. ':total_calls' " +
                "local successCallsKey = baseKey .. ':success_calls' " +
                "local failureCallsKey = baseKey .. ':failure_calls' " +
                "local totalTimeKey = baseKey .. ':total_time' " +
                "local maxTimeKey = baseKey .. ':max_time' " +
                "local minTimeKey = baseKey .. ':min_time' " +
                "local lastUpdateKey = baseKey .. ':last_update' " +
                " " +
                "local totalCalls = redis.call('INCR', totalCallsKey) " +
                "local totalTime = redis.call('INCRBY', totalTimeKey, duration) " +
                " " +
                "if isSuccess == 'true' then " +
                "    redis.call('INCR', successCallsKey) " +
                "else " +
                "    redis.call('INCR', failureCallsKey) " +
                "end " +
                " " +
                "local currentMax = redis.call('GET', maxTimeKey) " +
                "if not currentMax or tonumber(currentMax) < duration then " +
                "    redis.call('SET', maxTimeKey, duration) " +
                "end " +
                " " +
                "local currentMin = redis.call('GET', minTimeKey) " +
                "if not currentMin or tonumber(currentMin) > duration then " +
                "    redis.call('SET', minTimeKey, duration) " +
                "end " +
                " " +
                "redis.call('SET', lastUpdateKey, currentTime) " +
                " " +
                "local expireSeconds = expireDays * 24 * 3600 " +
                "redis.call('EXPIRE', totalCallsKey, expireSeconds) " +
                "redis.call('EXPIRE', successCallsKey, expireSeconds) " +
                "redis.call('EXPIRE', failureCallsKey, expireSeconds) " +
                "redis.call('EXPIRE', totalTimeKey, expireSeconds) " +
                "redis.call('EXPIRE', maxTimeKey, expireSeconds) " +
                "redis.call('EXPIRE', minTimeKey, expireSeconds) " +
                "redis.call('EXPIRE', lastUpdateKey, expireSeconds) " +
                " " +
                "return {totalCalls, totalTime}";
            
            String currentTime = String.valueOf(System.currentTimeMillis());
            String orgCodeValue = organizationNo != null ? organizationNo : "default";
            RedisScript<List> script = RedisScript.of(luaScript, List.class);
            List<Object> result = redisTemplate.execute(script, 
                java.util.Arrays.asList(baseKey),
                orgCodeValue,
                String.valueOf(duration),
                String.valueOf(isSuccess),
                String.valueOf(expireDays),
                currentTime
            );
            
            log.debug("原子更新接口调用统计信息: {}, 耗时: {}ms, 成功: {}", url, duration, isSuccess);
        } catch (Exception e) {
            log.error("原子更新接口调用统计信息失败: {}", url, e);
        }
    }

    /**
     * 从原子统计中获取接口调用统计信息
     * 
     * @param url 接口地址
     * @return 统计信息
     */
    public ExternalWebhookApiMonitor.ApiCallStats getAtomicApiCallStats(String url) {
        return getAtomicApiCallStats(url, null);
    }

    /**
     * 从原子统计中获取接口调用统计信息（带机构号）
     * 
     * @param url 接口地址
     * @param orgCode 机构号
     * @return 统计信息
     */
    public ExternalWebhookApiMonitor.ApiCallStats getAtomicApiCallStats(String url, String orgCode) {
        try {
            String baseKey = String.format("%s:%s", STATS_PREFIX, url);
            
            // 使用Lua脚本获取所有统计数据
            String luaScript = 
                "local baseKey = KEYS[1] " +
                " " +
                "local totalCallsKey = baseKey .. ':total_calls' " +
                "local successCallsKey = baseKey .. ':success_calls' " +
                "local failureCallsKey = baseKey .. ':failure_calls' " +
                "local totalTimeKey = baseKey .. ':total_time' " +
                "local maxTimeKey = baseKey .. ':max_time' " +
                "local minTimeKey = baseKey .. ':min_time' " +
                " " +
                "local totalCalls = redis.call('GET', totalCallsKey) or '0' " +
                "local successCalls = redis.call('GET', successCallsKey) or '0' " +
                "local failureCalls = redis.call('GET', failureCallsKey) or '0' " +
                "local totalTime = redis.call('GET', totalTimeKey) or '0' " +
                "local maxTime = redis.call('GET', maxTimeKey) or '0' " +
                "local minTime = redis.call('GET', minTimeKey) or '0' " +
                " " +
                "return {totalCalls, successCalls, failureCalls, totalTime, maxTime, minTime}";
            
            RedisScript<List> script = RedisScript.of(luaScript, List.class);
            List<Object> result = redisTemplate.execute(script, 
                java.util.Arrays.asList(baseKey)
            );
            
            if (result != null && result.size() >= 6) {
                long totalCalls = Long.parseLong(result.get(0).toString());
                long successCalls = Long.parseLong(result.get(1).toString());
                long failureCalls = Long.parseLong(result.get(2).toString());
                long totalTime = Long.parseLong(result.get(3).toString());
                long maxTime = Long.parseLong(result.get(4).toString());
                long minTime = Long.parseLong(result.get(5).toString());
                
                if (totalCalls > 0) {
                    double avgTime = (double) totalTime / totalCalls;
                    double successRate = (double) successCalls / totalCalls * 100;
                    
                    return ExternalWebhookApiMonitor.ApiCallStats.builder()
                        .url(url)
                        .orgCode(orgCode)
                        .totalCalls(totalCalls)
                        .successCalls(successCalls)
                        .failureCalls(failureCalls)
                        .successRate(successRate)
                        .avgTime(avgTime)
                        .maxTime(maxTime)
                        .minTime(minTime)
                        .totalTime(totalTime)
                        .build();
                }
            }
        } catch (Exception e) {
            log.error("获取原子统计信息失败: {}", url, e);
        }
        return null;
    }

    /**
     * 存储每日统计信息
     * 
     * @param date 日期
     * @param url 接口地址
     * @param stats 统计信息
     */
    public void storeDailyStats(String date, String url, ExternalWebhookApiMonitor.ApiCallStats stats) {
        storeDailyStats(date, url, null, stats);
    }

    /**
     * 存储每日统计信息（带机构号）
     * 
     * @param date 日期
     * @param url 接口地址
     * @param orgCode 机构号
     * @param stats 统计信息
     */
    public void storeDailyStats(String date, String url, String orgCode, ExternalWebhookApiMonitor.ApiCallStats stats) {
        try {
            String orgCodeValue = orgCode != null ? orgCode : "default";
            String key = String.format("%s:%s:%s:%s", DAILY_PREFIX, date, orgCodeValue, url);
            String value = objectMapper.writeValueAsString(stats);
            
            long expireDays = externalApiProperties.getRedis().getDataRetentionDays();
            redisTemplate.opsForValue().set(key, value, expireDays, TimeUnit.DAYS);
            log.debug("存储每日统计信息到Redis: {}, 过期时间: {}天", key, expireDays);
        } catch (JsonProcessingException e) {
            log.error("序列化每日统计信息失败: {}:{}:{}", date, orgCode, url, e);
        }
    }

    /**
     * 获取每日统计信息
     * 
     * @param date 日期
     * @param url 接口地址
     * @return 统计信息
     */
    public ExternalWebhookApiMonitor.ApiCallStats getDailyStats(String date, String url) {
        return getDailyStats(date, url, null);
    }

    /**
     * 获取每日统计信息（带机构号）
     * 
     * @param date 日期
     * @param url 接口地址
     * @param orgCode 机构号
     * @return 统计信息
     */
    public ExternalWebhookApiMonitor.ApiCallStats getDailyStats(String date, String url, String orgCode) {
        try {
            String orgCodeValue = orgCode != null ? orgCode : "default";
            String key = String.format("%s:%s:%s:%s", DAILY_PREFIX, date, orgCodeValue, url);
            Object value = redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                if (value instanceof String) {
                    return objectMapper.readValue((String) value, ExternalWebhookApiMonitor.ApiCallStats.class);
                } else {
                    return objectMapper.convertValue(value, ExternalWebhookApiMonitor.ApiCallStats.class);
                }
            }
        } catch (Exception e) {
            log.error("获取每日统计信息失败: {}:{}:{}", date, orgCode, url, e);
        }
        return null;
    }

    /**
     * 存储监控事件（使用唯一键避免覆盖）
     * 
     * @param event 监控事件
     */
    public void storeMonitorEvent(MonitorEvent event) {
        try {
            // 使用实例ID和时间戳确保唯一性
            String instanceId = getInstanceId();
            String uniqueKey = String.format("%s:event:%s:%s:%s", MONITOR_PREFIX, 
                    event.getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), 
                    instanceId,
                    System.currentTimeMillis());
            String value = objectMapper.writeValueAsString(event);
            
            long expireDays = externalApiProperties.getRedis().getDataRetentionDays();
            redisTemplate.opsForValue().set(uniqueKey, value, expireDays, TimeUnit.DAYS);
            log.debug("存储监控事件到Redis: {}, 过期时间: {}天", uniqueKey, expireDays);
        } catch (JsonProcessingException e) {
            log.error("序列化监控事件失败", e);
        }
    }

    /**
     * 获取实例ID（用于区分不同实例）
     * 
     * @return 实例ID
     */
    private String getInstanceId() {
        try {
            // 使用主机名和进程ID作为实例标识
            String hostname = java.net.InetAddress.getLocalHost().getHostName();
            String pid = java.lang.management.ManagementFactory.getRuntimeMXBean().getName().split("@")[0];
            return hostname + "-" + pid;
        } catch (Exception e) {
            // 如果获取失败，使用随机UUID
            return java.util.UUID.randomUUID().toString().substring(0, 8);
        }
    }

    /**
     * 清理过期数据
     * 
     * 删除超过30天的监控数据
     */
    public void cleanupExpiredData() {
        try {
            // 获取所有监控相关的键
            String pattern = MONITOR_PREFIX + ":*";
            Set<String> keys = redisTemplate.keys(pattern);
            
            if (keys != null && !keys.isEmpty()) {
                // 检查每个键的过期时间
                for (String key : keys) {
                    Long ttl = redisTemplate.getExpire(key, TimeUnit.SECONDS);
                    if (ttl != null && ttl <= 0) {
                        redisTemplate.delete(key);
                        log.debug("清理过期监控数据: {}", key);
                    }
                }
            }
            
            log.info("监控数据清理完成");
        } catch (Exception e) {
            log.error("清理过期监控数据失败", e);
        }
    }

    /**
     * 获取所有监控数据的键
     * 
     * @return 键集合
     */
    public Set<String> getAllMonitorKeys() {
        try {
            String pattern = MONITOR_PREFIX + ":*";
            Set<String> keys = redisTemplate.keys(pattern);
            return keys != null ? keys : new HashSet<>();
        } catch (Exception e) {
            log.error("获取监控数据键失败", e);
            return new HashSet<>();
        }
    }

    /**
     * 获取所有统计数据的键
     * 
     * @return 键集合
     */
    public Set<String> getAllStatsKeys() {
        try {
            String pattern = STATS_PREFIX + ":*";
            Set<String> keys = redisTemplate.keys(pattern);
            return keys != null ? keys : new HashSet<>();
        } catch (Exception e) {
            log.error("获取统计数据键失败", e);
            return new HashSet<>();
        }
    }

    /**
     * 获取指定机构号的统计数据键
     * 
     * @param orgCode 机构号
     * @return 键集合
     */
    public Set<String> getStatsKeysByOrgCode(String orgCode) {
        try {
            String pattern = STATS_PREFIX + ":" + orgCode + ":*";
            Set<String> keys = redisTemplate.keys(pattern);
            return keys != null ? keys : new HashSet<>();
        } catch (Exception e) {
            log.error("获取机构号统计数据键失败: {}", orgCode, e);
            return new HashSet<>();
        }
    }

    /**
     * 获取所有机构号列表
     * 
     * @return 机构号集合
     */
    public Set<String> getAllOrgCodes() {
        try {
            Set<String> orgCodes = new HashSet<>();
            String pattern = STATS_PREFIX + ":*";
            Set<String> keys = redisTemplate.keys(pattern);
            
            if (keys != null) {
                for (String key : keys) {
                    // 解析键中的机构号
                    String[] parts = key.split(":");
                    if (parts.length >= 3) {
                        orgCodes.add(parts[2]);
                    }
                }
            }
            
            return orgCodes;
        } catch (Exception e) {
            log.error("获取所有机构号失败", e);
            return new HashSet<>();
        }
    }

    /**
     * 监控事件
     */
    public static class MonitorEvent {
        private String url;
        private String orgCode; // 机构号
        private String eventType; // SUCCESS, FAILURE, SLOW_QUERY
        private long duration;
        private String errorMessage;
        private LocalDateTime timestamp;

        public MonitorEvent(String url, String orgCode, String eventType, long duration, String errorMessage) {
            this.url = url;
            this.orgCode = orgCode;
            this.eventType = eventType;
            this.duration = duration;
            this.errorMessage = errorMessage;
            this.timestamp = LocalDateTime.now();
        }

        // Getters and Setters
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        
        public String getOrgCode() { return orgCode; }
        public void setOrgCode(String orgCode) { this.orgCode = orgCode; }
        
        public String getEventType() { return eventType; }
        public void setEventType(String eventType) { this.eventType = eventType; }
        
        public long getDuration() { return duration; }
        public void setDuration(long duration) { this.duration = duration; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    }
} 
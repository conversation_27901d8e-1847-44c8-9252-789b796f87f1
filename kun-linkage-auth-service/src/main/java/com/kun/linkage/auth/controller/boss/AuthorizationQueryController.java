package com.kun.linkage.auth.controller.boss;

import com.kun.linkage.auth.facade.constant.AuthApplicationRequestParamNameConstant;
import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationInquiryDetailVO;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationInquiryPageVO;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationInquiryReuqestVO;
import com.kun.linkage.auth.service.AuthFlowService;
import com.kun.linkage.auth.utils.I18nMessageService;
import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.page.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Tag(name = "AuthorizationQueryController", description = "授权记录查询")
@RestController
@RequestMapping("/boss/authorization")
public class AuthorizationQueryController extends BaseVccBossController {

    @Resource
    private I18nMessageService i18nMessageService;

    @Resource
    private AuthFlowService authFlowService;

    /**
     * 分页查询授权记录
     *
     * @param requestVO
     * @return
     */
    @Operation(description = "分页查询授权记录", summary = "分页查询授权记录")
    @VerifyVccBossPermission(verifyCodes = {"kl-auth-query"})
    @PostMapping("/pageList")
    public Result<PageResult<AuthorizationInquiryPageVO>> list(@RequestBody AuthorizationInquiryReuqestVO requestVO) {
        if (StringUtils.isBlank(requestVO.getAuthorizationDateFrom())) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(AuthApplicationRequestParamNameConstant.AUTHORIZATION_DATE_FROM)));
        }
        if (StringUtils.isBlank(requestVO.getAuthorizationDateUntil())) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(AuthApplicationRequestParamNameConstant.AUTHORIZATION_DATE_TO)));
        }
        PageResult<AuthorizationInquiryPageVO> pageList = authFlowService.pageList(requestVO);
        return Result.success(pageList);
    }

    /**
     * 授权详情查询
     *
     * @param authTransId       授权交易ID
     * @param authorizationTime 授权时间
     * @return
     */
    @Operation(description = "授权详情查询", summary = "授权详情查询")
    @VerifyVccBossPermission(verifyCodes = {"kl-auth-query"})
    @GetMapping("detail")
    public Result<AuthorizationInquiryDetailVO> detail(@Parameter(name = "authTransId", description = "系统授权流水号", required = true) @RequestParam(name = "authTransId", required = false) String authTransId,
        @Parameter(name = "authorizationTime", description = "授权日期时间", required = true) @RequestParam(name = "authorizationTime", required = false) String authorizationTime) {
        if (StringUtils.isBlank(authTransId)) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(AuthApplicationRequestParamNameConstant.KL_AUTH_TRANS_ID)));
        }
        if (StringUtils.isBlank(authorizationTime)) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(AuthApplicationRequestParamNameConstant.AUTHORIZATION_TIME)));
        }
        AuthorizationInquiryDetailVO detail = authFlowService.detail(authTransId, authorizationTime);
        if (detail == null) {
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        } else {
            return Result.success(detail);
        }
    }
}

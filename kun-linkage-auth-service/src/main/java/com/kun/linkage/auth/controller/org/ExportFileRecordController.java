package com.kun.linkage.auth.controller.org;

import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.auth.facade.vo.boss.ExportFileRecordQueryVO;
import com.kun.linkage.auth.facade.vo.boss.ExportFileRecordVO;
import com.kun.linkage.auth.service.ExportFileRecordService;
import com.kun.linkage.auth.utils.I18nMessageService;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.KLExportFileRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Tag(name = "ExportFileRecordController", description = "导出文件记录管理")
@RestController
@RequestMapping("/org/exportFileRecord")
public class ExportFileRecordController extends BaseVccBossController {

    @Resource
    private ExportFileRecordService exportFileRecordService;

    @Resource
    private I18nMessageService i18nMessageService;

    /**
     * 分页查询导出文件记录
     *
     * @param queryVO
     * @return
     */
    @Operation(description = "分页查询导出文件记录", summary = "分页查询导出文件记录")
    @PostMapping("/pageList")
    public Result<PageResult<ExportFileRecordVO>> pageList(@RequestBody ExportFileRecordQueryVO queryVO) {
        PageResult<ExportFileRecordVO> pageResult = exportFileRecordService.pageList(queryVO);
        return Result.success(pageResult);
    }

    /**
     * 获取文件下载URL
     *
     * @param fileRecordId 文件记录ID
     * @return
     */
    @Operation(description = "获取文件下载URL", summary = "获取文件下载URL")
    @GetMapping("/downloadFile/{fileRecordId}")
    public Result<String> downloadFile(
            @Parameter(name = "fileRecordId", description = "文件记录ID", required = true)
            @PathVariable String fileRecordId) {
        
        if (StringUtils.isBlank(fileRecordId)) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(), "fileRecordId"));
        }

        // 查询文件记录
        KLExportFileRecord fileRecord = exportFileRecordService.getById(fileRecordId);
        if (fileRecord == null) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.FILE_NOT_FOUND.getCode(),
                    i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.FILE_NOT_FOUND.getCode()));
        }
        return Result.success(fileRecord.getS3Url());
    }
}

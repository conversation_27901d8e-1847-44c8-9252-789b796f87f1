package com.kun.linkage.auth.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kun.linkage.auth.config.ExternalWebhookApiProperties;
import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.auth.facade.vo.webhook.BaseWebHookRequestVO;
import com.kun.linkage.auth.facade.vo.webhook.WebHookResult;
import com.kun.linkage.auth.monitor.ExternalWebhookApiMonitor;
import com.kun.linkage.auth.utils.StopWatchUtil;
import com.kun.linkage.common.base.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestTemplate;


import javax.annotation.Resource;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * 简化版外部接口调用服务
 * 
 * 只使用URL配置，不依赖数据库配置表
 */
@Slf4j
@Service
public class ExternalWebhookService {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private ExternalWebhookApiProperties externalApiProperties;

    @Resource
    private ExternalWebhookApiMonitor externalApiMonitor;

    @Resource
    private WebhookLogService webhookLogService;

    @Resource(name = "externalApiAsyncExecutor")
    private Executor externalApiAsyncExecutor;

    /**
     * 直接传入URL调用外部接口
     * 
     * @param url 外部接口URL
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 业务响应数据类型（如：PaymentResponse.class）
     * @return 响应结果
     */
    public <T> WebHookResult<T> callApiByUrl(String url, BaseWebHookRequestVO requestBody, Map<String, String> headers,
        Class<T> responseType) {
        if (url == null || url.trim().isEmpty()) {
            log.error("外部接口URL为空, 不调用外部接口");
            return null;
        }
        if (requestBody == null) {
            log.warn("请求体为空,不调用外部接口: {}", url);
            return null;
        }
        return callExternalApi(url, requestBody, headers, responseType);
    }



    /**
     * 根据机构信息调用外部接口（泛型版本）- 无重试
     * 
     * @param externalApiUrl 外部接口URL（从机构信息表获取）
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 业务响应数据类型（如：PaymentResponse.class）
     * @return 响应结果
     */
    public <T> WebHookResult<T> callApiByOrganization(String externalApiUrl, BaseWebHookRequestVO requestBody, Map<String, String> headers, Class<T> responseType) {
        String organizationNo = requestBody.getOrganizationNo();
        if (organizationNo == null || organizationNo.trim().isEmpty()) {
            log.error("请求体中的机构号不能为空");
            throw new BusinessException(KunLinkageAuthResponseCodeConstant.MID_NOT_FOUND.getCode());
        }
        
        if (externalApiUrl == null || externalApiUrl.trim().isEmpty()) {
            log.warn("机构号 {} 的外部接口URL不能为空", organizationNo);
            return null;
        }
        
        log.info("根据机构信息调用外部接口: 机构号={}, URL={}", organizationNo, externalApiUrl);
        return callExternalApiWithoutRetry(externalApiUrl, requestBody, headers, responseType);
    }

    /**
     * 根据机构信息调用外部接口（泛型版本）- 带重试
     * 
     * @param externalApiUrl 外部接口URL（从机构信息表获取）
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 业务响应数据类型（如：PaymentResponse.class）
     * @return 响应结果
     */
    @NewSpan
    public <T> WebHookResult<T> callApiByOrganizationWithRetry(String externalApiUrl, BaseWebHookRequestVO requestBody, Map<String, String> headers, Class<T> responseType) {
        String organizationNo = requestBody.getOrganizationNo();
        if (organizationNo == null || organizationNo.trim().isEmpty()) {
            log.warn("请求体中的机构号为空");
            return null;
        }
        
        if (externalApiUrl == null || externalApiUrl.trim().isEmpty()) {
            log.error("机构号 {} 的外部接口URL不能为空", organizationNo);
            return null;
        }
        
        log.info("根据机构信息调用外部接口: 机构号={}, URL={}", organizationNo, externalApiUrl);
        return callExternalApiWithRetry(externalApiUrl, requestBody, headers, responseType);
    }



    /**
     * 调用外部接口（泛型版本）- 无重试
     * 
     * @param url 接口地址
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 业务响应数据类型（如：PaymentResponse.class）
     * @return 响应结果
     */
    public <T> WebHookResult<T> callExternalApi(String url, BaseWebHookRequestVO requestBody, Map<String, String> headers, Class<T> responseType) {
        return callExternalApiWithoutRetry(url, requestBody, headers, responseType);
    }

    /**
     * 调用外部接口（泛型版本）- 带重试
     * 
     * @param url 接口地址
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 业务响应数据类型（如：PaymentResponse.class）
     * @return 响应结果
     */
    public <T> WebHookResult<T> callExternalApiWithRetry(String url, BaseWebHookRequestVO requestBody, Map<String, String> headers, Class<T> responseType) {
        return callExternalApiWithRetryInternal(url, requestBody, headers, responseType, externalApiProperties.getRetry().getMaxAttempts());
    }

    /**
     * 调用外部接口（泛型版本）- 无重试
     * 
     * @param url 接口地址
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 业务响应数据类型（如：PaymentResponse.class）
     * @return 响应结果
     */
    public <T> WebHookResult<T> callExternalApiWithoutRetry(String url, BaseWebHookRequestVO requestBody, Map<String, String> headers, Class<T> responseType) {
        StopWatch stopWatch = externalApiMonitor.recordCallStart(url);
        
        try {
            log.info("开始调用外部接口: {}, 请求体: {}", url, requestBody);
            
            // 构建请求头
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            if (headers != null) {
                headers.forEach(httpHeaders::add);
            }

            // 构建请求实体
            HttpEntity<BaseWebHookRequestVO> requestEntity = new HttpEntity<>(requestBody, httpHeaders);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
            
            // 获取耗时并检查慢查询
            long duration = StopWatchUtil.stopAndGetTotalTime(stopWatch);
            if (StopWatchUtil.isSlowQuery(stopWatch, externalApiProperties.getMonitoring().getSlowQueryThreshold())) {
                log.warn("慢查询警告: 外部接口调用耗时 {}ms, 超过阈值 {}ms, URL: {}", 
                        duration, externalApiProperties.getMonitoring().getSlowQueryThreshold(), url);
            }
            
            log.info("外部接口调用完成: {}, 耗时: {}ms, 响应: {}", url, duration, response.getBody());
            
            // 根据HTTP状态码判断成功状态
            if (response.getStatusCodeValue() == 200) {
                // HTTP 200，认为调用成功
                externalApiMonitor.recordCallSuccess(requestBody.getOrganizationNo(), url, stopWatch);
                recordSuccessLog(url, requestBody, response.getBody(), response.getStatusCodeValue(), duration, requestBody.getOrganizationNo());
                log.info("外部接口调用成功: {}, 耗时: {}ms, HTTP状态码: {}", url, duration, response.getStatusCodeValue());
                
                // 解析响应数据
                WebHookResult<T> result = parseResponseAndBuildResult(response.getBody(), responseType, requestBody.getRequestNo());
                return result;
            } else {
                // HTTP状态码不是200，认为调用失败
                externalApiMonitor.recordCallFailure(requestBody.getOrganizationNo(), url, stopWatch, "HTTP status not 200: " + response.getStatusCodeValue());
                recordFailureLog(url, requestBody, "HTTP status not 200: " + response.getStatusCodeValue(), duration, requestBody.getOrganizationNo());
                log.warn("外部接口调用失败: {}, 耗时: {}ms, HTTP状态码: {}", url, duration, response.getStatusCodeValue());
                
                // 返回失败结果
                return new WebHookResult<>(
                    requestBody.getRequestNo(),
                    KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode(),
                    null
                );
            }
            
        } catch (Exception e) {
            // 记录失败
            externalApiMonitor.recordCallFailure(requestBody.getOrganizationNo(), url, stopWatch, e.getMessage());
            long duration = StopWatchUtil.stopAndGetTotalTime(stopWatch);
            
            // 判断是否为超时异常
            boolean isTimeout = isTimeoutException(e);
            
            if (isTimeout) {
                // 超时特殊记录
                log.error("外部接口调用超时: {}, 耗时: {}ms, 错误: {}", url, duration, e.getMessage(), e);
                recordTimeoutLog(url, requestBody, duration, null);
            } else {
                // 普通失败记录
                log.error("外部接口调用失败: {}, 耗时: {}ms, 错误: {}", url, duration, e.getMessage(), e);
                recordFailureLog(url, requestBody, e.getMessage(), duration, null);
            }
            
            // 构建失败结果
            String errorCode = isTimeout ? KunLinkageAuthResponseCodeConstant.TIMEOUT.getCode() : KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode();
            String errorMessage = isTimeout ? KunLinkageAuthResponseCodeConstant.TIMEOUT.getMessage() : null;
            
            WebHookResult<T> result = new WebHookResult<>(
                requestBody.getRequestNo(),
                errorCode, 
                errorMessage
            );
            
            return result;
        }
    }



    /**
     * 带重试机制调用外部接口（泛型版本）
     * 
     * @param url 接口地址
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 响应类型
     * @param maxRetries 最大重试次数
     * @return 响应结果
     */
    public <T> WebHookResult<T> callExternalApiWithRetryInternal(String url, BaseWebHookRequestVO requestBody, Map<String, String> headers, Class<T> responseType, int maxRetries) {
        int retryCount = 0;
        StopWatch stopWatch = externalApiMonitor.recordCallStart(url);
        
        while (retryCount <= maxRetries) {
            try {
                if (retryCount > 0) {
                    log.info("外部接口调用重试: {}, 第 {} 次重试", url, retryCount);
                } else {
                    log.info("开始调用外部接口: {}, 请求体: {}", url, requestBody);
                }
                
                // 构建请求头
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                if (headers != null) {
                    headers.forEach(httpHeaders::add);
                }

                // 构建请求实体
                HttpEntity<BaseWebHookRequestVO> requestEntity = new HttpEntity<>(requestBody, httpHeaders);

                // 发送请求
                ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
                
                // 获取耗时并检查慢查询
                long duration = StopWatchUtil.stopAndGetTotalTime(stopWatch);
                if (StopWatchUtil.isSlowQuery(stopWatch, externalApiProperties.getMonitoring().getSlowQueryThreshold())) {
                    log.warn("慢查询警告: 外部接口调用耗时 {}ms, 超过阈值 {}ms, URL: {}", 
                            duration, externalApiProperties.getMonitoring().getSlowQueryThreshold(), url);
                }
                
                log.info("外部接口调用完成: {}, 耗时: {}ms, 响应: {}", url, duration, response.getBody());
                
                // 解析响应并判断成功状态
                WebHookResult<T> result = parseResponseAndBuildResult(response.getBody(), responseType, requestBody.getRequestNo());
                
                // 根据响应结果判断成功或失败
                if ("0000".equals(result.getCode())) {
                    // 业务成功
                    externalApiMonitor.recordCallSuccess(requestBody.getOrganizationNo(), url, stopWatch);
                    recordSuccessLog(url, requestBody, response.getBody(), response.getStatusCodeValue(), duration, null);
                    log.info("外部接口调用业务成功: {}, 耗时: {}ms", url, duration);
                    return result;
                } else {
                    // 业务失败
                    externalApiMonitor.recordCallFailure(requestBody.getOrganizationNo(), url, stopWatch, "Business failed with code: " + result.getCode());
                    recordFailureLog(url, requestBody, "Business failed with code: " + result.getCode() + ", message: " + result.getMessage(), duration, null);
                    log.warn("外部接口调用业务失败: {}, 耗时: {}ms, 错误码: {}, 错误信息: {}", 
                            url, duration, result.getCode(), result.getMessage());
                }
                requestBody.setRequestNo(UUID.randomUUID().toString());
            } catch (Exception e) {
                retryCount++;
                
                if (retryCount > maxRetries) {
                    // 记录最终失败
                    externalApiMonitor.recordCallFailure(requestBody.getOrganizationNo(), url, stopWatch, e.getMessage());
                    long duration = StopWatchUtil.stopAndGetTotalTime(stopWatch);
                    
                    // 判断是否为超时异常
                    boolean isTimeout = isTimeoutException(e);
                    
                    if (isTimeout) {
                        // 超时特殊记录
                        log.error("外部接口调用最终超时: {}, 耗时: {}ms, 重试次数: {}, 错误: {}", 
                                url, duration, maxRetries, e.getMessage(), e);
                        recordTimeoutLog(url, requestBody, duration, null);
                        // 监控超时统计
                        externalApiMonitor.recordCallFailure(requestBody.getOrganizationNo(), url, stopWatch, "Timeout after " + retryCount + " retries: " + e.getMessage());
                    } else {
                        // 普通失败记录
                        log.error("外部接口调用最终失败: {}, 耗时: {}ms, 重试次数: {}, 错误: {}", 
                                url, duration, maxRetries, e.getMessage(), e);
                        recordFailureLog(url, requestBody, e.getMessage(), duration, null);
                        // 监控失败统计
                        externalApiMonitor.recordCallFailure(requestBody.getOrganizationNo(), url, stopWatch, "Failed after " + retryCount + " retries: " + e.getMessage());
                    }
                    
                    // 构建失败结果
                    String errorCode = isTimeout ? KunLinkageAuthResponseCodeConstant.TIMEOUT.getCode() : KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode();
                    String errorMessage = isTimeout ? KunLinkageAuthResponseCodeConstant.TIMEOUT.getMessage() : null;
                    
                    WebHookResult<T> result = new WebHookResult<>(
                        requestBody.getRequestNo(),
                        errorCode, 
                        errorMessage
                    );
                    
                    return result;
                }
                
                long duration = StopWatchUtil.stopAndGetTotalTime(stopWatch);
                
                // 判断是否为超时异常
                boolean isTimeout = isTimeoutException(e);
                
                if (isTimeout) {
                    log.warn("外部接口调用超时: {}, 耗时: {}ms, 第 {} 次重试, 错误: {}", 
                            url, duration, retryCount, e.getMessage());
                    // 监控重试超时（记录为失败，但包含重试信息）
                    externalApiMonitor.recordCallFailure(requestBody.getOrganizationNo(), url, stopWatch, "Retry timeout #" + retryCount + ": " + e.getMessage());
                } else {
                    log.warn("外部接口调用失败: {}, 耗时: {}ms, 第 {} 次重试, 错误: {}", 
                            url, duration, retryCount, e.getMessage());
                    // 监控重试失败（记录为失败，但包含重试信息）
                    externalApiMonitor.recordCallFailure(requestBody.getOrganizationNo(), url, stopWatch, "Retry failure #" + retryCount + ": " + e.getMessage());
                }
                
                // 指数退避重试
                try {
                    long backoffTime = (long) (externalApiProperties.getRetry().getInitialInterval() * 
                            Math.pow(externalApiProperties.getRetry().getBackoffMultiplier(), retryCount - 1));
                    log.debug("外部接口调用重试等待: {}ms, URL: {}, 第 {} 次重试", backoffTime, url, retryCount);
                    Thread.sleep(backoffTime);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.error("外部接口调用重试被中断: {}, 第 {} 次重试", url, retryCount, ie);
                    externalApiMonitor.recordCallFailure(requestBody.getOrganizationNo(), url, stopWatch, "Retry interrupted #" + retryCount + ": " + ie.getMessage());
                    log.error("重试被中断", ie);
                }
                // 重置请求号以避免重复请求
                requestBody.setRequestNo(UUID.randomUUID().toString());
            }
        }
        
        log.error("外部接口调用失败，已达到最大重试次数");
        return null;
    }



    /**
     * 异步调用外部接口（泛型版本）- 无重试
     * 
     * @param url 接口地址
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 业务响应数据类型（如：PaymentResponse.class）
     * @return CompletableFuture包装的响应结果
     */
    public <T> CompletableFuture<WebHookResult<T>> callExternalApiAsync(String url, BaseWebHookRequestVO requestBody, Map<String, String> headers, Class<T> responseType) {
        // 验证机构号
        String organizationNo = requestBody.getOrganizationNo();
        if (organizationNo == null || organizationNo.trim().isEmpty()) {
            log.warn("请求体中的机构号为空");
            return null;
        }

        log.info("异步调用外部接口: 机构号={}, URL={}", organizationNo, url);
        return callExternalApiAsync(url, requestBody, headers, responseType, 30, TimeUnit.SECONDS);
    }

    /**
     * 异步调用外部接口（泛型版本）- 无重试，带超时
     * 
     * @param url 接口地址
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 响应类型
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return CompletableFuture包装的响应结果
     */
    public <T> CompletableFuture<WebHookResult<T>> callExternalApiAsync(String url, BaseWebHookRequestVO requestBody, Map<String, String> headers, Class<T> responseType, long timeout, TimeUnit unit) {
        CompletableFuture<WebHookResult<T>> future = CompletableFuture.supplyAsync(() -> {
            try {
                return callExternalApiWithoutRetry(url, requestBody, headers, responseType);
            } catch (Exception e) {
                log.error("异步调用外部接口异常: URL={}, 错误={}", url, e.getMessage(), e);
                throw new BusinessException(KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode());
            }
        }, externalApiAsyncExecutor);

        // 添加超时控制（Java 8兼容）
        CompletableFuture<WebHookResult<T>> timeoutFuture = new CompletableFuture<>();
        
        // 启动超时任务
        externalApiAsyncExecutor.execute(() -> {
            try {
                Thread.sleep(unit.toMillis(timeout));
                timeoutFuture.complete(new WebHookResult<>(
                    requestBody.getRequestNo(),
                    KunLinkageAuthResponseCodeConstant.TIMEOUT.getCode(),
                    "Async call timeout after " + timeout + " " + unit.name().toLowerCase()
                ));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        // 使用anyOf实现超时控制
        return CompletableFuture.anyOf(future, timeoutFuture)
                .thenApply(result -> {
                    if (result instanceof WebHookResult) {
                        return (WebHookResult<T>) result;
                    } else {
                        return (WebHookResult<T>) result;
                    }
                })
                .exceptionally(throwable -> {
                    String errorMessage = throwable.getCause() != null ?
                        throwable.getCause().getMessage() : throwable.toString();
                    log.error("异步调用外部接口失败: URL={}, 错误={}", url, errorMessage, throwable);
                    return new WebHookResult<>(
                        requestBody.getRequestNo(),
                        KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode(),
                        null
                    );
                });
    }

    /**
     * 异步调用外部接口（泛型版本）- 带重试
     * 
     * @param url 接口地址
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 响应类型
     * @return CompletableFuture包装的响应结果
     */
    public <T> CompletableFuture<WebHookResult<T>> callExternalApiAsyncWithRetry(String url, BaseWebHookRequestVO requestBody, Map<String, String> headers, Class<T> responseType) {
        // 验证机构号
        String organizationNo = requestBody.getOrganizationNo();
        if (organizationNo == null || organizationNo.trim().isEmpty()) {
            log.warn("请求体中的机构号为空");
            return null;
        }

        log.info("异步调用外部接口（带重试）: 机构号={}, URL={}", organizationNo, url);
        return callExternalApiAsyncWithRetry(url, requestBody, headers, responseType, 60, TimeUnit.SECONDS);
    }

    /**
     * 异步调用外部接口（泛型版本）- 带重试，带超时
     * 
     * @param url 接口地址
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 响应类型
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return CompletableFuture包装的响应结果
     */
    public <T> CompletableFuture<WebHookResult<T>> callExternalApiAsyncWithRetry(String url, BaseWebHookRequestVO requestBody, Map<String, String> headers, Class<T> responseType, long timeout, TimeUnit unit) {
        CompletableFuture<WebHookResult<T>> future = CompletableFuture.supplyAsync(() -> {
            try {
                return callExternalApiWithRetry(url, requestBody, headers, responseType);
            } catch (Exception e) {
                log.error("异步调用外部接口异常（带重试）: URL={}, 错误={}", url, e.getMessage(), e);
                throw new BusinessException(KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode());
            }
        }, externalApiAsyncExecutor);

        // 添加超时控制（Java 8兼容）
        CompletableFuture<WebHookResult<T>> timeoutFuture = new CompletableFuture<>();
        
        // 启动超时任务
        externalApiAsyncExecutor.execute(() -> {
            try {
                Thread.sleep(unit.toMillis(timeout));
                timeoutFuture.complete(new WebHookResult<>(
                    requestBody.getRequestNo(),
                    KunLinkageAuthResponseCodeConstant.TIMEOUT.getCode(),
                    "Async call with retry timeout after " + timeout + " " + unit.name().toLowerCase()
                ));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        // 使用anyOf实现超时控制
        return CompletableFuture.anyOf(future, timeoutFuture)
                .thenApply(result -> {
                    if (result instanceof WebHookResult) {
                        return (WebHookResult<T>) result;
                    } else {
                        return (WebHookResult<T>) result;
                    }
                })
                .exceptionally(throwable -> {
                    String errorMessage = throwable.getCause() != null ?
                        throwable.getCause().getMessage() : throwable.toString();
                    log.error("异步调用外部接口失败（带重试）: URL={}, 错误={}", url, errorMessage, throwable);
                    return new WebHookResult<>(
                        requestBody.getRequestNo(),
                        KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode(),
                        null
                    );
                });
    }



    /**
     * 直接传入URL异步调用外部接口（泛型版本）
     * 
     * @param url 外部接口URL
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 业务响应数据类型（如：PaymentResponse.class）
     * @return CompletableFuture包装的响应结果
     */
    public <T> CompletableFuture<WebHookResult<T>> callApiByUrlAsync(String url, BaseWebHookRequestVO requestBody, Map<String, String> headers, Class<T> responseType) {
        // 验证机构号
        String organizationNo = requestBody.getOrganizationNo();
        if (organizationNo == null || organizationNo.trim().isEmpty()) {
            log.warn("请求体中的机构号为空");
            return CompletableFuture.completedFuture(null);
        }

        log.info("异步调用外部接口: 机构号={}, URL={}", organizationNo, url);
        return CompletableFuture.supplyAsync(() -> {
            try {
                return callApiByUrl(url, requestBody, headers, responseType);
            } catch (Exception e) {
                log.error("异步调用外部接口异常: URL={}, 错误={}", url, e.getMessage(), e);
                throw new BusinessException(KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode());
            }
        }, externalApiAsyncExecutor);
    }



    /**
     * 根据机构信息异步调用外部接口（泛型版本）- 无重试
     * 
     * @param externalApiUrl 外部接口URL（从机构信息表获取）
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 业务响应数据类型（如：PaymentResponse.class）
     * @return CompletableFuture包装的响应结果
     */
    public <T> CompletableFuture<WebHookResult<T>> callApiByOrganizationAsync(String externalApiUrl, BaseWebHookRequestVO requestBody, Map<String, String> headers, Class<T> responseType) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return callApiByOrganization(externalApiUrl, requestBody, headers, responseType);
            } catch (Exception e) {
                log.error("异步调用外部接口异常: URL={}, 错误={}", externalApiUrl, e.getMessage(), e);
                throw new BusinessException(KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode());
            }
        }, externalApiAsyncExecutor);
    }

    /**
     * 根据机构信息异步调用外部接口（泛型版本）- 带重试
     * 
     * @param externalApiUrl 外部接口URL（从机构信息表获取）
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 业务响应数据类型（如：PaymentResponse.class）
     * @return CompletableFuture包装的响应结果
     */
    public <T> CompletableFuture<WebHookResult<T>> callApiByOrganizationAsyncWithRetry(String externalApiUrl, BaseWebHookRequestVO requestBody, Map<String, String> headers, Class<T> responseType) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return callApiByOrganizationWithRetry(externalApiUrl, requestBody, headers, responseType);
            } catch (Exception e) {
                log.error("异步调用外部接口异常（带重试）: URL={}, 错误={}", externalApiUrl, e.getMessage(), e);
                throw new BusinessException(KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode());
            }
        }, externalApiAsyncExecutor);
    }

    /**
     * 记录成功的API调用日志
     * 
     * @param webhookUrl 调用的URL
     * @param request 请求内容
     * @param response 响应内容
     * @param httpStatus HTTP状态码
     * @param cost 请求耗时（毫秒）
     * @param organizationNo 机构号（可选）
     */
    private void recordSuccessLog(String webhookUrl, Object request, String response, 
                                Integer httpStatus, long cost, String organizationNo) {
        try {
            // 使用WebhookLogService保存日志，确保事务独立
            boolean success = webhookLogService.saveSuccessLog(webhookUrl, request, response, httpStatus, cost, organizationNo);
            if (!success) {
                log.warn("外部API调用成功日志保存失败: URL={}, 耗时={}ms", webhookUrl, cost);
            }
        } catch (Exception e) {
            log.error("记录外部API调用成功日志失败: URL={}, 耗时={}ms", webhookUrl, cost, e);
        }
    }

    /**
     * 记录失败的API调用日志
     * 
     * @param webhookUrl 调用的URL
     * @param request 请求内容
     * @param errorMsg 错误信息
     * @param cost 请求耗时（毫秒）
     * @param organizationNo 机构号（可选）
     */
    private void recordFailureLog(String webhookUrl, Object request, String errorMsg, 
                                long cost, String organizationNo) {
        try {
            // 使用WebhookLogService保存日志，确保事务独立
            boolean success = webhookLogService.saveFailureLog(webhookUrl, request, errorMsg, cost, organizationNo);
            if (!success) {
                log.warn("外部API调用失败日志保存失败: URL={}, 错误={}, 耗时={}ms", webhookUrl, errorMsg, cost);
            }
        } catch (Exception e) {
            log.error("记录外部API调用失败日志失败: URL={}, 错误={}, 耗时={}ms", webhookUrl, errorMsg, cost, e);
        }
    }

    /**
     * 记录超时的API调用日志
     * 
     * @param webhookUrl 调用的URL
     * @param request 请求内容
     * @param cost 请求耗时（毫秒）
     * @param organizationNo 机构号（可选）
     */
    private void recordTimeoutLog(String webhookUrl, Object request, long cost, String organizationNo) {
        try {
            // 使用WebhookLogService保存超时日志，确保事务独立
            boolean success = webhookLogService.saveTimeoutLog(webhookUrl, request, cost, organizationNo);
            if (!success) {
                log.warn("外部API调用超时日志保存失败: URL={}, 耗时={}ms", webhookUrl, cost);
            }
        } catch (Exception e) {
            log.error("记录外部API调用超时日志失败: URL={}, 耗时={}ms", webhookUrl, cost, e);
        }
    }

    /**
     * 判断是否为超时异常
     * 
     * @param e 异常对象
     * @return 是否为超时异常
     */
    private boolean isTimeoutException(Exception e) {
        if (e == null) {
            return false;
        }
        
        // 检查异常类型
        if (e instanceof org.springframework.web.client.ResourceAccessException) {
            String message = e.getMessage();
            return message != null && (
                message.contains("timeout") || 
                message.contains("超时") || 
                message.contains("Read timed out") ||
                message.contains("Connect timed out")
            );
        }
        
        // 检查异常原因
        Throwable cause = e.getCause();
        if (cause != null) {
            if (cause instanceof java.net.SocketTimeoutException) {
                return true;
            }
            if (cause instanceof java.net.ConnectException) {
                String message = cause.getMessage();
                return message != null && message.contains("timeout");
            }
        }
        
        // 检查异常消息
        String message = e.getMessage();
        return message != null && (
            message.contains("timeout") || 
            message.contains("超时") || 
            message.contains("Read timed out") ||
            message.contains("Connect timed out") ||
            message.contains("Connection timed out")
        );
    }

    /**
     * 解析响应并构建WebHookResult
     * 
     * 响应数据必须是标准的WebHookResult结构，其他格式一律不认：
     * - 只有标准WebHookResult格式才被接受
     * - 其他任何格式都返回解析失败
     * - 处理返回类型为空或Void.class的情况
     * 
     * @param responseBody 响应体字符串
     * @param responseType 响应类型
     * @param requestId 请求ID
     * @return WebHookResult对象
     */
    private <T> WebHookResult<T> parseResponseAndBuildResult(String responseBody, Class<T> responseType, String requestId) {
        try {
            // 只接受标准WebHookResult格式
            if (responseBody == null || !responseBody.contains("\"code\"")) {
                log.warn("响应体不是标准WebHookResult格式: {}", responseBody);
                return new WebHookResult<>(requestId, KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode(), null);
            }

            // 处理返回类型为空或Void.class的情况
            if (responseType == null || responseType == Void.class) {
                // 对于无返回类型的情况，只解析code和message字段
                try {
                    // 创建一个简单的Map来解析响应
                    @SuppressWarnings("unchecked")
                    WebHookResult<T> result = objectMapper.readValue(responseBody, 
                            objectMapper.getTypeFactory().constructParametricType(WebHookResult.class, Object.class));
                    
                    // 设置请求ID
                    result.setRequestNo(requestId);
                    
                    // 对于Void类型，将data设置为null
                    result.setData(null);
                    
                    return result;
                } catch (Exception e) {
                    log.error("无法解析标准WebHookResult格式（Void类型）: {}", e.getMessage());
                    return new WebHookResult<>(requestId, KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode(), null);
                }
            }

            // 尝试解析为标准WebHookResult格式
            try {
                WebHookResult<T> result = objectMapper.readValue(responseBody, 
                        objectMapper.getTypeFactory().constructParametricType(WebHookResult.class, responseType));
                
                // 设置请求ID
                if (StringUtils.isBlank(result.getRequestNo())) {
                    result.setRequestNo(requestId);
                }

                return result;
            } catch (Exception e) {
                log.warn("无法解析标准WebHookResult格式: {}", e.getMessage());
                return new WebHookResult<>(requestId, KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode(),null);
            }
            
        } catch (Exception e) {
            log.error("解析响应体失败: {}", responseBody, e);
            return new WebHookResult<>(requestId, KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode(), null);
        }
    }

} 


package com.kun.linkage.auth.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.kun.linkage.auth.ext.mapper.KcAuthFlowMapper;
import com.kun.linkage.auth.facade.vo.boss.KcAuthFlowDetailRequestVO;
import com.kun.linkage.auth.facade.vo.boss.KcAuthFlowDetailVO;
import com.kun.linkage.auth.facade.vo.boss.KcAuthFlowPageVO;
import com.kun.linkage.auth.facade.vo.boss.KcAuthFlowRequestVO;
import com.kun.linkage.auth.utils.BigDecimalConverter;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * title: <br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * @date 2025/7/9 15:59
 */
@Slf4j
@Service
public class KcAuthFlowService {

    @Resource
    private KcAuthFlowMapper kcAuthFlowMapper;

    public Result<PageResult<KcAuthFlowPageVO>> pageList(KcAuthFlowRequestVO requestVO) {
        PageResult<KcAuthFlowPageVO> pageVOPageResult = PageHelperUtil.getPage(requestVO, () -> kcAuthFlowMapper.pageList(requestVO));
        return Result.success(pageVOPageResult);
    }

    public Result<KcAuthFlowDetailVO> detail(KcAuthFlowDetailRequestVO requestVO) {
        KcAuthFlowDetailVO kcAuthFlowDetailVO = kcAuthFlowMapper.detail(requestVO);
        if (ObjectUtil.isNotNull(kcAuthFlowDetailVO)) {
            if (StrUtil.isBlank(kcAuthFlowDetailVO.getConversionRateCardholderBilling())) {
                kcAuthFlowDetailVO.setConversionRateCardholderBilling("0");
            } else {
                kcAuthFlowDetailVO.setConversionRateCardholderBilling(BigDecimalConverter.convert(kcAuthFlowDetailVO.getConversionRateCardholderBilling()).toPlainString());
            }
        }

        return Result.success(kcAuthFlowDetailVO);
    }
}

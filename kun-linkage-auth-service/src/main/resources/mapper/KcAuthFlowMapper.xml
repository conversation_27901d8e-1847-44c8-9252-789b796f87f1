<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kun.linkage.auth.ext.mapper.KcAuthFlowMapper">


    <select id="pageList" resultType="com.kun.linkage.auth.facade.vo.boss.KcAuthFlowPageVO">
        select a.*
        , m.english_name as merchantName
        , transCcy.ccy_code as transCcyCode
        , billCcy.ccy_code as billCcyCode
        from kc_auth_flow a
        left join kc_kyc_main_info m
            on a.member_id = m.member_id
        LEFT JOIN kc_ccy_info transCcy
            on transCcy.visa_code = a.currency_code_transaction
        LEFT JOIN kc_ccy_info billCcy
            on billCcy.visa_code = a.currency_code_cardholder_billing
        <where>
            <if test="authStartTime != null">
                AND DATE_FORMAT(a.auth_start_time, '%Y-%m-%d') &gt;= DATE_FORMAT(#{authStartTime}, '%Y-%m-%d')
                AND a.create_date &gt;= #{authStartTime}
            </if>
            <if test="authEndTime != null">
                AND DATE_FORMAT(a.auth_start_time, '%Y-%m-%d') &lt;= DATE_FORMAT(#{authEndTime}, '%Y-%m-%d')
                AND a.create_date &lt;= #{authEndTime}
            </if>
            <if test="transAmtMin != null">
                AND a.trans_amt &gt;= #{transAmtMin}
            </if>
            <if test="transAmtMax != null">
                AND a.trans_amt &lt;= #{transAmtMax}
            </if>
            <if test="merchantId != null and merchantId != ''">
                AND a.merchant_id = #{merchantId}
            </if>
            <if test="transId != null and transId != ''">
                AND a.trans_id = #{transId}
            </if>
            <if test="authType != null and authType != ''">
                AND a.auth_type = #{authType}
            </if>
            <if test="originalTransId != null and originalTransId != ''">
                AND a.original_trans_id = #{originalTransId}
            </if>
            <if test="merchantType != null and merchantType != ''">
                AND a.merchant_type = #{merchantType}
            </if>
            <if test="referenceNo != null and referenceNo != ''">
                AND a.reference_no = #{referenceNo}
            </if>
            <if test="cardId != null and cardId != ''">
                AND a.card_id = #{cardId}
            </if>
            <if test="authorizationDecision != null">
                AND a.authorization_decision = #{authorizationDecision}
            </if>
            <if test="systemsTraceAuditNumber != null and systemsTraceAuditNumber != ''">
                AND a.systems_trace_audit_number = #{systemsTraceAuditNumber}
            </if>
            <if test="channel != null and channel != ''">
                AND a.channel = #{channel}
            </if>
            <if test="returnCode != null and returnCode != ''">
                AND a.return_code = #{returnCode}
            </if>
            <if test="approveCode != null and approveCode != ''">
                AND a.approve_code = #{approveCode}
            </if>
        </where>
        ORDER BY a.id DESC
    </select>

    <select id="detail" resultType="com.kun.linkage.auth.facade.vo.boss.KcAuthFlowDetailVO">
        select a.*
             ,a.retrieval_reference_number as referenceNo
             , m.english_name as merchantName
             , transCcy.ccy_code as transCcyCode
             , billCcy.ccy_code as billCcyCode
        from kc_auth_flow a
        left join kc_kyc_main_info m
            on a.member_id = m.member_id
        LEFT JOIN kc_ccy_info transCcy
            on transCcy.visa_code = a.currency_code_transaction
        LEFT JOIN kc_ccy_info billCcy
            on billCcy.visa_code = a.currency_code_cardholder_billing
        where a.trans_id = #{transId} and a.create_date = #{createDate}
    </select>
</mapper>
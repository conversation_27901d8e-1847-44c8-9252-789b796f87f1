INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select temp.code,temp.name,temp.id,temp.create_date,temp.update_date
from (select 'boss-sms-template' as code,
             '短信模板'          as name,
             (max(id) + 1)       as id,
             now()               as create_date,
             null                as update_date
      from vcc_permission) temp where 1=1;



INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select temp.code,temp.name,temp.id,temp.create_date,temp.update_date
from (select 'boss-email-template' as code,
             '邮件模板'            as name,
             (max(id) + 1)         as id,
             now()                 as create_date,
             null                  as update_date
      from vcc_permission) temp where 1=1;

INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select temp.code,temp.name,temp.id,temp.create_date,temp.update_date
from (select 'boss-markup-mapping-add' as code,
             'markup模板映射新增'      as name,
             (max(id) + 1)             as id,
             now()                     as create_date,
             null                      as update_date
      from vcc_permission) temp where 1=1;

INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select temp.code,temp.name,temp.id,temp.create_date,temp.update_date
from (select 'boss-markup-mapping-update' as code,
             'markup模板映射修改'         as name,
             (max(id) + 1)                as id,
             now()                        as create_date,
             null                         as update_date
      from vcc_permission) temp where 1=1;

INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select temp.code,temp.name,temp.id,temp.create_date,temp.update_date
from (select 'boss-markup-mapping-review' as code,
             'markup模板映射审核'         as name,
             (max(id) + 1)                as id,
             now()                        as create_date,
             null                         as update_date
      from vcc_permission) temp where 1=1;

INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select temp.code,temp.name,temp.id,temp.create_date,temp.update_date
from (select 'boss-data-dict' as code,
             '数据字典'       as name,
             (max(id) + 1)    as id,
             now()            as create_date,
             null             as update_date
      from vcc_permission) temp where 1=1;

-- 邮件通知模板 --
CREATE TABLE `vcc_email_template`
(
    `template_no`          varchar(255) NOT NULL COMMENT '模板编号',
    `template_name`        varchar(256) DEFAULT NULL COMMENT '模板名称',
    `email_title`          varchar(256) DEFAULT NULL COMMENT ' 邮件标题',
    `template_type`        varchar(64)  DEFAULT NULL COMMENT '模板类型；3DS;交易动账通知',
    `template_language`    varchar(8)   NOT NULL COMMENT '模板类型；3DS;交易动账通知',
    `template_content`     text         DEFAULT NULL COMMENT '模板内容',
    `template_content_key` varchar(256) DEFAULT NULL COMMENT '模板内容key',
    `status`               varchar(32)  DEFAULT NULL COMMENT '模板状态;ACTIVE;INACTIVE',
    `create_user_id`       bigint       DEFAULT NULL COMMENT '创建用户id',
    `update_user_id`       bigint       DEFAULT NULL COMMENT '更新用户id',
    `id`                   bigint       NOT NULL COMMENT 'id',
    `create_date`          datetime     DEFAULT NULL COMMENT '创建时间',
    `update_date`          datetime     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uniq_template_no` (`template_no` asc) USING BTREE
) ENGINE=InnoDB COMMENT='邮件通知模板';

-- 短信通知模板 --
CREATE TABLE `vcc_sms_template`
(
    `template_no`          varchar(255) NOT NULL COMMENT '模板编号',
    `template_name`        varchar(256) DEFAULT NULL COMMENT '模板名称',
    `template_type`        varchar(64)  DEFAULT NULL COMMENT '模板类型；3DS;交易动账通知',
    `template_language`    varchar(8)   NOT NULL COMMENT '模板语言; 默认 ''EN''，支持 ''EN'', ''CN''',
    `template_content`     text         DEFAULT NULL COMMENT '模板内容',
    `template_content_key` varchar(256) DEFAULT NULL COMMENT '模板内容key',
    `template_sms_flag`    int          DEFAULT '0' COMMENT '是否是模板类短信;0:否;1:是',
    `status`               varchar(32)  DEFAULT NULL COMMENT '模板状态;ACTIVE;INACTIVE',
    `create_user_id`       bigint       DEFAULT NULL COMMENT '创建用户id',
    `update_user_id`       bigint       DEFAULT NULL COMMENT '更新用户id',
    `id`                   bigint       NOT NULL COMMENT 'id',
    `create_date`          datetime     DEFAULT NULL COMMENT '创建时间',
    `update_date`          datetime     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uniq_template_no` (`template_no` asc) USING BTREE
) ENGINE=InnoDB  COMMENT='短信通知模板';

-- 持卡人markup映射关系
create table common_ch_markup_mapping
(
    processor         varchar(32)    not null comment 'processor',
    merchant_no       varchar(64)    not null comment '商户号',
    card_bin          varchar(18)    not null comment '卡bin',
    card_product_code varchar(63)    not null comment '卡产品码',
    billing_currency  varchar(4)     not null comment '账单币种',
    billing_amount    decimal(12, 3) not null comment '账单金额',
    markup_rate       decimal(6, 4)  not null default 0 comment '持卡人markup费率',
    status            varchar(8)     not null comment '状态',
    is_deleted        int(2)         not null comment '逻辑删除',
    created_by        varchar(150) null comment '创建人',
    updated_by        varchar(150) null comment '更新人',
    id                bigint(64)     not null primary key comment '主键id',
    create_date       datetime null comment '创建时间',
    update_date       datetime null comment '更新时间'
) ENGINE=InnoDB comment '持卡人markup';

-- 持卡人markup映射关系审核记录表
create table common_ch_markup_mapping_review_log
(
    processor         varchar(32)    not null comment 'processor',
    merchant_no       varchar(64)    not null comment '商户号',
    card_bin          varchar(18)    not null comment '卡bin',
    card_product_code varchar(63)    not null comment '卡产品码',
    billing_currency  varchar(4)     not null comment '账单币种',
    billing_amount    decimal(12, 3) not null comment '账单金额',
    markup_rate       decimal(6, 4)  not null default 0 comment '持卡人markup费率',
    `before`          text null comment '修改前数据',
    after             text null comment '修改后数据',
    operation_type    int(2)         not null comment '操作类型',
    review_status     varchar(8)     not null comment '审核状态',
    reject_reason     text null comment '驳回原因',
    created_by        varchar(150) null comment '创建人',
    updated_by        varchar(150) null comment '更新人',
    reviewer          varchar(150) null comment '审核人',
    id                bigint(64)     not null primary key comment '主键id',
    create_date       datetime null comment '创建时间',
    update_date       datetime null comment '更新时间',
    mapping_id        bigint(64)     null comment 'markup映射表ID'
) ENGINE=InnoDB comment '持卡人markup审核记录表';


-- 数据字典表 ---
CREATE TABLE `vcc_data_dict`
(
    `id`          bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键id',
    `dict_type`        varchar(32)  DEFAULT NULL COMMENT '数据类型代码',
    `dict_type_second` varchar(32)  DEFAULT NULL COMMENT '子类型',
    `dict_value`       varchar(32)  DEFAULT NULL COMMENT '数据值',
    `en_desc`          varchar(255) DEFAULT NULL COMMENT '英文描述',
    `cn_desc`          varchar(255) DEFAULT NULL COMMENT '中文简体描述',
    `valid_flag`       int          DEFAULT NULL COMMENT '1有效 0无效',
    `dict_index`       int          DEFAULT NULL COMMENT '排序字段',
    `create_time`      datetime NULL DEFAULT NULL COMMENT '创建时间',
    `create_user_id`   bigint       DEFAULT NULL COMMENT '创建用户id',
    `update_time`      datetime NULL DEFAULT NULL COMMENT '修改时间',
    `update_user_id`   bigint       DEFAULT NULL COMMENT '更新用户id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB  COMMENT='数据字典表';


INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`,
                             `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('SMS_TEMPLATE_TYPE', NULL, '3DSV', '3DS 验证', '3-D Secure Verification', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`,
                             `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('SMS_TEMPLATE_TYPE', NULL, 'TAN', '交易动账通知', 'Transaction Account Movement Notification', 1, 2, now(),
        NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`,
                             `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('SMS_TEMPLATE_TYPE', NULL, 'CSCN-A', '卡状态变更通知-激活', 'Card Status Change Notification - Activation', 1,
        3, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`,
                             `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('SMS_TEMPLATE_TYPE', NULL, 'CSCN-F', '卡状态变更通知-冻结', 'Card Status Change Notification - Freezing', 1, 4,
        now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`,
                             `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('SMS_TEMPLATE_TYPE', NULL, 'CSCN-U', '卡状态变更通知-解冻', 'Card Status Change Notification - Unfreezing', 1,
        5, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`,
                             `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('SMS_TEMPLATE_TYPE', NULL, 'CSCN-C', '卡状态变更通知-注销', 'Card Status Change Notification - Cancellation', 1,
        6, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`,
                             `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('SMS_TEMPLATE_TYPE', NULL, 'ABN', '账户余额通知', 'Account Balance Notification', 1, 7, now(), NULL, now(),
        NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`,
                             `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('EMAIL_TEMPLATE_TYPE', NULL, '3DSV', '3DS 验证', '3-D Secure Verification', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`,
                             `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('EMAIL_TEMPLATE_TYPE', NULL, 'TAN', '交易动账通知', 'Transaction Account Movement Notification', 1, 2, now(),
        NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`,
                             `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('EMAIL_TEMPLATE_TYPE', NULL, 'CSCN-A', '卡状态变更通知-激活', 'Card Status Change Notification - Activation', 1,
        3, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`,
                             `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('EMAIL_TEMPLATE_TYPE', NULL, 'CSCN-F', '卡状态变更通知-冻结', 'Card Status Change Notification - Freezing', 1,
        4, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`,
                             `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('EMAIL_TEMPLATE_TYPE', NULL, 'CSCN-U', '卡状态变更通知-解冻', 'Card Status Change Notification - Unfreezing', 1,
        5, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`,
                             `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('EMAIL_TEMPLATE_TYPE', NULL, 'CSCN-C', '卡状态变更通知-注销', 'Card Status Change Notification - Cancellation',
        1, 6, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `en_desc`, `cn_desc`, `valid_flag`,
                             `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('EMAIL_TEMPLATE_TYPE', NULL, 'ABN', '账户余额通知', 'Account Balance Notification', 1, 7, now(), NULL, now(),
        NULL);


CREATE TABLE `vcc_agreement_signing_record`
(
    id                BIGINT AUTO_INCREMENT COMMENT '自增主键' PRIMARY KEY,
    user_id           BIGINT NOT NULL COMMENT '用户ID',
    agreements_type   VARCHAR(32) NULL COMMENT '协议类型',
    agreement_version VARCHAR(10) NULL COMMENT '隐私协议版本',
    signing_time      DATETIME NULL COMMENT '签约时间',
    status        varchar(12) null comment '状态',
    create_date       DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_date       DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间'
) ENGINE=InnoDB COMMENT '隐私协议版本签署记录表';

-- 渠道对账文件表
create table `vcc_channel_tran_file`( 
    `upload_user_id` bigint(64) NOT NULL COMMENT '文件上传人' , 
    `file_type` int(10) NOT NULL COMMENT '人间类型' , 
    `status` int(10) NOT NULL COMMENT '状态' , 
    `file_name` varchar(255) NOT NULL COMMENT '文件名' , 
    `file_path` varchar(255) NOT NULL COMMENT '文件路径' , 
    `upload_date` datetime NOT NULL COMMENT '更新时间' , 
    `sum_count` int(50) NOT NULL COMMENT '总数' , 
    `success_count` int(50) NOT NULL COMMENT '成功数' , 
    `error_msg` text NULL COMMENT '失败数' , 
    `id` bigint(64) NOT NULL COMMENT '主键id' PRIMARY KEY,
    `create_date` datetime NULL COMMENT '创建时间' , 
    `update_date` datetime NULL COMMENT '更新时间'
) ENGINE = InnoDB COMMENT '渠道交易对账文件表';

-- 业务归属
alter table `vcc_wallet_detail` 
    add `request_no` varchar(50) NOT NULL comment '请求号' FIRST,
    add `original_order_id` varchar(50) NULL comment '原始订单号' after `order_id`,
    add `settle_date` datetime NULL comment '结算时间' after `update_date`;

-- 业务归属和修改密码标识位
alter table `vcc_user` 
    add `business_attribution` int(11) NULL comment '业务归属',
    add `is_change_password` int(1) NULL comment '是否强制修改密码';
-- 业务归属
alter table `vcc_kyc_cert_info` add `business_attribution` int(11) NULL comment '业务归属';

-- 补充商户表和商户认证信息表业务归属字段
UPDATE vcc_kyc_cert_info
    SET business_attribution = 1
    WHERE business_attribution IS NULL;

UPDATE vcc_user
    SET business_attribution = 1
    WHERE business_attribution IS NULL;

-- 产品编号
alter table `vcc_card` add `code` varchar(10) NULL comment '产品编号';
-- 版本号，乐观锁
alter table `vcc_user_card` add `version` bigint(50) NOT NULL comment '版本号';
-- 授权ID
alter table `vcc_open_card_task` add `license_id` bigint(64) NULL comment '授权ID';


CREATE TABLE kcard.`vcc_wallet_snapshot` (
  `ref_id` bigint NOT NULL COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `customer_id` bigint NOT NULL COMMENT '用户ID',
  `wallet_id` varchar(50) DEFAULT NULL COMMENT '钱包ID',
  `is_share` int(1) NOT NULL COMMENT '是否共享钱包',
  `status` int NOT NULL COMMENT '钱包状态',
  `type` int NOT NULL COMMENT '钱包类型',
  `source` int NOT NULL COMMENT '钱包来源',
  `currency` varchar(10) NOT NULL COMMENT '币种',
  `usable_quota` decimal(50,2) NOT NULL COMMENT '可用额度',
  `overflow_quota` decimal(50,2) NOT NULL COMMENT '溢出额度',
  `version` bigint NOT NULL DEFAULT 1 COMMENT '版本号',
  `id` bigint NOT NULL COMMENT '主键id',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='用户钱包快照表';


CREATE TABLE `vcc_user_card_snapshot` (
  `card_number` varchar(100) NOT NULL COMMENT '卡号',
  `card_number_last_four` varchar(50) NOT NULL COMMENT '卡号后四位',
  `cvv` varchar(50) NULL COMMENT 'cvv（已废弃）',
  `expiry` varchar(50) NULL COMMENT '有效期（已废弃）',
  `request_date` datetime NOT NULL COMMENT '请求时间',
  `order_id` varchar(50) NOT NULL COMMENT '订单号',
  `card_bin_id` bigint NOT NULL COMMENT '卡bin id',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `customer_id` bigint NOT NULL COMMENT '商户id',
  `channel_card_id` varchar(100) NOT NULL COMMENT '渠道卡id',
  `channel` int NOT NULL COMMENT '通道',
  `currency` varchar(10) NOT NULL COMMENT '币种',
  `card_type` varchar(50) NOT NULL COMMENT '卡类型',
  `usable_quota` decimal(50,2) NOT NULL COMMENT '可用额度',
  `usable_limit` decimal(50,2) NOT NULL COMMENT '可用额度',
  `remark` varchar(255) NOT NULL COMMENT '备注',
  `card_status` varchar(50) NOT NULL COMMENT '卡状态',
  `cardholder_id` bigint DEFAULT NULL COMMENT '持卡人id',
  `is_share` int(1) NOT NULL COMMENT '是否共享',
  `limit_config` varchar(255) DEFAULT NULL COMMENT '限制配置',
  `license_id` bigint DEFAULT NULL COMMENT '授权id',
  `apply_fee` decimal(10,2) NOT NULL COMMENT '开卡手续费',
  `open_card_task_id` bigint DEFAULT NULL COMMENT '开卡任务id',
  `version` bigint NOT NULL COMMENT '版本号',
  `id` bigint NOT NULL COMMENT '主键id',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='卡快照表';


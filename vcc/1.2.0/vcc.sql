-- 授权交易表增加记录字段

alter table kcard.`vcc_auth_tran` 
  modify `card_id` bigint(64) NULL comment '卡id',
  modify `card_quota` decimal(50,2) NULL comment '卡余额(已废弃)',

  add `channel_card_id` varchar(255) NULL comment '渠道卡id' after `card_id`,
  add `billing_currency_scale` int(5) NULL comment '账单币种精度' after `billing_currency`,
  add `merchant_currency_scale` int(5) NULL comment '交易币种类型' after `merchant_currency`,
  add `channel_auth_type` varchar(50) NULL comment '渠道推过来的授权交易类型' after `auth_type`,
  add `channel_status` varchar(50) NULL comment '渠道推过来的授权交易状态' after `status`,
  add `remaining_auth_amount` decimal(50,2) NULL comment '授权金额，含撤销金额' after `channel_status`,
  add `clear_amount` decimal(50,2) NULL comment '清算金额' after `remaining_auth_amount`,
  add `card_opening_amount` decimal(50,2) NULL comment '期初金额' after `card_quota`,
  add `card_closing_amount` decimal(50,2) NULL comment '期末金额' after `card_quota`,
  add `direction` varchar(10) NULL comment '交易方向' after `card_closing_amount`,
  add `approve_code` varchar(50) NULL comment 'approveCode' after `direction`,
  add `card_acceptor_id` varchar(255) NULL comment '收单商户id' after `approve_code`,
  add `card_acceptor_country` varchar(50) NULL comment '收单商户国家码' after `card_acceptor_id`,
  add `acquire_reference_no` varchar(255) NULL comment '参考号' after `card_acceptor_country`,
  add `retrieval_reference_number` varchar(255) NULL comment '商户订单号' after `acquire_reference_no`
  add `version` int(50) NULL comment '版本号' after `retrieval_reference_number`,
;

alter table kcard.`vcc_clear_tran` 
  modify `type` varchar(50) NULL comment '交易类型(已废弃)',
  modify `card_id` bigint(64) NULL comment '卡id',
  modify `transaction_id` varchar(50) NULL comment '交易id(已废弃)',

  add `channel_card_id` varchar(255) NULL comment '渠道卡id' after `card_id`,
  
  add `clear_id` varchar(255) NULL comment '清算订单的id' after `transaction_id`,
  add `original_clear_id` varchar(255) NULL comment '原清算订单的id' after `clear_id`,
  add `tran_id` varchar(255) NULL comment '原始交易id' after `original_clear_id`,
  add `original_tran_id` varchar(255) NULL comment '原始交易id' after `tran_id`,
  add `clear_amount` decimal(50,2) NULL comment '本次清算的金额' after `original_tran_id`,
  add `card_opening_amount` decimal(50,2) NULL comment '期初金额' after `clear_amount`,
  add `card_closing_amount` decimal(50,2) NULL comment '期末金额' after `card_opening_amount`,
  add `direction` varchar(10) NULL comment '交易方向' after `card_closing_amount`,
  add `approve_code` varchar(50) NULL comment 'approveCode' after `direction`,
  add `clear_status` varchar(50) NULL comment '清算状态' after `approve_code`,
  add `clear_type` varchar(50) NULL comment '清算类型' after `clear_status`,
  add `channel_clear_type` varchar(50) NULL comment '渠道推过来的清算交易类型' after `clear_type`,
  add `auth_type` varchar(50) NULL comment '授权交易类型' after `channel_clear_type`,
  add `channel_auth_type` varchar(50) NULL comment '渠道推过来的授权交易类型' after `auth_type`,
  add `fail_code` varchar(50) NULL comment '异常错误码' after `channel_auth_type`,

  add `billing_currency_scale` int(5) NULL comment '账单币种精度' after `billing_currency`,
  add `merchant_currency_scale` int(5) NULL comment '交易币种类型' after `merchant_currency`,
  add `card_acceptor_id` varchar(255) NULL comment '收单商户id' after `merchant_currency_scale`,
  add `card_acceptor_country` varchar(50) NULL comment '收单商户国家码' after `tran_id`,
  add `acquire_reference_no` varchar(255) NULL comment '参考号' after `card_acceptor_id`,
  add `arn` varchar(255) NULL comment '参考号' after `acquire_reference_no`,
  add `mcc` varchar(255) NULL comment 'mcc' after `arn`,
  add `original_info` text NULL comment '原始交易信息' after `remark`
;

alter table kcard.`vcc_card_tran_log`
  add `opening_amount` decimal(50,2) NULL comment '期初金额' after `card_balance`,
  add `closing_amount` decimal(50,2) NULL comment '期末金额' after `opening_amount`,
  add `direction` varchar(20) NULL comment '交易方向' after `closing_amount`
;


CREATE TABLE kcard.`vcc_auth_release` (
  `tran_id` varchar(255) DEFAULT NULL COMMENT '授权交易ID',
  `card_id` bigint(64) DEFAULT NULL COMMENT '授权交易卡ID',
  `channel_card_id` varchar(255) DEFAULT NULL COMMENT '渠道授权交易卡ID',
  `card_number` varchar(255) DEFAULT NULL COMMENT '授权交易卡号',
  `user_id` bigint(50) DEFAULT NULL COMMENT '授权交易用户ID',
  `release_date` datetime DEFAULT NULL COMMENT '释放时间',
  `release_amount` decimal(10,2) DEFAULT NULL COMMENT '实际释放金额',
  `request_amount` decimal(10,2) DEFAULT NULL COMMENT '请求释放金额',
  `release_currency` varchar(10) DEFAULT NULL COMMENT '释放币种',
  `auth_type` varchar(50) DEFAULT NULL COMMENT '授权类型',
  `channel_auth_type` varchar(255) DEFAULT NULL COMMENT '渠道授权类型',
  `status` int(20) DEFAULT NULL COMMENT '释放状态',
  `original_info` text COMMENT '原始交易信息',
  `id` bigint(64) NOT NULL COMMENT '主键id',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_vcc_auth_tran_id` (`tran_id`),
  KEY `idx_vcc_auth_tran_card_number` (`card_number`),
  KEY `idx_vcc_auth_tran_card_id` (`card_id`),
  KEY `idx_vcc_auth_tran_channel_card_id` (`channel_card_id`),
  KEY `idx_vcc_auth_tran_user_id` (`user_id`)
) ENGINE=InnoDB COMMENT='VCC授权交易释放记录表';


CREATE TABLE kcard.`vcc_auth_tran_log` (
  `request_no` varchar(255) DEFAULT NULL COMMENT '请求号',
  `tran_id` varchar(255) DEFAULT NULL COMMENT '授权交易ID',
  `channel_card_id` varchar(255) DEFAULT NULL COMMENT '授权交易卡号',
  `channel_auth_type` varchar(255) DEFAULT NULL COMMENT '授权交易类型',
  `request_param` text COMMENT '请求信息',
  `response_code` varchar(255) DEFAULT NULL COMMENT '响应码',
  `response_param` text COMMENT '响应信息',
  `id` bigint(64) NOT NULL COMMENT '主键id',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='VCC授权交易记录表';

CREATE TABLE `vcc_clear_tran_log` (
  `request_no` varchar(255) DEFAULT NULL COMMENT '请求号',
  `clear_id` varchar(255) DEFAULT NULL COMMENT '清算ID',
  `tran_id` varchar(255) DEFAULT NULL COMMENT '授权ID',
  `channel_card_id` varchar(255) DEFAULT NULL COMMENT '卡号',
  `channel_clear_type` varchar(255) DEFAULT NULL COMMENT '清算类型',
  `request_param` text COMMENT '请求信息',
  `response_code` varchar(255) DEFAULT NULL COMMENT '响应码',
  `response_param` text COMMENT '响应信息',
  `id` bigint NOT NULL COMMENT '主键id',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='VCC清算记录表';


CREATE TABLE kcard.`vcc_auth_release_log` (
  `request_no` varchar(255) DEFAULT NULL COMMENT '请求号',
  `tran_id` varchar(255) DEFAULT NULL COMMENT '清算ID',
  `channel_card_id` varchar(255) DEFAULT NULL COMMENT '卡号',
  `channel_auth_type` varchar(255) DEFAULT NULL COMMENT '授权类型',
  `release_amount` decimal(10,2) DEFAULT NULL COMMENT '释放金额',
  `release_currency` varchar(10) DEFAULT NULL COMMENT '释放币种',
  `request_param` text COMMENT '请求信息',
  `response_code` varchar(255) DEFAULT NULL COMMENT '响应码',
  `response_param` text COMMENT '响应信息',
  `id` bigint(64) NOT NULL COMMENT '主键id',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='VCC授权释放记录表';


-- 撤销状态变更
update `kcard`.`vcc_auth_tran` set `status` = 'approved' where `auth_type` = 'Reversal' and `status` = 'void';

-- 交易记录卡id更新
update `kcard`.`vcc_auth_tran` set `channel_card_id` = 'K-fec68cfaa3bb46b79ad6bdd6f20805c7', `card_id` = '1925853712114720800' where `card_number` = 'usfgq7yGGdwe6/f5lyHR/xxFo7a+VktS+3nTFcqRxCM='; 
update `kcard`.`vcc_auth_tran` set `channel_card_id` = 'K-5b8884c4f6044646a962f4b5f5fcd359', `card_id` = '1930511907920945200' where `card_number` = 'PD1jBgbG+EO1pGT8GPEhCkvR8Tp0q4F1Gv43cvvbC40='; 
update `kcard`.`vcc_auth_tran` set `channel_card_id` = 'K-6ec7126d0d264990ad05e94cce9a26b9', `card_id` = '1931962717908918300' where `card_number` = 'wH29xyDewsob9SdxALhqTbIXtqTqMrDHIGGCdVCsJmQ='; 
update `kcard`.`vcc_auth_tran` set `channel_card_id` = 'K-c39d7e4700a34b8aabd46f399907416b', `card_id` = '1931963471109447700' where `card_number` = 'EWnm3dZyC2YLLyWEWGa9n7QswZIhIvzvAqfjpavxyqc='; 
update `kcard`.`vcc_auth_tran` set `channel_card_id` = 'K-ec6dcdad3e8c408aa578c4adca6a0b8f', `card_id` = '1932047777894305800' where `card_number` = 'DraA7S4YsSou6MLWYAGazx5riAI474SiNtRxMY7MCSU='; 
update `kcard`.`vcc_auth_tran` set `channel_card_id` = 'K-3d037c1615b04d4091c5d1d5c6ab27d1', `card_id` = '1932381980315967500' where `card_number` = 'f3/oburV3huDTPEg4FenRRxsiN6KwPpcdh4zyKF/AC0='; 
update `kcard`.`vcc_auth_tran` set `channel_card_id` = 'K-064a347a20884bf1b9442e7dadd375ef', `card_id` = '1932395318034915300' where `card_number` = 'SJ0i0uQ9jVJRVLDu9N7U8rtSvcai7FtTE+g22McA3SI='; 
update `kcard`.`vcc_auth_tran` set `channel_card_id` = 'K-a81bba360fd7481fb6bc8b84f5153b67', `card_id` = '1932401104601571300' where `card_number` = 'FpxIYxZTrRfqfGrT5GAG8Dd/G57H+OpPIj8BxIznjyI='; 
update `kcard`.`vcc_auth_tran` set `channel_card_id` = 'K-4c530966795f4352abb954606a0935a0', `card_id` = '1932675160444084200' where `card_number` = 'N4RsqxSrN73zszEg4gaFVUo5t4181yZG07BE39j1Ay4='; 
update `kcard`.`vcc_auth_tran` set `channel_card_id` = 'K-bae8117f26934691a8cc7f9906c71887', `card_id` = '1932675413679382500' where `card_number` = 'ivrAQmviIY5m3hklOtQdgRypiStIP8pKRfGCOdrrycg=';

-- 交易期末金额同步
update `kcard`.`vcc_auth_tran` set `card_closing_amount` = `card_quota` where create_date <= '2025-07-16 18:56:00';
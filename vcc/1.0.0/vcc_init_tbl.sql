CREATE TABLE `vcc_auth_tran` (
  `tran_id` varchar(100) NOT NULL COMMENT '交易id',
  `original_tran_id` varchar(100) DEFAULT NULL COMMENT '原始交易id',
  `card_number` varchar(100) NOT NULL COMMENT '卡号',
  `card_id` bigint(50) NOT NULL COMMENT '卡id',
  `user_id` bigint(50) NOT NULL COMMENT '用户id',
  `merchant_name` varchar(100) NOT NULL COMMENT '平台商户名',
  `tran_date` datetime NOT NULL COMMENT '交易时间',
  `billing_amount` decimal(10,2) NOT NULL COMMENT '账单金额',
  `billing_currency` varchar(10) NOT NULL COMMENT '账单币种',
  `merchant_amount` decimal(10,2) NOT NULL COMMENT '交易金额',
  `merchant_currency` varchar(10) NOT NULL COMMENT '交易币种',
  `auth_type` varchar(50) NOT NULL COMMENT '授权交易类型',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `status` varchar(10) NOT NULL COMMENT '交易状态',
  `mcc` varchar(10) DEFAULT NULL COMMENT 'mcc',
  `rate_fee` decimal(10,2) DEFAULT NULL COMMENT '渠道费率',
  `rate_fee_currency` varchar(10) DEFAULT NULL COMMENT '渠道费率币种',
  `fee` decimal(10,2) DEFAULT NULL COMMENT '费率',
  `fee_detail` text COMMENT '费率详情',
  `sync_source` int(10) NOT NULL COMMENT '数据来源，webhook或task',
  `fail_code` varchar(20) DEFAULT NULL COMMENT '失败错误码',
  `fail_reason` varchar(50) DEFAULT NULL COMMENT '失败错误原因',
  `card_quota` decimal(10,2) DEFAULT NULL COMMENT '当前卡额度',
  `original_info` text COMMENT '原始交易数据',
  `id` bigint(64) NOT NULL COMMENT 'id',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_vcc_auth_tran_id` (`tran_id`),
  KEY `idx_vcc_original_tran_id` (`original_tran_id`),
  KEY `idx_vcc_auth_tran_card_number` (`card_number`),
  KEY `idx_vcc_auth_tran_card_id` (`card_id`),
  KEY `idx_vcc_auth_tran_user_id` (`user_id`)
) ENGINE=InnoDB  COMMENT='授权交易表';



CREATE TABLE `vcc_boss_operation_log` (
  `operator_id` bigint(50) NOT NULL COMMENT '系统用户id',
  `object` int(5) NOT NULL COMMENT '操作对象',
  `object_id` bigint(64) DEFAULT NULL COMMENT '操作对象id',
  `type` int(5) NOT NULL COMMENT '操作类型',
  `subject` varchar(50) DEFAULT NULL COMMENT '操作子类',
  `data_before` varchar(200) DEFAULT NULL COMMENT '修改前',
  `data_after` varchar(200) DEFAULT NULL COMMENT '修改后',
  `remark` varchar(100) DEFAULT NULL COMMENT '备注',
  `id` bigint(64) NOT NULL COMMENT 'id',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '系统操作记录表' ;



CREATE TABLE `vcc_boss_role` (
  `role_name` varchar(50) NOT NULL COMMENT '角色名',
  `role_code` varchar(50) NOT NULL COMMENT '角色代号',
  `status` int(50) NOT NULL COMMENT '状态',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_vcc_boss_role_role_name` (`role_name`),
  UNIQUE KEY `uniq_vcc_boss_role_role_code` (`role_code`)
) ENGINE=InnoDB  COMMENT '角色表' ;



CREATE TABLE `vcc_boss_role_permission` (
  `boss_role_id` bigint(50) NOT NULL COMMENT '角色id',
  `permission_id` bigint(50) NOT NULL COMMENT '权限id',
  `status` int(5) NOT NULL COMMENT '状态',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '角色权限映射表' ;



CREATE TABLE `vcc_boss_user` (
  `username` varchar(150) NOT NULL COMMENT '用户名',
  `account` varchar(50) NOT NULL COMMENT '账号',
  `password` varchar(150) NOT NULL COMMENT '密码',
  `status` int(5) NOT NULL COMMENT '状态',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_vcc_boss_user_account` (`account`)
) ENGINE=InnoDB  COMMENT '系统用户表' ;



CREATE TABLE `vcc_boss_user_boss_role` (
  `user_id` bigint(50) NOT NULL COMMENT '用户id',
  `boss_role_id` bigint(50) NOT NULL COMMENT '系统用户id',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_vcc_boss_user_boss_role_boss_user_id` (`user_id`),
  KEY `idx_vcc_boss_user_boss_role_boss_role_id` (`boss_role_id`)
) ENGINE=InnoDB  COMMENT '系统用户权限角色映射表' ;



CREATE TABLE `vcc_card` (
  `section_no` varchar(10) NOT NULL COMMENT 'Bin段',
  `currency` varchar(10) NOT NULL COMMENT '币种',
  `remark` text COMMENT '备注',
  `description` text COMMENT '描述',
  `card_label` varchar(255) NOT NULL COMMENT '卡品名',
  `channel` int(11) NOT NULL COMMENT '渠道',
  `channel_id` varchar(50) DEFAULT NULL COMMENT '渠道id',
  `region` varchar(20) DEFAULT NULL COMMENT '发卡地',
  `status` int(5) NOT NULL COMMENT '状态',
  `tag` varchar(20) DEFAULT NULL COMMENT '标签',
  `card_type` int(11) NOT NULL DEFAULT '0' COMMENT '卡类型',
  `reserve_count` int(11) DEFAULT NULL COMMENT '储备数量',
  `reserve_config` text COMMENT '储备配置',
  `min_open_card_amount` decimal(50,2) DEFAULT NULL COMMENT '最小开卡充值金额',
  `max_open_card_count` int(11) DEFAULT NULL COMMENT '最大可批量开卡数',
  `min_recharge_amount` decimal(50,2) DEFAULT NULL COMMENT '最小充值金额',
  `min_retention_amount` decimal(50,2) DEFAULT NULL COMMENT '最小销卡保留金额',
  `channel_fee_json` text COMMENT '渠道费率',
  `operation_config` text COMMENT '操作配置',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '卡Bin段表' ;



CREATE TABLE `vcc_card_risk_open_rule` (
  `slot_count` int(9) NOT NULL COMMENT '卡槽数量',
  `batch_open_count` int(9) NOT NULL COMMENT '批量开卡数',
  `operation_user_id` bigint(50) NOT NULL COMMENT '系统用户id',
  `operation_date` datetime NOT NULL COMMENT '操作时间',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '开卡次数配置表' ;



CREATE TABLE `vcc_card_risk_out_rule` (
  `day_out_count` int(9) NOT NULL COMMENT '日转出次数',
  `total_out_count` int(9) NOT NULL COMMENT '总转出次数',
  `operation_user_id` bigint(50) NOT NULL COMMENT '系统用户id',
  `operation_date` datetime NOT NULL COMMENT '操作时间',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '卡片转出规则表' ;



CREATE TABLE `vcc_card_risk_user_open_rule` (
  `user_id` bigint(50) NOT NULL COMMENT '用户id',
  `slot_count` int(9) NOT NULL COMMENT '卡槽数',
  `batch_open_count` int(9) DEFAULT NULL COMMENT '可批量开卡数',
  `operation_user_id` bigint(50) NOT NULL COMMENT '系统用户id',
  `operation_date` datetime DEFAULT NULL COMMENT '系统用户操作时间',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '用户开卡配置表' ;


CREATE TABLE `vcc_card_risk_user_out_rule` (
  `user_id` bigint(50) NOT NULL COMMENT '用户id',
  `day_out_count` int(9) NOT NULL COMMENT '日转出次数',
  `total_out_count` int(9) NOT NULL COMMENT '总转出次数',
  `operation_user_id` bigint(50) NOT NULL COMMENT '系统用户id',
  `operation_date` datetime NOT NULL COMMENT '操作时间',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '用户卡转出配置表' ;


CREATE TABLE `vcc_card_tran_log` (
  `order_id` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` bigint(50) NOT NULL COMMENT '用户id',
  `card_id` bigint(50) NOT NULL COMMENT '卡id',
  `card_number` varchar(100) DEFAULT NULL COMMENT '加密卡号',
  `use_flag` int(50) NOT NULL COMMENT '分类',
  `status` int(50) NOT NULL COMMENT '状态',
  `amount` decimal(50,2) DEFAULT NULL COMMENT '金额',
  `card_balance` decimal(50,2) DEFAULT NULL COMMENT '卡余额',
  `currency` varchar(10) DEFAULT NULL COMMENT '币种',
  `request_date` datetime NOT NULL COMMENT '请求时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_vcc_card_tran_order_id` (`order_id`),
  KEY `idx_vcc_card_tran_card_number` (`card_number`)
) ENGINE=InnoDB  COMMENT '卡片操作和动账记录表' ;


CREATE TABLE `vcc_cardholder` (
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `user_id` bigint(32) NOT NULL COMMENT '所属用户id',
  `first_name` varchar(64) NOT NULL COMMENT '姓',
  `last_name` varchar(64) NOT NULL COMMENT '名',
  `country` varchar(64) NOT NULL COMMENT '国籍',
  `province` varchar(64) NOT NULL COMMENT '省份',
  `city` varchar(64) NOT NULL COMMENT '城市',
  `area` varchar(64) NOT NULL COMMENT '地区',
  `post_code` varchar(64) NOT NULL COMMENT '邮政编码',
  `address` text COMMENT '地址',
  `phone` varchar(50) NOT NULL COMMENT '手机号',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `remark` text COMMENT '备注',
  `status` int(10) NOT NULL COMMENT '状态',
  `birthday` datetime DEFAULT NULL COMMENT '生日',
  `mobile_prefix` varchar(10) DEFAULT NULL COMMENT '区号',
  `channel_cardholder_id` varchar(64) DEFAULT NULL COMMENT '渠道方对应的持卡人id',
  PRIMARY KEY (`id`),
  KEY `idx_vcc_cardholder_user_id` (`user_id`),
  KEY `idx_vcc_cardholder_phone` (`phone`)
) ENGINE=InnoDB  COMMENT '持卡人表' ;


CREATE TABLE `vcc_clear_tran` (
  `transaction_id` varchar(64) NOT NULL COMMENT '交易id',
  `user_id` bigint(50) NOT NULL COMMENT '用户id',
  `card_number` varchar(100) NOT NULL COMMENT '卡号',
  `card_id` bigint(50) NOT NULL COMMENT '卡id',
  `merchant_name` varchar(100) NOT NULL COMMENT '平台名',
  `tran_date` datetime NOT NULL COMMENT '交易时间',
  `posting_date` datetime NOT NULL COMMENT '结算推送的时间',
  `billing_amount` decimal(10,2) NOT NULL COMMENT '账单金额',
  `billing_currency` varchar(30) NOT NULL COMMENT '账单币种',
  `merchant_amount` decimal(10,2) DEFAULT NULL COMMENT '交易金额',
  `merchant_currency` varchar(10) DEFAULT NULL COMMENT '交易币种',
  `type` varchar(30) NOT NULL COMMENT '交易类型',
  `remark` varchar(100) DEFAULT NULL COMMENT '备注',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_vcc_clear_tran_user_id` (`user_id`),
  KEY `idx_vcc_clear_tran_card_id` (`card_id`)
) ENGINE=InnoDB  COMMENT '清算表' ;


CREATE TABLE `vcc_comptroller` (
  `user_id` bigint(50) NOT NULL COMMENT '用户id',
  `use_flag` int(50) NOT NULL COMMENT '分类',
  `status` int(50) NOT NULL COMMENT '状态',
  `request_ip` varchar(50) NOT NULL COMMENT '请求IP',
  `operation_user_id` bigint(50) NOT NULL COMMENT '系统用户id',
  `request_date` datetime NOT NULL COMMENT '请求日期',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_vcc_comptroller_user_id` (`user_id`)
) ENGINE=InnoDB  COMMENT '用户操作锚点表' ;



CREATE TABLE `vcc_kyc_cert_info` (
  `user_id` bigint(32) NOT NULL COMMENT '用户id',
  `customer_id` bigint(20) DEFAULT NULL COMMENT '商户号',
  `status` int(11) NOT NULL COMMENT '状态',
  `sync_status` int(11) NOT NULL COMMENT '下游同步状态',
  `kyc_info` text COMMENT 'kyc信息',
  `audit_date` datetime DEFAULT NULL COMMENT '审核时间',
  `request_date` datetime DEFAULT NULL COMMENT '创建时间',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_vcc_kyc_cert_info_user_id` (`user_id`)
) ENGINE=InnoDB  COMMENT '入网资料表' ;


CREATE TABLE `vcc_notice` (
  `author_id` bigint(32) NOT NULL COMMENT '作者',
  `type` int(5) NOT NULL COMMENT '类型',
  `title` varchar(250) NOT NULL COMMENT '标题',
  `content` text COMMENT '内容',
  `publish_date` datetime DEFAULT NULL COMMENT '发布时间',
  `is_published` int(1) NOT NULL COMMENT '是否法币',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '消息表' ;



CREATE TABLE `vcc_open_api_license` (
  `user_id` bigint(50) NOT NULL COMMENT '用户id',
  `customer_id` bigint(50) NOT NULL COMMENT '商户号',
  `expire_date` datetime DEFAULT NULL COMMENT '过期时间',
  `aes_key` varchar(250) DEFAULT NULL COMMENT 'aeskey',
  `remark` varchar(250) DEFAULT NULL COMMENT '备注',
  `open_card_day_limit` int(5) DEFAULT NULL COMMENT '日限',
  `open_card_week_limit` int(5) DEFAULT NULL COMMENT '周限',
  `open_card_month_limit` int(5) DEFAULT NULL COMMENT '月限',
  `webhook_switch` int(1) DEFAULT NULL COMMENT 'webhook开关',
  `webhook_url` varchar(250) DEFAULT NULL COMMENT 'webhook地址',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT 'license配置表' ;



CREATE TABLE `vcc_open_card_task` (
  `trigger_type` int(3) NOT NULL DEFAULT '2' COMMENT '触发类型',
  `channel_order_id` varchar(100) DEFAULT NULL COMMENT '渠道订单id',
  `user_id` bigint(64) NOT NULL COMMENT '用户id',
  `customer_id` bigint(64) NOT NULL COMMENT '商户号',
  `card_id` bigint(64) NOT NULL COMMENT '卡id',
  `cardholder_id` bigint(64) DEFAULT NULL COMMENT '持卡人id',
  `wallet_id` bigint(64) DEFAULT NULL COMMENT '钱包id',
  `channel` int(5) NOT NULL COMMENT '渠道',
  `channel_request_param` text COMMENT '开卡参数',
  `count` int(5) NOT NULL COMMENT '开卡数',
  `amount` decimal(50,2) NOT NULL COMMENT '开卡首充金额',
  `fee_detail` text COMMENT '手续费明细',
  `limit_config` text COMMENT '共享卡限额',
  `task_status` int(5) NOT NULL COMMENT '任务状态',
  `remark` varchar(100) DEFAULT NULL COMMENT '备注',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '开卡任务表' ;



CREATE TABLE `vcc_permission` (
  `code` varchar(50) NOT NULL COMMENT '代码',
  `name` varchar(50) NOT NULL COMMENT '权限名',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '权限表' ;



CREATE TABLE `vcc_rate_template` (
  `name` varchar(50) NOT NULL COMMENT '费率名',
  `operation_user_id` bigint(50) NOT NULL COMMENT '系统用户id',
  `recharge_fee` varchar(2500) NOT NULL COMMENT '充值费',
  `withdraw_fee` varchar(2500) NOT NULL COMMENT '提现费',
  `hkd_fx_rate` varchar(2500) NOT NULL COMMENT 'hkd加成',
  `card_recharge_fee` varchar(2500) DEFAULT NULL COMMENT '卡充值费',
  `digital_conversion_fee` varchar(2500) NOT NULL COMMENT '数币承兑费',
  `digital_withdraw_fee` varchar(2500) NOT NULL COMMENT '数币提现费',
  `segments` text COMMENT 'Bin段费率',
  `is_default` int(1) NOT NULL COMMENT '是否默认',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '费率模板表' ;



CREATE TABLE `vcc_referral_code` (
  `code` varchar(20) NOT NULL COMMENT '推荐码',
  `user_id` bigint(50) DEFAULT NULL COMMENT '所属用户id',
  `status` int(50) NOT NULL COMMENT '状态',
  `income_expire` int(50) NOT NULL COMMENT '收益期',
  `channel_info` text COMMENT '渠道信息',
  `recharge_cost` text COMMENT '充值成本',
  `card_cost` text COMMENT '开卡成本',
  `remark` text COMMENT '备注',
  `remark_for_agent` text COMMENT '客户备注',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_vcc_referral_code` (`code`)
) ENGINE=InnoDB  COMMENT '推荐码表' ;



CREATE TABLE `vcc_referral_code_log` (
  `referral_code_id` bigint(50) NOT NULL COMMENT '推荐码id',
  `new_user_id` bigint(50) NOT NULL COMMENT '新用户id',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '推荐码记录表' ;



CREATE TABLE `vcc_referral_income` (
  `owner_id` bigint(50) NOT NULL COMMENT '所属用户id',
  `referral_user_id` bigint(50) NOT NULL COMMENT '推荐用户id',
  `referral_code_id` bigint(50) NOT NULL COMMENT '推荐码id',
  `use_flag` int(5) NOT NULL COMMENT '分类',
  `amount` decimal(50,2) NOT NULL COMMENT '金额',
  `income` decimal(50,2) NOT NULL COMMENT '收益',
  `currency` varchar(10) NOT NULL COMMENT '币种',
  `detail` text COMMENT '详情',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '代理收益表' ;



CREATE TABLE `vcc_regulation` (
  `user_id` bigint(50) NOT NULL COMMENT '用户id',
  `type` varchar(50) NOT NULL COMMENT '类型',
  `use_flag` int(50) NOT NULL COMMENT '分类',
  `amount` decimal(50,2) NOT NULL COMMENT '金额',
  `currency` varchar(10) DEFAULT NULL COMMENT '币种',
  `card_id` bigint(50) DEFAULT NULL COMMENT '卡id',
  `remark` varchar(200) NOT NULL COMMENT '备注',
  `request_date` datetime NOT NULL COMMENT '请求时间',
  `operation_user_id` bigint(50) NOT NULL COMMENT '系统用户id',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '调账表' ;



CREATE TABLE `vcc_reserve_card` (
  `card_id` bigint(100) NOT NULL COMMENT '卡id',
  `card_number` varchar(100) DEFAULT NULL COMMENT '加密卡号',
  `cvv` varchar(100) DEFAULT NULL COMMENT 'cvv',
  `channel` int(5) NOT NULL COMMENT '渠道',
  `channel_card_id` varchar(100) NOT NULL COMMENT '渠道卡id',
  `channel_account_id` varchar(200) DEFAULT NULL COMMENT '渠道账户id',
  `expiry` varchar(100) DEFAULT NULL COMMENT '过期时间',
  `order_id` varchar(100) NOT NULL COMMENT '清单id',
  `status` int(5) NOT NULL COMMENT '状态',
  `limit_config` varchar(255) DEFAULT NULL COMMENT '限额',
  `open_card_task_id` bigint(64) DEFAULT NULL COMMENT '开卡任务id',
  `balance` decimal(50,2) DEFAULT NULL COMMENT '卡余额',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_vcc_reserve_channel_card_id` (`channel_card_id`)
) ENGINE=InnoDB  COMMENT '储备卡表' ;



CREATE TABLE `vcc_tran_risk_rule` (
  `name` varchar(50) NOT NULL COMMENT '交易规则名',
  `rule_type` int(5) NOT NULL COMMENT '规则类型',
  `rule_config` text COMMENT '规则config',
  `is_default` int(1) NOT NULL COMMENT '是否默认',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '交易风控规则表' ;



CREATE TABLE `vcc_user` (
  `account` varchar(50) NOT NULL COMMENT '账号',
  `customer_id` bigint(50) DEFAULT NULL COMMENT 'crm和kun商户号',
  `acs_customer_id` varchar(255) DEFAULT NULL COMMENT 'acs商户号',
  `password` varchar(50) NOT NULL COMMENT '密码',
  `pay_password` varchar(50) NOT NULL COMMENT '交易密码',
  `pay_password_switch` int(1) DEFAULT NULL COMMENT '交易开关',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `source` int(11) NOT NULL COMMENT '注册来源',
  `status` int(11) NOT NULL COMMENT '状态',
  `role` int(11) NOT NULL COMMENT '角色',
  `is_new` int(1) DEFAULT NULL COMMENT '是否新用户',
  `kyc_type` int(11) DEFAULT NULL COMMENT 'kyc类型',
  `referral_code_id` bigint(50) DEFAULT NULL COMMENT '推荐码id',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_vcc_user_account` (`account`)
) ENGINE=InnoDB  COMMENT '用户表' ;


CREATE TABLE `vcc_user_card` (
  `card_number` varchar(100) NOT NULL COMMENT '卡号',
  `card_number_last_four` varchar(50) NOT NULL COMMENT '卡号后4位',
  `cvv` varchar(50) NOT NULL COMMENT 'cvv',
  `expiry` varchar(50) NOT NULL COMMENT '有效期',
  `request_date` datetime NOT NULL COMMENT '开卡时间',
  `order_id` varchar(50) NOT NULL COMMENT '订单号',
  `card_bin_id` bigint(50) NOT NULL COMMENT 'bin段id',
  `user_id` bigint(50) NOT NULL COMMENT '用户id',
  `customer_id` bigint(50) NOT NULL COMMENT '商户号',
  `channel_card_id` varchar(100) NOT NULL COMMENT '渠道卡id',
  `channel` int(5) NOT NULL COMMENT '渠道',
  `currency` varchar(10) NOT NULL COMMENT '币种',
  `card_type` varchar(50) NOT NULL COMMENT '卡类型',
  `usable_quota` decimal(50,2) NOT NULL COMMENT '卡余额',
  `usable_limit` decimal(50,2) NOT NULL COMMENT '卡限额',
  `remark` varchar(255) NOT NULL COMMENT '备注',
  `card_status` varchar(50) NOT NULL COMMENT '卡状态',
  `cardholder_id` bigint(50) DEFAULT NULL COMMENT '持卡人id',
  `is_share` int(1) NOT NULL COMMENT '是否共享卡',
  `limit_config` varchar(255) DEFAULT NULL COMMENT '卡限额配置',
  `license_id` bigint(50) DEFAULT NULL COMMENT '所属license',
  `apply_fee` decimal(10,2) NOT NULL COMMENT '开卡费',
  `open_card_task_id` bigint(64) DEFAULT NULL COMMENT '开卡任务id',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_vcc_card_card_number` (`card_number`),
  UNIQUE KEY `uniq_vcc_card_channel_card_id` (`channel_card_id`),
  KEY `idx_vcc_card_card_number_last_four` (`card_number_last_four`)
) ENGINE=InnoDB  COMMENT '用户卡片表' ;



CREATE TABLE `vcc_user_notice` (
  `user_id` bigint(32) NOT NULL COMMENT '用户id',
  `notice_id` bigint(32) NOT NULL COMMENT '消息id',
  `is_read` int(1) NOT NULL COMMENT '是否已读',
  `type` int(5) NOT NULL COMMENT '类型',
  `title` varchar(250) NOT NULL COMMENT '标题',
  `content` text COMMENT '内容',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '用户消息表' ;



CREATE TABLE `vcc_user_rate` (
  `user_id` bigint(50) NOT NULL COMMENT '用户id',
  `rate_template_id` bigint(64) NOT NULL COMMENT '模板id',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '用户费率模板关联表' ;



CREATE TABLE `vcc_user_tran_risk_log` (
  `user_id` bigint(50) NOT NULL COMMENT '用户id',
  `trigger_type` int(5) NOT NULL COMMENT '触发时机',
  `card_number` varchar(100) DEFAULT NULL COMMENT '加密卡号',
  `tran_id` bigint(50) DEFAULT NULL COMMENT '交易id',
  `rule_id` bigint(50) DEFAULT NULL COMMENT '风控规则id',
  `rule_type` int(5) DEFAULT NULL COMMENT '风控类型',
  `risk_level` int(5) NOT NULL COMMENT '风控执行等级',
  `status` int(5) NOT NULL COMMENT '状态',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '用户交易风控表' ;



CREATE TABLE `vcc_user_tran_risk_rule` (
  `user_id` bigint(50) NOT NULL COMMENT '用户id',
  `tran_rule_id` bigint(50) DEFAULT NULL COMMENT '交易规则id',
  `card_rule_id` bigint(50) DEFAULT NULL COMMENT '卡规则id',
  `account_rule_id` bigint(50) DEFAULT NULL COMMENT '负资金规则id',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT '用户交易风控映射表' ;



CREATE TABLE `vcc_wallet` (
  `user_id` bigint(50) NOT NULL COMMENT '用户id',
  `customer_id` bigint(50) NOT NULL COMMENT '商户号',
  `wallet_id` varchar(50) DEFAULT NULL COMMENT '渠道钱包id',
  `is_share` int(1) NOT NULL COMMENT '是否共享钱包',
  `status` int(11) NOT NULL COMMENT '状态',
  `type` int(11) NOT NULL COMMENT '类型',
  `source` int(11) NOT NULL COMMENT '来源',
  `currency` varchar(10) NOT NULL COMMENT '币种',
  `usable_quota` decimal(50,2) NOT NULL COMMENT '可用额度',
  `overflow_quota` decimal(50,2) NOT NULL COMMENT '超支额度',
  `version` bigint(50) NOT NULL COMMENT '版本号',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_vcc_wallet_user_id` (`user_id`),
  KEY `idx_vcc_wallet_customer_id` (`customer_id`),
  KEY `idx_vcc_wallet_currency_code` (`currency`)
) ENGINE=InnoDB  COMMENT '账户表' ;



CREATE TABLE `vcc_wallet_detail` (
  `order_id` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` bigint(50) NOT NULL COMMENT '用户id',
  `wallet_id` bigint(50) DEFAULT NULL COMMENT '钱包id',
  `card_number` varchar(100) DEFAULT NULL COMMENT '加密卡号',
  `use_flag` int(11) NOT NULL COMMENT '分类',
  `status` int(11) NOT NULL COMMENT '状态',
  `sync_status` int(11) NOT NULL COMMENT '同步状态',
  `amount` decimal(50,2) NOT NULL COMMENT '交易金额',
  `wallet_amount` decimal(50,2) DEFAULT NULL COMMENT '瞬时钱包余额',
  `currency` varchar(10) DEFAULT NULL COMMENT '交易币种',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `request_date` datetime NOT NULL COMMENT '请求时间',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_vcc_wallet_detail_order_id` (`order_id`),
  KEY `idx_vcc_wallet_detail_card_number` (`card_number`)
) ENGINE=InnoDB  COMMENT '账户动账明细表' ;



CREATE TABLE `vcc_webhook_message` (
  `license_id` bigint(50) NOT NULL COMMENT 'licenseid',
  `message_id` varchar(50) NOT NULL COMMENT '消息id',
  `msg_type` int(5) NOT NULL COMMENT '消息类型',
  `push_count` int(3) NOT NULL COMMENT '推送次数',
  `status` int(3) NOT NULL COMMENT '推送状态',
  `last_code` int(3) NOT NULL COMMENT '上一次推送时的错误码',
  `next_push_date` datetime NOT NULL COMMENT '下一次推送的时间',
  `content` text COMMENT '内容',
  `id` bigint(64) NOT NULL COMMENT 'id', 
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT 'webhook表' ;

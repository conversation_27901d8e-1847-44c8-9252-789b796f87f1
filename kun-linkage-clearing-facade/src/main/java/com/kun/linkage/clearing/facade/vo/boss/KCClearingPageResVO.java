package com.kun.linkage.clearing.facade.vo.boss;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

public class KCClearingPageResVO implements Serializable {
    
    private static final long serialVersionUID = 1L;


    /**
     * 文件唯一标识
     */
    @Schema(description ="主键id")
    private String id;

    /**
     * 通道来源;PBC-GW
     */
    @Schema(description ="通道来源;PBC-GW")
    private String channelSource;

    /**
     * 原文件名
     */
    @Schema(description ="原文件名")
    private String originalFilename;

    /**
     * 原始文件名
     */
    @Schema(description ="file_name")
    private String fileName;

    /**
     * 文件类型（如PDF、JPG）
     */
    @Schema(description ="文件类型（如PDF、JPG）")
    private String fileType;

    /**
     * 文件存储路径
     */
    @Schema(description ="文件存储路径")
    private String storagePath;

    /**
     * 文件日期
     */
    @Schema(description ="文件日期")
    private LocalDate fileTime;

    /**
     * 下载日期
     */
    @Schema(description ="下载日期")
    private Date downloadTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getChannelSource() {
        return channelSource;
    }

    public void setChannelSource(String channelSource) {
        this.channelSource = channelSource;
    }

    public String getOriginalFilename() {
        return originalFilename;
    }

    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getStoragePath() {
        return storagePath;
    }

    public void setStoragePath(String storagePath) {
        this.storagePath = storagePath;
    }

    public LocalDate getFileTime() {
        return fileTime;
    }

    public void setFileTime(LocalDate fileTime) {
        this.fileTime = fileTime;
    }

    public Date getDownloadTime() {
        return downloadTime;
    }

    public void setDownloadTime(Date downloadTime) {
        this.downloadTime = downloadTime;
    }
}

package com.kun.linkage.auth.facade.vo.webhook;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(
    description = "Represents the result of a callback operation, including transaction details and response status.")
public class WebHookResult<T> implements Serializable {

    @Schema(description = "Unique identifier for the transaction", example = "1234567890")
    private String requestNo;

    @Schema(description = "Response code indicating the status of the request", example = "0000")
    private String code;

    @Schema(description = "Message providing additional information about the response",
        example = "Request processed successfully")
    private String message;

    private T data;

    public WebHookResult(String requestNo, String code, String message) {
        this.requestNo = requestNo;
        this.code = code;
        this.message = message;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}

package com.kun.linkage.auth.facade.vo.webhook;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.UUID;

@Schema(
    description = "Base class for callback request objects, providing a common structure for all callback requests.")
public class BaseWebHookRequestVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "Unique identifier for the request", example = "1234567890")
    private String requestNo = UUID.randomUUID().toString();

    @Schema(description = "Unique identifier for the partner", example = "ORG123456")
    private String organizationNo;

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
}

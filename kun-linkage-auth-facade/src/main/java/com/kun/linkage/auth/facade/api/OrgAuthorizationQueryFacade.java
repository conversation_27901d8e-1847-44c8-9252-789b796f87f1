package com.kun.linkage.auth.facade.api;

import com.kun.linkage.auth.facade.vo.boss.AuthorizationInquiryPageVO;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationInquiryReuqestVO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.config.FeignConfiguration;
import com.kun.linkage.common.base.page.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name = "kun-linkage-auth", path = "/linkage-auth/org/authorization", configuration = FeignConfiguration.class)
public interface OrgAuthorizationQueryFacade {
    /**
     * 分页查询授权记录
     *
     * @param requestVO
     * @return
     */
    @Operation(description = "分页查询授权记录", summary = "分页查询授权记录")
    @RequestMapping(value = "/pageList", method = RequestMethod.POST)
    Result<PageResult<AuthorizationInquiryPageVO>> pageList(@RequestBody AuthorizationInquiryReuqestVO requestVO);
}

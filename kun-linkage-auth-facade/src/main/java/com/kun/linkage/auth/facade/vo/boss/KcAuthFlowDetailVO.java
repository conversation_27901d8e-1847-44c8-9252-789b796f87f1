package com.kun.linkage.auth.facade.vo.boss;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * title: <br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * @date 2025/7/9 16:11
 */
@Setter
@Getter
public class KcAuthFlowDetailVO {
    //业务系统
    @Schema(description = "业务系统")
    private String systemMark;
    //系统授权流水号
    @Schema(description = "系统授权流水号")
    private String requestId;
    //原系统授权流水号
    @Schema(description = "原系统授权流水号")
    private String originalRequestId;
    //授权日期时间
    @Schema(description = "授权日期时间")
    private Date authStartTime;
    //授权完成日期时间
    @Schema(description = "授权完成日期时间")
    private Date authCompletionTime;
    //商户号
    @Schema(description = "商户号")
    private String merchantId;
    //商户名称
    @Schema(description = "商户名称")
    private String merchantName;
    //CardId
    @Schema(description = "CardId")
    private String cardId;
    //通道
    @Schema(description = "通道")
    private String channel;
    //通道流水号
    @Schema(description = "通道流水号")
    private String transId;
    //原交易通道流水号
    @Schema(description = "原交易通道流水号")
    private String originalTransId;
    //参考号
    @Schema(description = "参考号")
    private String referenceNo;
    //授权码
    @Schema(description = "授权码")
    private String approveCode;
    //收单商户名称
    @Schema(description = "收单商户名称")
    private String cardAcceptorName;
    //收单商户号
    @Schema(description = "收单商户号")
    private String cardAcceptorIdentificationCode;
    //收单商户终端号
    @Schema(description = "收单商户终端号")
    private String cardAcceptorTerminalIdentification;
    //系统跟踪号
    @Schema(description = "系统跟踪号")
    private String systemsTraceAuditNumber;
    //MTI
    @Schema(description = "MTI")
    private String messageType;
    //Processing Code
    @Schema(description = "Processing Code")
    private String processingCode;
    //SVFE交易类型
    @Schema(description = "SVFE交易类型")
    private String svfeTransactionType;
    //响应码
    @Schema(description = "响应码")
    private String returnCode;
    //MCC Code
    @Schema(description = "MCC Code")
    private String merchantType;
    //授权类型
    @Schema(description = "授权类型")
    private String authType;
    //授权状态
    @Schema(description = "授权状态")
    private Integer authorizationDecision;
    //交易币种
    @Schema(description = "交易币种")
    private String transCcyCode;
    //交易金额
    @Schema(description = "交易金额")
    private BigDecimal transAmt;
    //持卡人币种
    @Schema(description = "持卡人币种")
    private String billCcyCode;
    //持卡人金额
    @Schema(description = "持卡人金额")
    private BigDecimal billAmount;
    //Markup比例
    @Schema(description = "Markup比例")
    private BigDecimal markupRate;
    //持卡人金额（with Markup）
    @Schema(description = "持卡人金额（with Markup）")
    private BigDecimal billAmountWithMarkup;
    //汇率
    @Schema(description = "汇率")
    private String conversionRateCardholderBilling;
    //剩余交易授权金额
    @Schema(description = "剩余交易授权金额")
    private BigDecimal remainAuthAmt;
    //剩余持卡人金额
    @Schema(description = "剩余持卡人金额")
    private BigDecimal remainBillAmt;
    //剩余持卡人金额（with Markup）
    @Schema(description = "剩余持卡人金额（with Markup）")
    private BigDecimal remainFrozenAmt;

}

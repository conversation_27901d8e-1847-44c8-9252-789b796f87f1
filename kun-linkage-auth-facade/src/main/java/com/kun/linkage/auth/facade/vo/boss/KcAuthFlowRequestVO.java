package com.kun.linkage.auth.facade.vo.boss;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.page.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * title: <br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * @date 2025/7/9 16:07
 */
@Getter
@Setter
public class KcAuthFlowRequestVO extends PageParam {
    //授权日期
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Schema(description = "授权日期 最小")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date authStartTime;

    @Schema(description = "授权日期 最大")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private Date authEndTime;
    //授权交易金额
    @Schema(description = "授权交易金额 最小")
    private BigDecimal transAmtMin;
    @Schema(description = "授权交易金额 最大")
    private BigDecimal transAmtMax;
    //商户号
    @Schema(description = "商户号")
    private String merchantId;
    //系统授权号
    @Schema(description = "系统授权号")
    private String transId;
    //授权类型
    @Schema(description = "授权类型")
    private String authType;
    //原系统授权号
    @Schema(description = "原系统授权号")
    private String originalTransId;
    //收单商户MCC
    @Schema(description = "收单商户MCC")
    private String merchantType;
    //参考号
    @Schema(description = "参考号")
    private String referenceNo;
    //cardId
    @Schema(description = "cardId")
    private String cardId;
    //授权状态
    @Schema(description = "授权状态")
    private Integer authorizationDecision;
    //系统跟踪号
    @Schema(description = "系统跟踪号")
    private String systemsTraceAuditNumber;
    //通道
    @Schema(description = "通道")
    private String channel;
    //相应码
    @Schema(description = "响应码")
    private String returnCode;
    //授权码
    @Schema(description = "授权码")
    private String approveCode;
}

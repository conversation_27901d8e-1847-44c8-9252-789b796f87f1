package com.kun.linkage.auth.facade.vo.webhook;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;

@Schema(
    description = "AuthWebHookRequestVO, 转授权请求数据")
public class PartnerAuthWebHookRequestVO extends BaseWebHookRequestVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String transactionId;

    private String cardId;

    private String customerId;

    private String transactionDirection;

    /**
     * 交易类型, @see com.kun.linkage.auth.facade.constant.TransactionTypeEnum
     */
    private String transactionType;

    private String transactionCurrency;

    private BigDecimal transactionAmount;

    private String authStatus;

    private String cardholderCurrency;

    private BigDecimal cardholderAmount;

    private String requestCurrency;

    private BigDecimal requestAmount;

    private String servicePointCardCode;

    private String servicePointPinCode;

    private String servicePointConditionCode;

    @Schema(description = "The date and time when the transaction was transmitted", example = "20250601120000",
        format = "YYYYMMDDHHmmSS")
    private String transmissionDateTime;

    private String referenceNumber;

    private String approvalCode;

    private String cardExpiryDate;

    private String merchantType;

    private String cardAcceptorTerminalCode;

    private String cardAcceptorIdentification;

    private String cardAcceptorNameLocation;

    private String networkReferenceId;

    @Schema(description = "The date and time when the original transaction was transmitted", example = "20250601120000",
        format = "YYYYMMDDHHmmSS")
    private String originalTransmissionDateTime;

    private String originalTransactionId;

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getTransactionDirection() {
        return transactionDirection;
    }

    public void setTransactionDirection(String transactionDirection) {
        this.transactionDirection = transactionDirection;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getTransactionCurrency() {
        return transactionCurrency;
    }

    public void setTransactionCurrency(String transactionCurrency) {
        this.transactionCurrency = transactionCurrency;
    }

    public BigDecimal getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(BigDecimal transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public String getAuthStatus() {
        return authStatus;
    }

    public void setAuthStatus(String authStatus) {
        this.authStatus = authStatus;
    }

    public String getCardholderCurrency() {
        return cardholderCurrency;
    }

    public void setCardholderCurrency(String cardholderCurrency) {
        this.cardholderCurrency = cardholderCurrency;
    }

    public BigDecimal getCardholderAmount() {
        return cardholderAmount;
    }

    public void setCardholderAmount(BigDecimal cardholderAmount) {
        this.cardholderAmount = cardholderAmount;
    }

    public String getRequestCurrency() {
        return requestCurrency;
    }

    public void setRequestCurrency(String requestCurrency) {
        this.requestCurrency = requestCurrency;
    }

    public BigDecimal getRequestAmount() {
        return requestAmount;
    }

    public void setRequestAmount(BigDecimal requestAmount) {
        this.requestAmount = requestAmount;
    }

    public String getServicePointCardCode() {
        return servicePointCardCode;
    }

    public void setServicePointCardCode(String servicePointCardCode) {
        this.servicePointCardCode = servicePointCardCode;
    }

    public String getServicePointPinCode() {
        return servicePointPinCode;
    }

    public void setServicePointPinCode(String servicePointPinCode) {
        this.servicePointPinCode = servicePointPinCode;
    }

    public String getServicePointConditionCode() {
        return servicePointConditionCode;
    }

    public void setServicePointConditionCode(String servicePointConditionCode) {
        this.servicePointConditionCode = servicePointConditionCode;
    }

    public String getTransmissionDateTime() {
        return transmissionDateTime;
    }

    public void setTransmissionDateTime(String transmissionDateTime) {
        this.transmissionDateTime = transmissionDateTime;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getApprovalCode() {
        return approvalCode;
    }

    public void setApprovalCode(String approvalCode) {
        this.approvalCode = approvalCode;
    }

    public String getCardExpiryDate() {
        return cardExpiryDate;
    }

    public void setCardExpiryDate(String cardExpiryDate) {
        this.cardExpiryDate = cardExpiryDate;
    }

    public String getMerchantType() {
        return merchantType;
    }

    public void setMerchantType(String merchantType) {
        this.merchantType = merchantType;
    }

    public String getCardAcceptorTerminalCode() {
        return cardAcceptorTerminalCode;
    }

    public void setCardAcceptorTerminalCode(String cardAcceptorTerminalCode) {
        this.cardAcceptorTerminalCode = cardAcceptorTerminalCode;
    }

    public String getCardAcceptorIdentification() {
        return cardAcceptorIdentification;
    }

    public void setCardAcceptorIdentification(String cardAcceptorIdentification) {
        this.cardAcceptorIdentification = cardAcceptorIdentification;
    }

    public String getCardAcceptorNameLocation() {
        return cardAcceptorNameLocation;
    }

    public void setCardAcceptorNameLocation(String cardAcceptorNameLocation) {
        this.cardAcceptorNameLocation = cardAcceptorNameLocation;
    }

    public String getNetworkReferenceId() {
        return networkReferenceId;
    }

    public void setNetworkReferenceId(String networkReferenceId) {
        this.networkReferenceId = networkReferenceId;
    }

    public String getOriginalTransmissionDateTime() {
        return originalTransmissionDateTime;
    }

    public void setOriginalTransmissionDateTime(String originalTransmissionDateTime) {
        this.originalTransmissionDateTime = originalTransmissionDateTime;
    }

    public String getOriginalTransactionId() {
        return originalTransactionId;
    }

    public void setOriginalTransactionId(String originalTransactionId) {
        this.originalTransactionId = originalTransactionId;
    }
}

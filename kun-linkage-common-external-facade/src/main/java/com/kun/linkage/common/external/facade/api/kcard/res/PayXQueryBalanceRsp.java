package com.kun.linkage.common.external.facade.api.kcard.res;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * title: <br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * @date 2025/7/4 11:08
 */
@Getter
@Setter
public class PayXQueryBalanceRsp {

    //客户号
    private String customerNo;

    //底层账户编号
    private String accountNo;

    //币种
    private String currency;

    //对应通道
    private String accountChannel;

    //余额
    private BigDecimal balance;

    //账户状态
    private String accountStatus;
}

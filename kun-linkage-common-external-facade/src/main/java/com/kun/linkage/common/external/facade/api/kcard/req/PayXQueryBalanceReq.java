package com.kun.linkage.common.external.facade.api.kcard.req;

import lombok.Getter;
import lombok.Setter;

/**
 * title: <br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * @date 2025/7/4 11:07
 */
@Getter
@Setter
public class PayXQueryBalanceReq extends KunAndPayXAccountBaseVo {

    /**
     * 币种（AUD,CAD,EUR,JPY,NZD,NOK,GBP,SEK,CHF,USD,SGD,HKD,CNH,PHP,VND）
     */
    private String currency;
}

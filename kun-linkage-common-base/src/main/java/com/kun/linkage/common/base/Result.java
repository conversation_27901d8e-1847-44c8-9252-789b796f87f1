package com.kun.linkage.common.base;

import com.kun.linkage.common.base.constants.CommonTipConstant;

import java.beans.Transient;
import java.io.Serializable;

/**
 * 通用响应类(业务代码中只用填响应码,会统一处理响应信息)
 *
 * @param <T>
 */
public class Result<T> implements Serializable {
    private static final long serialVersionUID = -1;
    /*** 响应码 */
    private String code;
    /*** 响应信息 */
    private String message;
    /*** 数据信息 */
    private T data;

    public Result() {
    }

    public static <T> Result<T> success() {
        return new Result<>(CommonTipConstant.SUCCESS, "success");
    }

    public static <T> Result<T> success(T data) {
        return new Result<>(CommonTipConstant.SUCCESS, "success", data);
    }

    public static <T> Result<T> success(String message, T data) {
        return new Result<>(CommonTipConstant.SUCCESS, message, data);
    }

    public static <T> Result<T> fail(String code) {
        return new Result<>(code, null);
    }

    public static <T> Result<T> fail(String code, T date) {
        return new Result<>(code, null, date);
    }


    public static <T> Result<T> fail(String code, String message) {
        return new Result<>(code, message);
    }

    public static <T> Result<T> fail(String code, String message, T date) {
        return new Result<>(code, message,date);
    }

    public static <T> Result<T> unknown() {
        return new Result<>(CommonTipConstant.UNKNOW_ERROR, "unknow error");
    }

    public static <T> Result<T> unknown(String message) {
        return new Result<>(CommonTipConstant.UNKNOW_ERROR, message);
    }

    public static Boolean isSuccess(Result<?> result) {
        if(null == result || null == result.getCode()) {
            return false;
        }
        return result.getCode().equals(CommonTipConstant.SUCCESS);
    }

    public Result(String code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public Result(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Transient
    public boolean isSuccess() {
        return CommonTipConstant.SUCCESS.equals(this.code);
    }

    @Override
    public String toString() {
        return "Result{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}

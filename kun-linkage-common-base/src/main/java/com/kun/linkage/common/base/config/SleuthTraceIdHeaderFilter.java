package com.kun.linkage.common.base.config;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.*;
import javax.servlet.http.*;
import java.io.IOException;

@Component
public class SleuthTraceIdHeaderFilter extends OncePerRequestFilter {
    @Override
    protected void doFilterInternal(HttpServletRequest request,
        HttpServletResponse response,
        FilterChain filterChain)
        throws ServletException, IOException {


        // 从 MDC 中获取 traceId，并写入响应头
        String traceId = MDC.get("traceId");
        if (traceId != null) {
            response.setHeader("X-B3-TraceId", traceId);
            response.setHeader("logtraceid", traceId);
        }
        filterChain.doFilter(request, response);
    }
}


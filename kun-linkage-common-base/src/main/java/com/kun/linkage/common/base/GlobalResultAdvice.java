package com.kun.linkage.common.base;

import com.kun.linkage.common.base.exception.GlobalExceptionHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestControllerAdvice
public class GlobalResultAdvice implements ResponseBodyAdvice<Object> {
    @Resource
    private MessageSource messageSource;

    @Resource
    private HttpServletRequest request;

    /**
     * 前缀路径列表，用于匹配请求路径
     */
    @Value("${spring.mvc.servlet.path:''}")
    private String basePrefix;

    /**
     * 忽略的路径列表
     * 例如：/static/, /public/, /favicon.ico, /actuator, /druid
     */
    @Value("${global.result.advice.ignore-paths:}")
    private List<String> ignorePaths;

    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        String requestURI = request.getRequestURI();
        if (ignorePaths != null && !ignorePaths.isEmpty()) {
            for (String ignorePath : ignorePaths) {
                if (requestURI.startsWith(ignorePath)) {
                    return false;
                }
            }
        }
        // 移除MVC前缀
        if (basePrefix != null && !basePrefix.isEmpty() && requestURI.startsWith(basePrefix)) {
            requestURI = requestURI.substring(basePrefix.length());
        }
        // 忽略静态资源
        if (requestURI.startsWith("/static/") || requestURI.startsWith("/public/") || requestURI.startsWith("/favicon.ico")) {
            return false;
        }
        // 忽略不需要包装的请求
        if (requestURI.startsWith("/actuator") || requestURI.startsWith("/druid")) {
            return false;
        }
        if (ignorePaths != null && !ignorePaths.isEmpty()) {
            for (String ignorePath : ignorePaths) {
                if (requestURI.startsWith(ignorePath)) {
                    return false;
                }
            }
        }
        // 这里可以根据实际情况调整忽略的返回类型
        // 如果返回类型是 Result 或者 GlobalExceptionHandler 中的方法，则不进行包装
/*        if (returnType.getParameterType().equals(Result.class)||(returnType.getMethod() != null
            && !returnType.getMethod().getDeclaringClass().equals(GlobalExceptionHandler.class))) {
            return true;
        } else {
            return false;
        }*/
        return Result.class.isAssignableFrom(returnType.getParameterType())
                // 跳过被异常处理器处理过的
                && returnType.getMethod() != null
                && !returnType.getMethod().getDeclaringClass().equals(GlobalExceptionHandler.class);
    }

    @Override
    public Result<?> beforeBodyWrite(Object body,
                                  MethodParameter returnType,
                                  MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request,
                                  ServerHttpResponse response) {
        if (body == null) {
            return null;
        }
        Result<?> result = ((Result<?>) body);
        // 获取国际化错误信息
        if (StringUtils.isNotBlank(result.getMessage()) && !result.getMessage().equals(result.getCode())) {
            // 如果消息已经是国际化的，则不再处理
            return result;
        }
        result.setMessage(messageSource.getMessage(result.getCode(), null, result.getCode(), LocaleContextHolder.getLocale()));
        return result;
    }
}
